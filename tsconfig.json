{"include": ["src"], "exclude": ["node_modules", "dist", "**/*.spec.ts"], "compilerOptions": {"module": "commonjs", "declaration": false, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": false, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@app": ["src/app.module"], "@modules/*": ["src/modules/*"], "@common/*": ["src/common/*"], "@shared/*": ["src/shared/*"], "@config": ["src/config"], "@config/*": ["src/config/*"], "@utils/*": ["src/shared/utils/*"], "@database/*": ["src/shared/database/*"], "@dto/*": ["src/common/dto/*"], "@filters/*": ["src/common/filters/*"], "@guards/*": ["src/common/guards/*"], "@interceptors/*": ["src/common/interceptors/*"], "@pipes/*": ["src/common/pipes/*"], "@entities/*": ["src/common/entities/*"]}}}