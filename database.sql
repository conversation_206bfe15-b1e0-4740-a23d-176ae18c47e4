create table postgres_log
(
    log_time               timestamp(3) with time zone,
    user_name              text,
    database_name          text,
    process_id             integer,
    connection_from        text,
    session_id             text   not null,
    session_line_num       bigint not null,
    command_tag            text,
    session_start_time     timestamp with time zone,
    virtual_transaction_id text,
    transaction_id         bigint,
    error_severity         text,
    sql_state_code         text,
    message                text,
    detail                 text,
    hint                   text,
    internal_query         text,
    internal_query_pos     integer,
    context                text,
    query                  text,
    query_pos              integer,
    location               text,
    application_name       text,
    backend_type           text,
    leader_pid             integer,
    query_id               bigint,
    constraint postgres_log_check
        check (false) no inherit
);

alter table postgres_log
    owner to postgres;

grant select on postgres_log to admin;

grant delete, insert, select, update on postgres_log to member;

create table coupons
(
    id                  uuid               not null
        primary key,
    code                varchar(50)        not null
        unique,
    description         text,
    discount_type       discount_type_enum not null,
    discount_value      double precision   not null,
    min_order_value     double precision   default 0,
    max_discount_amount double precision,
    start_date          bigint             not null,
    end_date            bigint             not null,
    usage_limit         integer,
    per_user_limit      integer            default 1,
    status              coupon_status_enum default 'ACTIVE'::coupon_status_enum,
    created_at          bigint             not null,
    updated_at          bigint             not null
);

comment on table coupons is '<PERSON>ảng quản lý thông tin mã giảm giá (coupons) trong hệ thống';

comment on column coupons.id is 'Định danh mã giảm giá (UUID)';

comment on column coupons.code is 'Mã giảm giá do hệ thống hoặc admin tạo';

comment on column coupons.description is 'Mô tả chi tiết về mã giảm giá';

comment on column coupons.discount_type is 'Loại giảm giá: phần trăm hoặc số tiền cố định';

comment on column coupons.discount_value is 'Giá trị giảm giá tương ứng với loại (%, số tiền)';

comment on column coupons.min_order_value is 'Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá';

comment on column coupons.max_discount_amount is 'Giảm giá tối đa cho mã giảm giá loại phần trăm';

comment on column coupons.start_date is 'Thời điểm bắt đầu áp dụng mã (Unix timestamp)';

comment on column coupons.end_date is 'Thời điểm kết thúc áp dụng mã (Unix timestamp)';

comment on column coupons.usage_limit is 'Tổng số lần sử dụng tối đa cho toàn bộ hệ thống';

comment on column coupons.per_user_limit is 'Số lần một người dùng được sử dụng mã này';

comment on column coupons.status is 'Trạng thái mã giảm giá: ACTIVE, INACTIVE, hoặc EXPIRED';

comment on column coupons.created_at is 'Thời gian tạo mã (Unix timestamp)';

comment on column coupons.updated_at is 'Thời gian cập nhật mã gần nhất (Unix timestamp)';

alter table coupons
    owner to root;

grant delete, insert, select, update on coupons to member;

create table webhook_logs
(
    id               uuid                  not null
        primary key,
    transaction_id   bigserial,
    transaction_type transaction_type_enum not null,
    source           varchar(50)           not null,
    payload          jsonb                 not null,
    status           webhook_status_enum default 'PROCESSING'::webhook_status_enum,
    response_code    varchar(20),
    response_message text,
    processed_at     bigint,
    created_at       bigint                not null
);

comment on column webhook_logs.id is 'Định danh log webhook';

comment on column webhook_logs.transaction_id is 'Định danh giao dịch';

comment on column webhook_logs.transaction_type is 'Loại giao dịch (POINT_PURCHASE, POINT_WITHDRAWAL)';

comment on column webhook_logs.source is 'Nguồn webhook';

comment on column webhook_logs.payload is 'Dữ liệu webhook';

comment on column webhook_logs.status is 'Trạng thái xử lý webhook (SUCCESS, FAILED, PROCESSING)';

comment on column webhook_logs.response_code is 'Mã phản hồi';

comment on column webhook_logs.response_message is 'Thông điệp phản hồi';

comment on column webhook_logs.processed_at is 'Thời gian xử lý (Unix timestamp)';

comment on column webhook_logs.created_at is 'Thời gian tạo (Unix timestamp)';

alter table webhook_logs
    owner to root;

grant select, update, usage on sequence webhook_logs_transaction_id_seq to member;

grant delete, insert, select, update on webhook_logs to member;

create table points
(
    id           serial
        constraint points_pk
            primary key,
    name         varchar(255),
    cash         double precision,
    rate         double precision,
    min          double precision,
    max          double precision,
    point        bigint,
    is_customize boolean default false not null,
    description  varchar(255)
);

comment on column points.name is 'tên của gói rpoint';

comment on column points.cash is 'Số tiền của gói point không phải customize';

comment on column points.rate is 'Tỷ lệ rate';

comment on column points.min is 'Nếu là customize thì có quy định min';

comment on column points.max is 'Nếu là customize thì có quy định số tiền max';

comment on column points.point is 'Số point';

comment on column points.is_customize is 'Có phải gói customize không';

comment on column points.description is 'Mô tả';

alter table points
    owner to root;

grant select, update, usage on sequence points_id_seq to member;

grant delete, insert, select, update on points to member;

create table banks
(
    bank_code varchar(20) not null
        constraint banks_pk
            primary key,
    bank_name varchar(255),
    logo_path varchar(255),
    full_name varchar(255),
    icon_path varchar(255),
    bin       varchar(20)
);

comment on table banks is 'Danh sách ngân hàng';

comment on column banks.bank_code is 'Mã code';

comment on column banks.bank_name is 'Tên ngân hàng';

comment on column banks.logo_path is 'path logo';

comment on column banks.full_name is 'Tên đầy đủ của ngân hàng';

comment on column banks.icon_path is 'path icon';

alter table banks
    owner to root;

grant delete, insert, select, update on banks to member;

create table affiliate_withdraw_history
(
    id                       bigint             not null
        constraint affiliate_withdraw_history_pk
            primary key,
    type                     contract_type_enum not null,
    affiliate_account_id     integer            not null,
    amount                   double precision   not null,
    balance_before           double precision,
    balance_after            double precision,
    bank_code                varchar(20)        not null,
    account_number           varchar(50)        not null,
    account_name             varchar(255)       not null,
    status                   varchar(20)        not null,
    created_at               bigint             not null,
    finish_at                bigint,
    vat_amount               double precision   not null,
    net_payment              double precision   not null,
    reject_reason            varchar(2000),
    purchase_invoice         varchar(255),
    processed_by_employee_id integer
);

comment on table affiliate_withdraw_history is 'Danh sách yêu cầu rút tiền của người dùng';

comment on column affiliate_withdraw_history.affiliate_account_id is 'Mã tài khoản affiliate';

comment on column affiliate_withdraw_history.amount is 'Số tiền rút';

comment on column affiliate_withdraw_history.balance_before is 'Số dư trước giao dịch';

comment on column affiliate_withdraw_history.balance_after is 'Số dư sau giao dịch';

comment on column affiliate_withdraw_history.bank_code is 'Mã ngân hàng';

comment on column affiliate_withdraw_history.account_number is 'Số tài khoản ngân hàng';

comment on column affiliate_withdraw_history.account_name is 'Tên tài khoản ngân hàng';

comment on column affiliate_withdraw_history.status is 'Trạng thái yêu cầu (''PENDING'', ''INVOICE_NOT_UPLOADED'', ''REJECTED'', ''PAID'')';

comment on column affiliate_withdraw_history.created_at is 'Thời gian tạo yêu cầu';

comment on column affiliate_withdraw_history.finish_at is 'Thời gian kết thúc yêu cầu';

comment on column affiliate_withdraw_history.vat_amount is 'Tiền thuế VAT';

comment on column affiliate_withdraw_history.net_payment is 'Tiền thực nhận';

comment on column affiliate_withdraw_history.reject_reason is 'Lý do từ chối';

comment on column affiliate_withdraw_history.purchase_invoice is 'Đường dẫn hóa đơn đầu vào';

comment on column affiliate_withdraw_history.processed_by_employee_id is 'Id nhân viên xử lý yêu cầu rút tiền  ';

alter table affiliate_withdraw_history
    owner to root;

grant delete, insert, select, update on affiliate_withdraw_history to member;

create table employees
(
    id           serial
        constraint employees_pk
            primary key,
    full_name    varchar(255)         not null,
    email        varchar(100)         not null
        constraint employees_pk_2
            unique,
    phone_number varchar(20)          not null
        constraint employees_pk_3
            unique,
    password     varchar(1000)        not null,
    address      varchar(2000)        not null,
    created_at   bigint               not null,
    updated_at   bigint,
    enable       boolean default true not null,
    avatar       varchar(255)
);

comment on table employees is 'Bảng lưu trữ thông tin nhân viên';

comment on column employees.id is 'Mã định danh duy nhất cho mỗi nhân viên, tự động tăng';

comment on column employees.email is 'Email nhân viên';

alter table employees
    owner to root;

grant select, update, usage on sequence employees_id_seq to member;

grant delete, insert, select, update on employees to member;

create table departments
(
    id         integer not null
        constraint departments_pk
            primary key,
    name       varchar(255),
    created_at bigint,
    update_at  bigint
);

comment on table departments is 'Phòng ban';

comment on column departments.name is 'Tên phòng ban';

comment on column departments.created_at is 'Thời gian tạo';

comment on column departments.update_at is 'Thời gian cập nhật';

alter table departments
    owner to root;

grant delete, insert, select, update on departments to member;

create table employee_roles
(
    id          integer      not null
        constraint employee_roles_pk
            primary key,
    name        varchar(255) not null,
    description varchar(500) not null
);

comment on table employee_roles is 'Danh sách role của nhân viên';

comment on column employee_roles.name is 'Tên role';

comment on column employee_roles.description is 'Mô tả';

alter table employee_roles
    owner to root;

grant delete, insert, select, update on employee_roles to member;

create table employee_has_roles
(
    employee_id integer not null
        constraint employee_has_roles_employees_id_fk
            references employees,
    role_id     integer not null
        constraint employee_has_roles_roles_id_fk
            references employee_roles,
    constraint employee_has_roles_pk
        primary key (employee_id, role_id)
);

alter table employee_has_roles
    owner to root;

grant delete, insert, select, update on employee_has_roles to member;

create table permissions
(
    id          integer default nextval('api_mapping_id_seq'::regclass) not null
        constraint permissions_pk_2
            primary key,
    action      varchar(255),
    description text,
    created_at  bigint                                                  not null,
    updated_at  bigint,
    module      varchar(50),
    constraint permissions_pk
        unique (action, module)
);

comment on column permissions.action is 'Hành động';

comment on column permissions.description is 'Mô tả quyền';

comment on column permissions.created_at is 'Thời gian tạo';

comment on column permissions.updated_at is 'Thời gian cập nhật';

comment on column permissions.module is 'Module';

alter table permissions
    owner to root;

grant delete, insert, select, update on permissions to member;

create table employee_role_has_permission
(
    role_id       integer not null
        constraint employee_role_has_permission_employee_roles_id_fk
            references employee_roles,
    permission_id integer not null
        constraint employee_role_has_permission_api_mapping_id_fk
            references permissions,
    constraint employee_role_has_permission_pk
        primary key (permission_id, role_id)
);

alter table employee_role_has_permission
    owner to root;

grant delete, insert, select, update on employee_role_has_permission to member;

create table plans
(
    id           serial
        primary key,
    name         varchar(100) not null,
    description  text,
    created_at   bigint       not null,
    updated_at   bigint       not null,
    package_type package_type default 'TIME_ONLY'::package_type
);

comment on table plans is 'Bảng lưu gói dịch vụ chính (subscription-based)';

comment on column plans.id is 'Định danh gói dịch vụ (UUID)';

comment on column plans.name is 'Tên gói (Basic, Pro, Enterprise...)';

comment on column plans.description is 'Mô tả chi tiết gói';

comment on column plans.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column plans.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table plans
    owner to root;

grant select, update, usage on sequence plans_id_seq to member;

grant delete, insert, select, update on plans to member;

create table plan_pricing
(
    id            serial
        primary key,
    plan_id       serial
        references plans
            on delete cascade,
    billing_cycle varchar(50) not null,
    price         numeric     not null,
    created_at    bigint      not null,
    updated_at    bigint,
    usage_limit   bigint,
    usage_unit    varchar(50),
    is_active     boolean     not null,
    description   text,
    unique (plan_id, billing_cycle)
);

comment on table plan_pricing is 'Bảng lưu các mức giá khác nhau cho mỗi plan theo chu kỳ thanh toán';

comment on column plan_pricing.plan_id is 'Khóa ngoại tới bảng plans';

comment on column plan_pricing.billing_cycle is 'Chu kỳ thanh toán (month, 6_months, year,...)';

comment on column plan_pricing.price is 'Giá của plan ứng với chu kỳ thanh toán này';

comment on column plan_pricing.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column plan_pricing.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

comment on column plan_pricing.usage_limit is 'Giới hạn dung lượng (có thể là NULL nếu là TIME_ONLY). Dùng BIGINT để lưu giá trị lớn (vd: bytes, requests)';

comment on column plan_pricing.usage_unit is 'Đơn vị của giá trị gói';

alter table plan_pricing
    owner to root;

grant select, update, usage on sequence plan_pricing_id_seq to member;

grant select, update, usage on sequence plan_pricing_plan_id_seq to member;

grant delete, insert, select, update on plan_pricing to member;

create table user_roles
(
    id         serial
        constraint user_roles_pk
            primary key,
    type       varchar(255)
        constraint user_roles_pk_2
            unique,
    name       varchar(255),
    created_at bigint,
    updated_at bigint
);

alter table user_roles
    owner to root;

grant select, update, usage on sequence user_roles_id_seq to member;

grant delete, insert, select, update on user_roles to member;

create table user_roles_has_permission
(
    role_id       integer not null
        constraint user_roles_has_permission_user_roles_id_fk
            references user_roles,
    permission_id integer not null
        constraint user_roles_has_permission_permissions_id_fk
            references permissions,
    constraint user_roles_has_permission_pk
        primary key (permission_id, role_id)
);

alter table user_roles_has_permission
    owner to root;

grant delete, insert, select, update on user_roles_has_permission to member;

create table plan_pricing_has_role
(
    plan_pricing_id integer not null
        constraint plan_pricing_has_role_plan_pricing_id_fk
            references plan_pricing,
    role_id         integer not null
        constraint plan_pricing_has_role_user_roles_id_fk
            references user_roles,
    constraint plan_pricing_has_role_pk
        primary key (plan_pricing_id, role_id)
);

comment on table plan_pricing_has_role is 'Bảng gói dịch vụ và quyền của gói dịch vụ đó';

comment on column plan_pricing_has_role.plan_pricing_id is 'mã gói dịch vụ';

comment on column plan_pricing_has_role.role_id is 'mã quyền ';

alter table plan_pricing_has_role
    owner to root;

grant delete, insert, select, update on plan_pricing_has_role to member;

create table system_configuration
(
    id                                  integer not null
        constraint system_configuration_pk
            primary key,
    bank_code                           varchar(20)
        constraint system_configuration_banks_bank_code_fk
            references banks,
    created_at                          bigint,
    updated_at                          bigint,
    account_number                      varchar(255),
    account_name                        varchar(255),
    active                              boolean,
    fee_percentage                      double precision,
    purchase_invoice_template           varchar(255),
    email_notification_system_id        integer,
    initial_affiliate_contract_business varchar(255),
    initial_rule_contract_business      varchar(255),
    initial_affiliate_contract_customer varchar(255),
    initial_rule_contract_customer      varchar(255)
);

comment on table system_configuration is 'Cấu hình của hệ thống';

comment on column system_configuration.bank_code is 'Mã ngân hàng';

comment on column system_configuration.created_at is 'Thời gian tạo';

comment on column system_configuration.updated_at is 'Thời gian cập nhật';

comment on column system_configuration.account_number is 'Số tài khoản';

comment on column system_configuration.account_name is 'Tên tài khoản';

comment on column system_configuration.active is 'Kích hoạt hay không';

comment on column system_configuration.fee_percentage is 'Lưu trữ phần trăm phí sàn, ví dụ: 2.50 nghĩa là 2.50%';

comment on column system_configuration.purchase_invoice_template is 'Link mẫu hóa đơn đầu vào';

comment on column system_configuration.email_notification_system_id is 'id của tài khoản email notification';

alter table system_configuration
    owner to root;

grant delete, insert, select, update on system_configuration to member;

create table admin_template_email
(
    id           serial
        constraint admin_template_email_pk_2
            primary key,
    subject      varchar(255),
    category     varchar(100)
        constraint admin_template_email_pk
            unique,
    content      text,
    created_at   bigint,
    updated_at   bigint,
    placeholders json,
    name         varchar(100),
    created_by   bigint,
    updated_by   bigint
);

comment on table admin_template_email is 'Danh sách template email bên admin';

comment on column admin_template_email.subject is 'Tiêu đề';

comment on column admin_template_email.category is 'Danh mục email';

comment on column admin_template_email.content is 'Nội dung email';

comment on column admin_template_email.created_at is 'Thời gian tạo';

comment on column admin_template_email.updated_at is 'Thời gian cập nhật';

comment on column admin_template_email.placeholders is 'Danh sách các placeholder';

comment on column admin_template_email.name is 'Tên mẫu';

alter table admin_template_email
    owner to root;

grant select, update, usage on sequence admin_template_email_id_seq to member;

grant delete, insert, select, update on admin_template_email to member;

create table affiliate_rank
(
    id            integer default nextval('affiliate_rank_rank_id_seq'::regclass) not null
        primary key,
    commission    double precision,
    min_condition bigint,
    max_condition bigint,
    rank_name     varchar(45),
    rank_badge    varchar(255),
    is_active     boolean default true                                            not null,
    display_order integer default 0                                               not null
        constraint affiliate_rank_display_order_check
            check (display_order >= 0),
    description   text,
    created_at    bigint,
    updated_at    bigint
);

comment on table affiliate_rank is 'Bảng rank của affiliate';

comment on column affiliate_rank.id is 'mã rank';

comment on column affiliate_rank.commission is 'Phần trăm hoa hồng';

comment on column affiliate_rank.min_condition is 'Số lượng người giới thiệu tối thiểu';

comment on column affiliate_rank.max_condition is 'Số lượng người giới thiệu tối đa';

comment on column affiliate_rank.rank_name is 'Tên rank';

comment on column affiliate_rank.rank_badge is 'Logo rank';

comment on column affiliate_rank.is_active is 'Trạng thái kích hoạt của rank';

comment on column affiliate_rank.display_order is 'Thứ tự hiển thị của rank';

comment on column affiliate_rank.description is 'Mô tả chi tiết về rank';

comment on column affiliate_rank.created_at is 'Thời gian tạo rank (Unix timestamp)';

comment on column affiliate_rank.updated_at is 'Thời gian cập nhật rank (Unix timestamp)';

alter table affiliate_rank
    owner to root;

grant delete, insert, select, update on affiliate_rank to member;

create table admin_template_sms
(
    id           serial
        primary key,
    category     varchar(100)
        unique,
    content      text,
    created_at   bigint,
    updated_at   bigint,
    placeholders json,
    name         varchar(100)
);

comment on table admin_template_sms is 'Danh sách template sms bên admin';

comment on column admin_template_sms.category is 'Danh mục sms';

comment on column admin_template_sms.content is 'Nội dung sms';

comment on column admin_template_sms.created_at is 'Thời gian tạo';

comment on column admin_template_sms.updated_at is 'Thời gian cập nhật';

comment on column admin_template_sms.placeholders is 'Danh sách các placeholder';

comment on column admin_template_sms.name is 'Tên mẫu';

alter table admin_template_sms
    owner to root;

grant select, update, usage on sequence admin_template_sms_id_seq to member;

grant delete, insert, select, update on admin_template_sms to member;

create table admin_audience
(
    id         bigserial
        primary key,
    email      varchar(255),
    phone      varchar(20),
    created_at bigint not null,
    updated_at bigint
);

comment on table admin_audience is 'Bảng khách hàng của admin';

comment on column admin_audience.email is 'Email người dùng';

comment on column admin_audience.phone is 'Số điện thoại';

comment on column admin_audience.created_at is 'Ngày tạo';

comment on column admin_audience.updated_at is 'Ngày cập nhật';

alter table admin_audience
    owner to root;

grant select, update, usage on sequence admin_audience_id_seq to member;

grant delete, insert, select, update on admin_audience to member;

create table admin_tags
(
    id         bigserial
        primary key,
    name       varchar(255),
    color      varchar(7),
    created_at bigint,
    updated_at bigint,
    created_by integer
        constraint admin_tags_employees_id_fk
            references employees,
    updated_by integer
        constraint admin_tags_employees_id_fk_2
            references employees
);

alter table admin_tags
    owner to root;

grant select, update, usage on sequence admin_tags_id_seq to member;

grant delete, insert, select, update on admin_tags to member;

create table admin_audience_has_tags
(
    audience_id bigint not null
        constraint admin_audience_has_tags_admin_audience_id_fk
            references admin_audience,
    tag_id      bigint not null
        constraint admin_audience_has_tags_admin_tags_id_fk
            references admin_tags,
    constraint admin_audience_has_tags_pk
        primary key (tag_id, audience_id)
);

alter table admin_audience_has_tags
    owner to root;

grant delete, insert, select, update on admin_audience_has_tags to member;

create table admin_segments
(
    id          bigserial
        primary key,
    name        varchar(255),
    description text,
    criteria    jsonb,
    created_at  bigint,
    updated_at  bigint
);

comment on table admin_segments is 'Phân khúc khách hàng của admin';

comment on column admin_segments.name is 'Tên tập khách hàng';

comment on column admin_segments.description is 'Mô tả';

comment on column admin_segments.criteria is 'Lưu trữ điều kiện lọc khách hàng khi tạo segment';

comment on column admin_segments.created_at is 'Thời gian tạo';

comment on column admin_segments.updated_at is 'Thời gian cập nhật';

alter table admin_segments
    owner to root;

grant select, update, usage on sequence admin_segments_id_seq to member;

grant delete, insert, select, update on admin_segments to member;

create table admin_audience_custom_fields
(
    id          bigserial
        primary key,
    field_name  varchar(255),
    field_value jsonb,
    field_type  varchar(20),
    created_at  bigint,
    updated_at  bigint,
    audience_id integer
);

comment on table admin_audience_custom_fields is 'Bảng danh sách trường tùy chỉnh của audience của admin';

comment on column admin_audience_custom_fields.field_name is 'Tên trường';

comment on column admin_audience_custom_fields.field_value is 'Giá trị';

comment on column admin_audience_custom_fields.field_type is 'Kiểu giá trị';

comment on column admin_audience_custom_fields.created_at is 'Thời gian tạo';

comment on column admin_audience_custom_fields.updated_at is 'Thời gian cập nhật';

alter table admin_audience_custom_fields
    owner to root;

grant select, update, usage on sequence admin_audience_custom_fields_id_seq to member;

grant delete, insert, select, update on admin_audience_custom_fields to member;

create table admin_campaigns
(
    id           bigserial
        primary key,
    title        varchar(255),
    description  text,
    platform     varchar(255),
    content      text,
    server       jsonb,
    scheduled_at bigint,
    subject      varchar(255),
    created_at   bigint,
    status       varchar(20),
    employee_id  integer
        constraint user_campaigns_users_id_fk
            references employees,
    updated_at   bigint
);

comment on table admin_campaigns is 'Bảng chiến dịch của admin';

comment on column admin_campaigns.title is 'Tiêu đề';

comment on column admin_campaigns.description is 'Mô tả';

comment on column admin_campaigns.platform is 'Nền tảng';

comment on column admin_campaigns.content is 'Nội dung';

comment on column admin_campaigns.server is 'Thông tin máy chủ gửi';

comment on column admin_campaigns.scheduled_at is 'Thời gian dự kiến gửi chiến dịch';

comment on column admin_campaigns.subject is 'Nội dung tiêu đề với chiến dịch là email ';

comment on column admin_campaigns.created_at is 'Ngày tạo';

comment on column admin_campaigns.status is 'Trạng thái';

comment on column admin_campaigns.employee_id is 'Mã nhân viên';

comment on column admin_campaigns.updated_at is 'Ngày cập nhật';

alter table admin_campaigns
    owner to root;

grant select, update, usage on sequence admin_campaigns_id_seq to member;

grant delete, insert, select, update on admin_campaigns to member;

create table admin_campaign_history
(
    id          bigserial
        primary key,
    campaign_id bigint
        constraint admin_campaign_history_admin_campaigns_id_fk
            references admin_campaigns,
    audience_id bigint
        constraint admin_campaign_history_admin_audience_id_fk
            references admin_audience,
    status      varchar(20),
    sent_at     bigint,
    created_at  bigint
);

comment on table admin_campaign_history is 'Bảng lịch sử chăm sóc khách hàng của admin';

comment on column admin_campaign_history.campaign_id is 'Mã chiến dịch';

comment on column admin_campaign_history.audience_id is 'Mã khách hàng';

comment on column admin_campaign_history.status is 'Trạng thái';

comment on column admin_campaign_history.sent_at is 'Thời gian gửi';

comment on column admin_campaign_history.created_at is 'Thời gian tạo';

alter table admin_campaign_history
    owner to root;

grant select, update, usage on sequence admin_campaign_history_id_seq to member;

grant delete, insert, select, update on admin_campaign_history to member;

create table system_api_key
(
    id         serial
        constraint system_api_key_pk
            primary key,
    info       jsonb,
    provider   varchar(30),
    note       varchar(1000),
    created_at bigserial,
    updated_at bigserial,
    created_by integer
        constraint system_api_key_employees_id_fk
            references employees,
    updated_by integer
        constraint system_api_key_employees_id_fk_2
            references employees,
    type       varchar(30)
);

comment on table system_api_key is 'Lưu trữkey của hệ thống';

comment on column system_api_key.info is 'Thông tin cấu hình';

comment on column system_api_key.provider is 'nhà cung cấp';

comment on column system_api_key.note is 'ghi chú';

comment on column system_api_key.created_at is 'thời gian tạo';

comment on column system_api_key.updated_at is 'thời gian cập nhật';

comment on column system_api_key.created_by is 'nhân viên tạo ';

comment on column system_api_key.updated_by is 'nhân viên cập nhật';

comment on column system_api_key.type is 'loại sử dụng';

alter table system_api_key
    owner to root;

grant select, update, usage on sequence system_api_key_id_seq to member;

grant select, update, usage on sequence system_api_key_created_at_seq to member;

grant select, update, usage on sequence system_api_key_updated_at_seq to member;

grant delete, insert, select, update on system_api_key to member;

create table providers
(
    id                   serial
        primary key,
    name                 varchar(255)                                                                     not null,
    type                 varchar(50)                                                                      not null,
    provider_info        jsonb            default '{"website": null, "logo_url": null, "description": null}'::jsonb,
    required_credentials jsonb            default '[{"example": "abcd1234xyz", "data_type": "string", "field_name": "api_key", "description": "Khóa API được cấp bởi nhà cung cấp vận chuyển", "is_required": true, "placeholder": "Nhập API Key", "display_name": "API Key", "validation_rules": {"pattern": null, "max_length": 100, "min_length": 6}}, {"example": "s3cr3t@key!123", "data_type": "string", "field_name": "api_secret", "description": "Mã bí mật API được cấp bởi nhà cung cấp vận chuyển", "is_required": true, "placeholder": "Nhập API Secret", "display_name": "API Secret", "validation_rules": {"pattern": null, "max_length": 100, "min_length": 8}}]'::jsonb,
    created_by           integer
        references employees,
    update_by            integer
        references employees,
    created_at           bigint           default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at           bigint           default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status               providers_status default 'APPROVED'::providers_status                            not null
);

comment on table providers is 'Quản lý thông tin nhà cung cấp dịch vụ';

comment on column providers.name is 'Tên nhà cung cấp';

comment on column providers.provider_info is 'Thông tin chi tiết nhà cung cấp';

comment on column providers.required_credentials is 'Danh sách các thông tin xác thực cần thiết';

comment on column providers.created_by is 'Người tạo';

alter table providers
    owner to root;

grant select, update, usage on sequence providers_id_seq to member;

grant delete, insert, select, update on providers to member;

create table llm_key
(
    id          uuid   default uuid_generate_v4()                                      not null
        primary key,
    provider_id integer
        references providers,
    content     jsonb                                                                  not null,
    create_by   integer
        references employees,
    update_by   integer
        references employees,
    create_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table llm_key is 'Bảng lưu trữ khóa API cho các mô hình ngôn ngữ lớn (LLM)';

comment on column llm_key.id is 'ID duy nhất của khóa API, là UUID';

comment on column llm_key.provider_id is 'ID tham chiếu đến nhà cung cấp dịch vụ LLM';

comment on column llm_key.content is 'Nội dung chi tiết của khóa API, bao gồm khóa, cài đặt và giới hạn';

comment on column llm_key.create_by is 'ID nhân viên tạo khóa';

comment on column llm_key.update_by is 'ID nhân viên cập nhật khóa gần nhất';

comment on column llm_key.create_at is 'Thời điểm tạo khóa (milliseconds)';

comment on column llm_key.update_at is 'Thời điểm cập nhật khóa gần nhất (milliseconds)';

alter table llm_key
    owner to root;

create index idx_llm_key_provider_id
    on llm_key (provider_id);

create index idx_llm_key_create_by
    on llm_key (create_by);

create index idx_llm_key_update_by
    on llm_key (update_by);

create index idx_llm_key_is_active
    on llm_key ((content ->> 'is_active'::text));

create index idx_llm_key_api_version
    on llm_key ((content ->> 'api_version'::text));

grant delete, insert, select, update on llm_key to member;

create table vector_store_key
(
    id          uuid   default uuid_generate_v4()                                      not null
        primary key,
    provider_id integer
        references providers,
    content     jsonb                                                                  not null,
    storage     bigint default 0                                                       not null,
    create_by   integer
        references employees,
    update_by   integer
        references employees,
    create_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table vector_store_key is 'Bảng lưu trữ khóa API cho các dịch vụ kho vector';

comment on column vector_store_key.id is 'ID duy nhất của khóa API, là UUID';

comment on column vector_store_key.provider_id is 'ID tham chiếu đến nhà cung cấp dịch vụ kho vector';

comment on column vector_store_key.content is 'Nội dung chi tiết của khóa API, bao gồm khóa và cài đặt';

comment on column vector_store_key.storage is 'Dung lượng lưu trữ đã sử dụng tính bằng bytes';

comment on column vector_store_key.create_by is 'ID nhân viên tạo khóa';

comment on column vector_store_key.update_by is 'ID nhân viên cập nhật khóa gần nhất';

comment on column vector_store_key.create_at is 'Thời điểm tạo khóa (milliseconds)';

comment on column vector_store_key.update_at is 'Thời điểm cập nhật khóa gần nhất (milliseconds)';

alter table vector_store_key
    owner to root;

create table users
(
    id                       serial
        constraint users_pk
            primary key,
    full_name                varchar(100),
    email                    varchar(100)
        constraint users_pk_2
            unique,
    phone_number             varchar(45)
        constraint users_pk_3
            unique,
    is_active                boolean   default true                    not null,
    is_verify_email          boolean   default false                   not null,
    created_at               bigint,
    updated_at               bigint,
    citizen_issue_place      varchar(100),
    citizen_issue_date       date,
    is_first_password_change boolean   default false                   not null,
    country                  integer,
    address                  varchar(1000),
    tax_code                 varchar(20),
    points_balance           bigint    default 0                       not null,
    type                     user_type default 'INDIVIDUAL'::user_type not null,
    platform                 varchar(20),
    citizen_id               varchar(20),
    avatar                   varchar(255),
    password                 varchar(1000),
    date_of_birth            date,
    gender                   gender,
    bank_code                varchar(20),
    account_number           varchar(50),
    account_holder           varchar(255),
    bank_branch              varchar(255),
    is_verify_phone          boolean,
    vector_store_key         uuid
        constraint users_vector_store_key_id_fk
            references vector_store_key,
    alert_threshold          bigint,
    was_rpoint_alerted       boolean,
    affiliate_account_id     integer,
    facebook_id              varchar(255)
        constraint users_pk_4
            unique,
    google_id                varchar
        unique
);

comment on table users is 'Bảng tài khoản của người dùng';

comment on column users.full_name is 'tên đầy đủ của người dùng';

comment on column users.email is 'email của người dùng';

comment on column users.phone_number is 'số điện thoại của người dùng';

comment on column users.is_active is 'trạng thái tài khoản';

comment on column users.is_verify_email is 'trạng thái xác thực';

comment on column users.created_at is 'thời gian tạo tài khoản';

comment on column users.updated_at is 'thời gian cập nhật thông tin';

comment on column users.citizen_issue_place is 'Nơi cấp chứng minh nhân dân';

comment on column users.citizen_issue_date is 'ngày cấp chứng minh nhân dân';

comment on column users.is_first_password_change is 'đánh dấu lần thay đổi mật khẩu đầu tiên';

comment on column users.country is 'đất nước';

comment on column users.address is 'địa chỉ';

comment on column users.tax_code is 'mã số thuế';

comment on column users.points_balance is 'Số dư points hiện tại';

comment on column users.type is 'loại tài khoản ''INDIVIDUAL'' hoặc''BUSINESS''';

comment on column users.platform is 'nền tảng đăng ký';

comment on column users.citizen_id is 'căn cước công dân';

comment on column users.avatar is 'avatar của người dùng';

comment on column users.password is 'mật khẩu đã được mã hóa của người dùng';

comment on column users.date_of_birth is 'Ngày sinh';

comment on column users.gender is 'Giới tính';

comment on column users.bank_code is 'mã code của ngân hàng';

comment on column users.account_number is 'Số tài khoản ngân hàng';

comment on column users.account_holder is 'tên tài khoản ngân hàng';

comment on column users.bank_branch is 'chi nhánh ngân hàng';

comment on column users.is_verify_phone is 'xác thực số điện thoại';

comment on column users.vector_store_key is 'key dùng cho tạo vector store';

comment on column users.alert_threshold is 'mức rpoint cảnh báo';

comment on column users.was_rpoint_alerted is 'đã cảnh báo rpoint hay chưa';

comment on column users.affiliate_account_id is 'Mã của người giới thiệu nếu có';

alter table users
    owner to root;

grant select, update, usage on sequence users_id_seq to member;

grant delete, insert, select, update on users to member;

create table business_info
(
    id                                serial
        constraint business_info_pk
            primary key,
    user_id                           integer      not null
        constraint business_info_pk_2
            unique
        constraint business_info_users_id_fk
            references users,
    business_name                     varchar(255) not null,
    business_email                    varchar(100) not null,
    business_phone                    varchar(20)  not null,
    business_registration_certificate varchar(255),
    tax_code                          varchar(20)  not null,
    created_at                        bigint       not null,
    updated_at                        bigint,
    representative_position           varchar(255),
    representative_name               varchar(100),
    status                            varchar(20)  not null,
    business_address                  varchar(1000)
);

comment on column business_info.user_id is 'mã tài khoản';

comment on column business_info.business_name is 'tên công ty';

comment on column business_info.business_email is 'email công ty';

comment on column business_info.business_phone is 'số điện thoại công ty';

comment on column business_info.business_registration_certificate is 'url giấy phép kinh doanh';

comment on column business_info.tax_code is 'mã số thuế';

comment on column business_info.created_at is 'thời gian tạo';

comment on column business_info.updated_at is 'thời gian chỉnh sửa';

comment on column business_info.representative_position is 'vị trí người đại diện';

comment on column business_info.representative_name is 'họ tên người đại diện';

comment on column business_info.status is 'trạng thái xác thực';

comment on column business_info.business_address is 'Địa chỉ công ty';

alter table business_info
    owner to root;

grant select, update, usage on sequence business_info_id_seq to member;

grant delete, insert, select, update on business_info to member;

create table two_factor_auth
(
    id                                integer default nextval('user_2fa_settings_id_seq'::regclass) not null
        constraint two_factor_auth_pk_2
            primary key,
    user_id                           integer
        constraint two_factor_auth_pk
            unique
        constraint two_factor_auth_users_id_fk
            references users,
    otp_sms_enabled                   boolean default false,
    otp_email_enabled                 boolean default false                                         not null,
    google_authenticator_enabled      boolean default false                                         not null,
    google_authenticator_secret       varchar(255),
    created_at                        bigint,
    updated_at                        bigint,
    is_google_authenticator_confirmed boolean default false                                         not null
);

comment on column two_factor_auth.user_id is 'mã tài khoản';

comment on column two_factor_auth.otp_sms_enabled is 'xác thực sms';

comment on column two_factor_auth.otp_email_enabled is 'xác thực email';

comment on column two_factor_auth.google_authenticator_enabled is 'xác thực google authenticator';

comment on column two_factor_auth.google_authenticator_secret is 'khóa bí mật google authenticator';

comment on column two_factor_auth.created_at is 'thời gian tạo';

comment on column two_factor_auth.updated_at is 'thời gian cập nhật';

comment on column two_factor_auth.is_google_authenticator_confirmed is 'confirm google authenticator';

alter table two_factor_auth
    owner to root;

grant delete, insert, select, update on two_factor_auth to member;

create table device_info
(
    id               uuid         not null
        primary key,
    user_id          integer
        references users,
    fingerprint      varchar(255) not null,
    ip_address       varchar(45),
    user_agent       text,
    browser          varchar(255),
    operating_system varchar(255),
    is_trusted       boolean default false,
    last_login       bigint,
    created_at       bigint       not null,
    updated_at       bigint       not null
);

alter table device_info
    owner to root;

grant delete, insert, select, update on device_info to member;

create table auth_verification_logs
(
    id            bigserial
        primary key,
    user_id       integer
        references users,
    auth_method   auth_method_enum not null,
    status        auth_status_enum not null,
    ip_address    varchar(45),
    user_agent    text,
    code_sent_at  bigint,
    verified_at   bigint,
    attempt_count integer default 0,
    created_at    bigint           not null
);

alter table auth_verification_logs
    owner to root;

grant select, update, usage on sequence auth_verification_logs_id_seq to member;

grant delete, insert, select, update on auth_verification_logs to member;

create table point_purchase_transactions
(
    id             bigserial
        primary key,
    user_id        integer
        references users,
    amount         double precision not null,
    points_amount  integer          not null,
    point_name     varchar(255),
    point_id       integer,
    currency       varchar(10)        default 'VND'::character varying,
    status         transaction_status default 'PENDING'::transaction_status,
    payment_method varchar(50)      not null,
    reference_id   varchar(100),
    description    text,
    retry_count    integer            default 0,
    last_retry_at  bigint,
    created_at     bigint           not null,
    updated_at     bigint           not null,
    completed_at   bigint,
    balance_before integer          not null,
    balance_after  integer          not null,
    coupon_id      integer,
    coupon_amount  double precision
);

comment on column point_purchase_transactions.id is 'Định danh giao dịch mua điểm';

comment on column point_purchase_transactions.user_id is 'Định danh người dùng';

comment on column point_purchase_transactions.amount is 'Số tiền';

comment on column point_purchase_transactions.points_amount is 'Số lượng points mua';

comment on column point_purchase_transactions.point_name is 'Tên loại point (nếu có)';

comment on column point_purchase_transactions.point_id is 'ID loại point (nếu có)';

comment on column point_purchase_transactions.currency is 'Loại tiền tệ';

comment on column point_purchase_transactions.status is 'Trạng thái giao dịch (PENDING, CONFIRMED, FAILED, REFUNDED)';

comment on column point_purchase_transactions.payment_method is 'Phương thức thanh toán';

comment on column point_purchase_transactions.reference_id is 'Mã tham chiếu từ cổng thanh toán';

comment on column point_purchase_transactions.description is 'Mô tả giao dịch';

comment on column point_purchase_transactions.retry_count is 'Số lần thử lại thanh toán';

comment on column point_purchase_transactions.last_retry_at is 'Thời gian thử lại cuối cùng (Unix timestamp)';

comment on column point_purchase_transactions.created_at is 'Thời gian tạo giao dịch (Unix timestamp)';

comment on column point_purchase_transactions.updated_at is 'Thời gian cập nhật giao dịch (Unix timestamp)';

comment on column point_purchase_transactions.completed_at is 'Thời gian hoàn thành giao dịch (Unix timestamp)';

comment on column point_purchase_transactions.balance_before is 'Số dư trước giao dịch';

comment on column point_purchase_transactions.balance_after is 'Số dư sau giao dịch';

comment on column point_purchase_transactions.coupon_amount is 'Tiền khuyến mãi, giảm giá';

alter table point_purchase_transactions
    owner to root;

grant select, update, usage on sequence point_purchase_transactions_id_seq to member;

grant delete, insert, select, update on point_purchase_transactions to member;

create table affiliate_accounts
(
    user_id           integer                                                            not null
        unique
        references users,
    status            affiliate_account_status default 'STEP0'::affiliate_account_status not null,
    total_earned      numeric(15, 2)           default 0                                 not null,
    total_paid_out    numeric(15, 2)           default 0                                 not null,
    available_balance numeric(15, 2)           default 0                                 not null,
    created_at        bigint                                                             not null,
    updated_at        bigint                                                             not null,
    id                serial
        constraint affiliate_accounts_pk
            primary key,
    performance       bigint                   default 0                                 not null,
    step              integer
);

comment on table affiliate_accounts is 'Thông tin tài khoản affiliate của người dùng';

comment on column affiliate_accounts.user_id is 'Liên kết đến người dùng tương ứng (users.id)';

comment on column affiliate_accounts.status is 'Trạng thái tài khoản affiliate theo từng bước quy trình duyệt/kích hoạt';

comment on column affiliate_accounts.total_earned is 'Tổng số tiền người dùng đã kiếm được từ affiliate';

comment on column affiliate_accounts.total_paid_out is 'Tổng số tiền đã được rút ra hoặc thanh toán cho người dùng';

comment on column affiliate_accounts.available_balance is 'Số dư hiện tại còn lại (total_earned - total_paid_out)';

comment on column affiliate_accounts.created_at is 'Thời gian tạo bản ghi (Unix timestamp)';

comment on column affiliate_accounts.updated_at is 'Thời gian cập nhật bản ghi gần nhất (Unix timestamp)';

comment on column affiliate_accounts.performance is 'Số lượng khách hàng mà người dùng giới thiệu trong 3 tháng gần nhất';

alter table affiliate_accounts
    owner to root;

grant select, update, usage on sequence affiliate_accounts_id_seq to member;

alter table users
    add constraint users_affiliate_accounts_id_fk
        foreign key (affiliate_account_id) references affiliate_accounts;

grant delete, insert, select, update on affiliate_accounts to member;

create table affiliate_contracts
(
    id                   bigserial
        primary key,
    user_id              integer
        references users,
    contract_type        contract_type_enum not null,
    status               contract_status_enum default 'DRAFT'::contract_status_enum,
    terms_accepted       boolean              default false,
    document_path        varchar(255),
    sign_method          sign_method_enum,
    employeeid           integer,
    rejection_reason     varchar(2000),
    created_at           bigint             not null,
    updated_at           bigint             not null,
    approved_at          bigint,
    citizen_id_front_url varchar(255),
    citizen_id_back_url  varchar(255),
    signature_url        varchar(255)
);

comment on column affiliate_contracts.citizen_id_front_url is 'URL ảnh mặt trước CCCD';

comment on column affiliate_contracts.citizen_id_back_url is 'URL ảnh mặt sau CCCD';

comment on column affiliate_contracts.signature_url is 'URL chữ ký';

alter table affiliate_contracts
    owner to root;

grant select, update, usage on sequence affiliate_contracts_id_seq to member;

grant delete, insert, select, update on affiliate_contracts to member;

create table affiliate_clicks
(
    id                   bigserial
        primary key,
    affiliate_account_id integer
        references affiliate_accounts,
    referral_code        varchar(50) not null,
    ip_address           varchar(45),
    user_agent           text,
    referrer_url         text,
    landing_page         text,
    click_time           bigint      not null
);

comment on table affiliate_clicks is 'Lưu thông tin các lượt click từ affiliate links';

comment on column affiliate_clicks.id is 'Định danh duy nhất của lượt click (UUID)';

comment on column affiliate_clicks.affiliate_account_id is 'Tài khoản affiliate đã tạo lượt click (liên kết với bảng users)';

comment on column affiliate_clicks.referral_code is 'Mã giới thiệu của affiliate dùng trong lượt click';

comment on column affiliate_clicks.ip_address is 'Địa chỉ IP của người click';

comment on column affiliate_clicks.user_agent is 'Thông tin trình duyệt của người click';

comment on column affiliate_clicks.referrer_url is 'Trang giới thiệu dẫn tới lượt click';

comment on column affiliate_clicks.landing_page is 'Trang đích sau khi click';

comment on column affiliate_clicks.click_time is 'Thời điểm diễn ra lượt click (Unix timestamp)';

alter table affiliate_clicks
    owner to root;

grant select, update, usage on sequence affiliate_clicks_id_seq to member;

grant delete, insert, select, update on affiliate_clicks to member;

create table affiliate_customer_order
(
    order_id             bigint not null
        constraint affiliate_customer_order_pk
            primary key,
    commission           double precision,
    affiliate_account_id integer
        constraint affiliate_customer_order_affiliate_accounts_id_fk
            references affiliate_accounts,
    rank_id              integer
);

comment on table affiliate_customer_order is 'Đơn hàng affiliate';

comment on column affiliate_customer_order.order_id is 'Mã đơn hàng';

comment on column affiliate_customer_order.commission is 'Phần trăm hoa hồng';

comment on column affiliate_customer_order.affiliate_account_id is 'tài khoản affiliate';

comment on column affiliate_customer_order.rank_id is 'Mã rank affiliate không cần nối khóa phụ';

alter table affiliate_customer_order
    owner to root;

grant delete, insert, select, update on affiliate_customer_order to member;

create table subscriptions
(
    id              bigserial
        primary key,
    user_id         serial
        references users,
    plan_pricing_id serial
        references plan_pricing,
    start_date      bigint                                                    not null,
    end_date        bigint                                                    not null,
    auto_renew      boolean             default true,
    status          subscription_status default 'ACTIVE'::subscription_status not null,
    created_at      bigint                                                    not null,
    updated_at      bigint                                                    not null,
    usage_limit     bigint,
    current_usage   bigint,
    remaining_value bigint,
    usage_unit      varchar(50),
    constraint subscriptions_pk
        unique (plan_pricing_id, user_id)
);

comment on table subscriptions is 'Bảng quản lý gói dịch vụ user đăng ký';

comment on column subscriptions.id is 'Định danh Subscription (UUID)';

comment on column subscriptions.user_id is 'Người dùng sở hữu subscription';

comment on column subscriptions.plan_pricing_id is 'Tùy chọn giá/chu kỳ thanh toán cụ thể mà user đăng ký (plan_pricing.id)';

comment on column subscriptions.start_date is 'Ngày bắt đầu subscription (Unix timestamp)';

comment on column subscriptions.end_date is 'Ngày hết hạn subscription (Unix timestamp)';

comment on column subscriptions.auto_renew is 'Có tự động gia hạn không (TRUE/FALSE)';

comment on column subscriptions.status is 'Trạng thái subscription (ACTIVE, CANCELLED,...)';

comment on column subscriptions.created_at is 'Thời điểm tạo';

comment on column subscriptions.updated_at is 'Thời điểm cập nhật';

comment on column subscriptions.usage_limit is 'Tổng số lượng tài nguyên được phép sử dụng';

comment on column subscriptions.current_usage is 'Số lượng tài nguyên đã sử dụng';

comment on column subscriptions.remaining_value is 'Số lượng tài nguyên còn lại';

comment on column subscriptions.usage_unit is 'Đơn vị của tài nguyên';

alter table subscriptions
    owner to root;

grant select, update, usage on sequence subscriptions_id_seq to member;

grant select, update, usage on sequence subscriptions_user_id_seq to member;

grant select, update, usage on sequence subscriptions_plan_pricing_id_seq to member;

grant delete, insert, select, update on subscriptions to member;

create table usage_logs
(
    id              bigserial
        primary key,
    subscription_id bigserial
        references subscriptions,
    feature         varchar(100) not null,
    amount          numeric      not null,
    usage_time      bigint       not null,
    created_at      bigint       not null
);

comment on table usage_logs is 'Ghi nhận mức sử dụng user, phục vụ tính phí usage-based';

comment on column usage_logs.subscription_id is 'Subscription mà usage thuộc về';

comment on column usage_logs.feature is 'Tên tính năng, map với plan_features.feature';

comment on column usage_logs.amount is 'Số lượng usage (GB, request,...)';

comment on column usage_logs.usage_time is 'Thời điểm usage phát sinh (Unix timestamp)';

comment on column usage_logs.created_at is 'Thời điểm ghi log (Unix timestamp)';

alter table usage_logs
    owner to root;

grant select, update, usage on sequence usage_logs_id_seq to member;

grant select, update, usage on sequence usage_logs_subscription_id_seq to member;

grant delete, insert, select, update on usage_logs to member;

create table user_has_roles
(
    user_id integer not null
        constraint user_has_roles_users_id_fk
            references users,
    role_id integer not null
        constraint user_has_roles_user_roles_id_fk
            references user_roles,
    constraint user_has_roles_pk
        primary key (user_id, role_id)
);

alter table user_has_roles
    owner to root;

grant delete, insert, select, update on user_has_roles to member;

create table affiliate_point_conversion_history
(
    id                   serial
        constraint affiliate_point_conversion_history_pk
            primary key,
    points_converted     bigint,
    affiliate_account_id integer
        constraint affiliate_point_conversion_history___fk_affiliate_account
            references affiliate_accounts,
    conversion_rate      double precision,
    amount               double precision,
    created_at           bigint,
    status               point_conversion_history_status
);

comment on table affiliate_point_conversion_history is 'Bảng lịch sử đổi tiền affiliate sang point hệ thống';

comment on column affiliate_point_conversion_history.points_converted is 'số point đổi được';

comment on column affiliate_point_conversion_history.affiliate_account_id is 'mã tài khoản affiliate_accounts';

comment on column affiliate_point_conversion_history.conversion_rate is 'tỷ lệ chuyển đổi';

comment on column affiliate_point_conversion_history.amount is 'số tiền rút';

comment on column affiliate_point_conversion_history.created_at is 'thời giam tạo';

comment on column affiliate_point_conversion_history.status is 'trạng thái ''SUCCESS'', ''PENDING'', ''FAILED''';

alter table affiliate_point_conversion_history
    owner to root;

grant select, update, usage on sequence affiliate_point_conversion_history_id_seq to member;

grant delete, insert, select, update on affiliate_point_conversion_history to member;

create table user_company_in_sepay
(
    id                 serial
        constraint user_company_in_sepay_pk
            primary key,
    company_id         varchar(20),
    user_id            integer
        constraint user_company_in_sepay_pk_2
            unique
        constraint user_company_in_sepay_users_id_fk
            references users,
    created_at         bigint,
    updated_at         bigint,
    full_name          varchar(255),
    short_name         varchar(255),
    status             varchar(20),
    transaction_amount varchar(20) default 10 not null
);

comment on table user_company_in_sepay is 'bảng này là đại diện company trên sepay-hub đối với mỗi người dùng';

comment on column user_company_in_sepay.company_id is 'mã công ty trên sepay-hub';

comment on column user_company_in_sepay.user_id is 'mã người dùng';

comment on column user_company_in_sepay.created_at is 'thời gian tạo';

comment on column user_company_in_sepay.updated_at is 'thời gian cập nhật';

comment on column user_company_in_sepay.status is 'Active';

comment on column user_company_in_sepay.transaction_amount is 'Số lượt giao dịch khả dụng của công ty';

alter table user_company_in_sepay
    owner to root;

grant select, update, usage on sequence user_company_in_sepay_id_seq to member;

grant delete, insert, select, update on user_company_in_sepay to member;

create table payment_gateway
(
    account_id            varchar(30) not null,
    company_id            integer
        constraint electronic_payment_gateway_user_user_id_fk
            references user_company_in_sepay,
    bank_code             varchar(20),
    account_number        varchar(30),
    identification_number varchar(30),
    phone_number          varchar(20),
    label                 varchar(50),
    status                varchar(30),
    request_id            varchar(100),
    account_holder_name   varchar(50),
    merchant_address      varchar(1000),
    merchant_name         varchar(500),
    id                    serial
        constraint electronic_payment_gateway_pk
            primary key,
    is_va                 boolean,
    main_id               integer,
    va_id                 varchar(30),
    can_create_va         boolean default false
);

comment on table payment_gateway is 'tài khoản ngân hàng liên kết';

comment on column payment_gateway.account_id is 'mã tài khoản ngân hàng trên sepay';

comment on column payment_gateway.bank_code is 'mã ngân hàng';

comment on column payment_gateway.account_number is 'số tài khoản';

comment on column payment_gateway.identification_number is 'căn cước công dân';

comment on column payment_gateway.phone_number is 'số điện thoại đăng ký tài khoản ngân hàng';

comment on column payment_gateway.label is 'nhãn';

comment on column payment_gateway.status is 'trạng thái';

comment on column payment_gateway.request_id is 'mã liên kết';

comment on column payment_gateway.account_holder_name is 'họ tên tài khoản ngân hàng';

comment on column payment_gateway.merchant_address is 'nơi điểm bán';

comment on column payment_gateway.merchant_name is 'tên điểm bán';

comment on column payment_gateway.is_va is 'có phải tài khoản VA hay không';

comment on column payment_gateway.main_id is 'nếu là tài khoản VA thì tài khoản gốc là mã này';

comment on column payment_gateway.va_id is 'mã va';

comment on column payment_gateway.can_create_va is 'tài khoản này có tạo được tài khoản VA hay không';

alter table payment_gateway
    owner to root;

grant select, update, usage on sequence payment_gateway_id_seq to member;

grant delete, insert, select, update on payment_gateway to member;

create table email_server_configurations
(
    id                  serial
        constraint email_server_configurations_pk
            primary key,
    user_id             integer
        constraint email_server_configurations_users_id_fk
            references users,
    server_name         varchar(100),
    host                varchar(255),
    port                integer,
    username            varchar(255),
    password            varchar(255),
    use_ssl             boolean,
    additional_settings json,
    created_at          bigint,
    updated_at          bigint
);

comment on table email_server_configurations is 'Lưu trữ thông tin cấu hình cụ thể cho máy chủ Email (SMTP server).';

comment on column email_server_configurations.user_id is 'Mã người dùng';

comment on column email_server_configurations.server_name is 'Tên hiển thị của cấu hình, ví dụ: “Mailgun Server #1” hoặc “AWS SES”';

comment on column email_server_configurations.host is 'Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…';

comment on column email_server_configurations.port is 'Cổng SMTP, ví dụ: 465, 587, …';

comment on column email_server_configurations.username is 'Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng).';

comment on column email_server_configurations.password is 'Mật khẩu hoặc token xác thực cho SMTP.';

comment on column email_server_configurations.use_ssl is 'Xác định có sử dụng SSL/TLS hay không';

comment on column email_server_configurations.additional_settings is 'Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.';

alter table email_server_configurations
    owner to root;

grant select, update, usage on sequence email_server_configurations_id_seq to member;

grant delete, insert, select, update on email_server_configurations to member;

create table sms_server_configurations
(
    id                  serial
        constraint sms_server_configurations_pk
            primary key,
    user_id             integer
        constraint sms_server_configurations_users_id_fk
            references users,
    provider_name       varchar(100),
    created_at          bigint,
    updated_at          bigint,
    api_key             varchar(255),
    endpoint            varchar(255),
    additional_settings json
);

comment on table sms_server_configurations is 'Lưu trữ thông tin cấu hình cho máy chủ SMS. Thông thường, các nhà cung cấp SMS (Twilio, Vonage, v.v.) cung cấp một API key/token thay vì yêu cầu host/port như SMTP.';

comment on column sms_server_configurations.provider_name is 'Tên nhà cung cấp SMS, ví dụ: “Twilio”, “Vonage”, “Nexmo”, …';

comment on column sms_server_configurations.api_key is 'Khoá (key) hoặc token của nhà cung cấp.';

comment on column sms_server_configurations.endpoint is 'URL endpoint để gọi API (có thể NULL nếu nhà cung cấp đã cố định).';

comment on column sms_server_configurations.additional_settings is 'Lưu các cấu hình tùy biến, ví dụ: “Message Service SID”, “Short Code”, “Alphanumeric Sender ID”, v.v.';

alter table sms_server_configurations
    owner to root;

grant select, update, usage on sequence sms_server_configurations_id_seq to member;

grant delete, insert, select, update on sms_server_configurations to member;

create table products
(
    id               bigserial
        constraint products_pk
            primary key,
    name             varchar(500),
    description      text,
    listed_price     bigint         default 0                       not null,
    created_at       bigint,
    updated_at       bigint,
    images           jsonb          default '[]'::jsonb             not null,
    user_manual      varchar(255),
    detail           varchar(255),
    status           product_status default 'DRAFT'::product_status not null,
    discounted_price bigint         default 0                       not null,
    employee_id      integer
        constraint products_employees_id_fk
            references employees,
    user_id          integer
        constraint products_users_id_fk
            references users,
    category         product_category,
    source_id        uuid
);

comment on table products is 'Bảng sản phẩm trong chợ';

comment on column products.name is 'Tên sản phẩm';

comment on column products.description is 'Mô tả';

comment on column products.listed_price is 'Giá point niêm yết';

comment on column products.created_at is 'Thời gian tạo';

comment on column products.updated_at is 'Thời gian cập nhật';

comment on column products.discounted_price is 'Giá point sau giảm (giá tính tiền)';

comment on column products.source_id is 'id của tài nguyên gốc';

alter table products
    owner to root;

grant select, update, usage on sequence products_id_seq to member;

grant delete, insert, select, update on products to member;

create table carts
(
    id         integer default nextval('cards_id_seq'::regclass) not null
        constraint cards_pk
            primary key,
    user_id    integer
        constraint cards_pk_2
            unique
        constraint cards_users_id_fk
            references users,
    created_at bigint,
    updated_at bigint
);

comment on table carts is 'Giỏ hàng';

comment on column carts.user_id is 'Mã người dùng';

comment on column carts.created_at is 'Thời gian tạo';

comment on column carts.updated_at is 'Thời gian cập nhật';

alter table carts
    owner to root;

grant delete, insert, select, update on carts to member;

create table cart_items
(
    id         serial
        constraint cart_items_pk
            primary key,
    cart_id    integer
        constraint cart_items_cards_id_fk
            references carts,
    product_id integer
        constraint cart_items_products_id_fk
            references products,
    quantity   integer
);

comment on table cart_items is 'Danh sách các sản phẩm trong giỏ hàng';

comment on column cart_items.cart_id is 'Mã giỏ hàng';

comment on column cart_items.product_id is 'Mã sản phẩm';

comment on column cart_items.quantity is 'Số lượng';

alter table cart_items
    owner to root;

grant select, update, usage on sequence cart_items_id_seq to member;

grant delete, insert, select, update on cart_items to member;

create table market_order
(
    id          bigserial
        constraint market_order_pk
            primary key,
    user_id     integer
        constraint market_order_users_id_fk
            references users,
    total_point bigint,
    created_at  bigint,
    updated_at  bigint
);

comment on column market_order.user_id is 'Mã người dùng';

comment on column market_order.total_point is 'Tổng số R-Point thanh toán';

comment on column market_order.created_at is 'Thời giantạo';

comment on column market_order.updated_at is 'Thời gian cập nhật';

alter table market_order
    owner to root;

grant select, update, usage on sequence market_order_id_seq to member;

grant delete, insert, select, update on market_order to member;

create table user_manage_notification
(
    id                            serial
        constraint user_manage_notification_pk
            primary key,
    user_id                       integer
        constraint user_manage_notification_pk_2
            unique
        constraint user_manage_notification_users_id_fk
            references users,
    receive_account_system_emails boolean default true not null,
    receive_billing_emails        boolean default true not null,
    receive_new_feature_emails    boolean default true not null,
    receive_affiliate_emails      boolean default true not null,
    receive_documentation_emails  boolean,
    receive_promotional_emails    boolean default true
);

comment on table user_manage_notification is 'Bảng tùy chỉnh nhận email thông báo của người dùng';

alter table user_manage_notification
    owner to root;

grant select, update, usage on sequence user_manage_notification_id_seq to member;

grant delete, insert, select, update on user_manage_notification to member;

create table rule_contract
(
    id                 serial
        constraint rule_contract_pk
            primary key,
    user_id            integer
        constraint rule_contract_users_id_fk
            references users,
    status             contract_status_enum not null,
    type               contract_type_enum,
    contract_url_pdf   varchar(255),
    created_at         bigint,
    user_signature_at  bigint,
    admin_signature_at bigint,
    reject_reason      text
);

comment on table rule_contract is 'Hợp đồng nguyên tắc';

comment on column rule_contract.user_id is 'Mã người dùng';

comment on column rule_contract.status is 'Trạng thái hợp đồng';

comment on column rule_contract.type is 'Loại hợp đồng';

comment on column rule_contract.contract_url_pdf is 'Đường dẫn hợp đồng';

comment on column rule_contract.created_at is 'Thời gian tạo hợp đồng';

comment on column rule_contract.user_signature_at is 'Thời gian đối tác ký hợp đồng';

comment on column rule_contract.admin_signature_at is 'Thời gian quản trị ký hợp đồng';

comment on column rule_contract.reject_reason is 'Lý do từ chối hợp đồng';

alter table rule_contract
    owner to root;

grant select, update, usage on sequence rule_contract_id_seq to member;

grant delete, insert, select, update on rule_contract to member;

create table blogs
(
    id                 serial
        constraint blogs_pk
            primary key,
    title              varchar(500),
    content            varchar(255),
    point              bigint,
    view_count         bigint,
    thumbnail_url      varchar(255),
    tags               jsonb,
    created_at         bigint,
    updated_at         bigint,
    user_id            integer
        constraint blogs_users_id_fk
            references users,
    employee_id        integer
        constraint blogs_employees_id_fk
            references employees,
    employee_moderator integer
        constraint blogs_employees_id_fk_2
            references employees,
    author_type        varchar(10),
    status             varchar(20)          not null,
    enable             boolean default true not null,
    "like"             bigint,
    description        text
);

comment on table blogs is 'Bảng danh sách bài viết';

comment on column blogs.title is 'Tiêu đề vài viết';

comment on column blogs.content is 'Đường link của file content trên CDN';

comment on column blogs.view_count is 'Số lượt xem';

comment on column blogs.thumbnail_url is 'Ảnh tiêu đề';

comment on column blogs.tags is 'Nhãn';

comment on column blogs.created_at is 'Thời gian tạo';

comment on column blogs.updated_at is 'Thời gian cập nhật';

comment on column blogs.user_id is 'Id của tác giả nếu đây là bài viết của người dùng';

comment on column blogs.employee_id is 'Mã của nhân viên nếu đây là bài viết của hệ thống';

comment on column blogs.employee_moderator is 'nhân viên kiểm duyệt bài viết nếu đây là bài viết của người dùng, nếu là bài viết của hệ thống thì sẽ là null';

comment on column blogs.author_type is 'Loại của tác bài viết ''USER'' hoặc ''SYSTEM''';

comment on column blogs.status is 'Trạng thái của bài viết DRAFT, PENDING, APPROVED';

comment on column blogs.enable is 'Trạng thái của bài viết';

comment on column blogs."like" is 'số lượt like';

alter table blogs
    owner to root;

grant select, update, usage on sequence blogs_id_seq to member;

grant delete, insert, select, update on blogs to member;

create table blog_purchases
(
    id                   serial
        constraint blog_purchases_pk
            primary key,
    user_id              integer
        constraint blog_purchases_users_id_fk
            references users,
    blog_id              integer
        constraint blog_purchases_blogs_id_fk
            references blogs,
    point                bigint,
    purchased_at         bigint,
    platform_fee_percent double precision,
    seller_receive_price bigint
);

comment on table blog_purchases is 'Bảng lưu thông tin mua blog';

comment on column blog_purchases.user_id is 'Mã người mua';

comment on column blog_purchases.blog_id is 'Mã bài viết';

comment on column blog_purchases.point is 'Số point thời điểm mua';

comment on column blog_purchases.purchased_at is 'Thời gian mua hàng';

comment on column blog_purchases.platform_fee_percent is 'Phần trăm phí sàn';

comment on column blog_purchases.seller_receive_price is 'Giá người bán nhận được sau khi trừ phí sàn';

alter table blog_purchases
    owner to root;

grant select, update, usage on sequence blog_purchases_id_seq to member;

grant delete, insert, select, update on blog_purchases to member;

create table blog_comments
(
    id                bigserial
        constraint blog_comments_pk
            primary key,
    blog_id           integer
        constraint blog_comments_blogs_id_fk
            references blogs,
    user_id           integer
        constraint blog_comments_users_id_fk
            references users,
    created_at        bigint,
    content           varchar(1000),
    author_type       varchar(10),
    employee_id       integer
        constraint blog_comments_employees_id_fk
            references employees,
    parent_comment_id bigint
        constraint blog_comments_blog_comments_id_fk
            references blog_comments
);

comment on column blog_comments.blog_id is 'Mã bài viết';

comment on column blog_comments.user_id is 'Mã người dùng nhắn tin';

comment on column blog_comments.created_at is 'Thời gian tạo tin nhắn';

comment on column blog_comments.content is 'Nội dung tin nhắn';

comment on column blog_comments.author_type is 'Loại tài khoản nhắn tin ''USER'' hoặc ''SYSTEM''';

comment on column blog_comments.employee_id is 'Mã nhân viên';

comment on column blog_comments.parent_comment_id is 'Mã của bình luận cha nếu có';

alter table blog_comments
    owner to root;

grant select, update, usage on sequence blog_comments_id_seq to member;

grant delete, insert, select, update on blog_comments to member;

create table user_template_email
(
    id           bigserial
        constraint user_template_email_pk
            primary key,
    content      text,
    tags         jsonb,
    name         varchar(255),
    subject      varchar(255),
    created_at   bigint,
    user_id      integer
        constraint user_template_email_users_id_fk
            references users,
    updated_at   bigint,
    placeholders json
);

comment on table user_template_email is 'Bảng template email của người dùng';

comment on column user_template_email.content is 'Nội dung email';

comment on column user_template_email.tags is 'Nhãn cho email';

comment on column user_template_email.name is 'Tên email';

comment on column user_template_email.subject is 'Tiêu đề email';

comment on column user_template_email.created_at is 'Thời gian tạo';

comment on column user_template_email.updated_at is 'Thời gian cập nhật';

comment on column user_template_email.placeholders is 'Biến truyền vào';

alter table user_template_email
    owner to root;

grant select, update, usage on sequence user_template_email_id_seq to member;

grant delete, insert, select, update on user_template_email to member;

create table user_audience
(
    id         bigserial
        constraint user_audience_pk
            primary key,
    email      varchar(255),
    phone      varchar(20),
    created_at bigint not null,
    updated_at bigint,
    user_id    integer
        constraint user_audience_users_id_fk
            references users
);

comment on table user_audience is 'Bảng khách hàng của người dùng';

comment on column user_audience.email is 'Email người dùng';

comment on column user_audience.phone is 'Số điện thoại';

comment on column user_audience.created_at is 'Ngày tạo';

comment on column user_audience.updated_at is 'Ngày cập nhật';

comment on column user_audience.user_id is 'Mã khách hàng';

alter table user_audience
    owner to root;

grant select, update, usage on sequence user_audience_id_seq to member;

grant delete, insert, select, update on user_audience to member;

create table user_audience_custom_fields
(
    id          bigint default nextval('audience_custom_fields_id_seq'::regclass) not null
        constraint user_audience_custom_fields_pk
            primary key,
    field_name  varchar(255),
    field_value jsonb,
    field_type  varchar(20),
    created_at  bigint,
    updated_at  bigint,
    audience_id integer
        constraint user_audience_custom_fields_user_audience_id_fk
            references user_audience
);

comment on table user_audience_custom_fields is 'Bảng danh sách trường tùy chỉnh của audience';

comment on column user_audience_custom_fields.field_name is 'Tên trường';

comment on column user_audience_custom_fields.field_value is 'Giá trị';

comment on column user_audience_custom_fields.field_type is 'Kiểu giá trị';

comment on column user_audience_custom_fields.created_at is 'Thời gian tạo';

comment on column user_audience_custom_fields.updated_at is 'Thời gian cập nhật';

alter table user_audience_custom_fields
    owner to root;

grant delete, insert, select, update on user_audience_custom_fields to member;

create table user_tags
(
    id         bigserial
        constraint user_tags_pk
            primary key,
    user_id    integer
        constraint user_tags_users_id_fk
            references users,
    name       varchar(255),
    color      varchar(7),
    created_at bigint,
    updated_at bigint
);

alter table user_tags
    owner to root;

grant select, update, usage on sequence user_tags_id_seq to member;

grant delete, insert, select, update on user_tags to member;

create table user_audience_has_tags
(
    audience_id bigint not null
        constraint user_audience_has_tags_user_audience_id_fk
            references user_audience,
    tag_id      bigint not null
        constraint user_audience_has_tags_user_tags_id_fk
            references user_tags,
    constraint user_audience_has_tags_pk
        primary key (audience_id, tag_id)
);

alter table user_audience_has_tags
    owner to root;

grant delete, insert, select, update on user_audience_has_tags to member;

create table user_segments
(
    id          bigserial
        constraint user_segments_pk
            primary key,
    user_id     integer
        constraint user_segments_users_id_fk
            references users,
    name        varchar(255),
    description text,
    criteria    jsonb,
    created_at  bigint,
    updated_at  bigint
);

comment on table user_segments is 'Phân khúc khách hàng của người dùng';

comment on column user_segments.user_id is 'Mã khách hàng';

comment on column user_segments.name is 'Tên tập khách hàng';

comment on column user_segments.description is 'Mô tả';

comment on column user_segments.criteria is 'Lưu trữ điều kiện lọc khách hàng khi tạo segment';

comment on column user_segments.created_at is 'Thời gian tạo';

comment on column user_segments.updated_at is 'Thời gian cập nhật';

alter table user_segments
    owner to root;

grant select, update, usage on sequence user_segments_id_seq to member;

grant delete, insert, select, update on user_segments to member;

create table user_campaigns
(
    id           bigserial
        constraint user_campaigns_pk
            primary key,
    title        varchar(255),
    description  text,
    platform     varchar(255),
    content      text,
    server       jsonb,
    scheduled_at bigint,
    subject      varchar(255),
    created_at   bigint,
    status       varchar(20),
    user_id      integer
        constraint user_campaigns_users_id_fk
            references users,
    updated_at   bigint
);

comment on table user_campaigns is 'Bảng chiến dịch của người dùng';

comment on column user_campaigns.title is 'Tiêu đề';

comment on column user_campaigns.description is 'Mô tả';

comment on column user_campaigns.platform is 'Nền tảng';

comment on column user_campaigns.content is 'Nội dung';

comment on column user_campaigns.server is 'Thông tin máy chủ gửi';

comment on column user_campaigns.scheduled_at is 'Thời gian dự kiến gửi chiến dịch';

comment on column user_campaigns.subject is 'Nội dung tiêu đề với chiến dịch là email ';

comment on column user_campaigns.created_at is 'Ngày tạo';

comment on column user_campaigns.status is 'Trạng thái';

comment on column user_campaigns.user_id is 'Mã người dùng';

comment on column user_campaigns.updated_at is 'Ngày cập nhật';

alter table user_campaigns
    owner to root;

grant select, update, usage on sequence user_campaigns_id_seq to member;

grant delete, insert, select, update on user_campaigns to member;

create table user_campaign_history
(
    id          bigint default nextval('campaign_history_id_seq'::regclass) not null
        constraint user_campaign_history_pk
            primary key,
    campaign_id bigint
        constraint user_campaign_history_user_campaigns_id_fk
            references user_campaigns,
    audience_id bigint
        constraint user_campaign_history_user_audience_id_fk
            references user_audience,
    status      varchar(20),
    sent_at     bigint,
    created_at  bigint
);

comment on table user_campaign_history is 'Bảng lịch sử chăm sóc khách hàng';

comment on column user_campaign_history.campaign_id is 'Mã chiến dịch';

comment on column user_campaign_history.audience_id is 'Mã khách hàng';

comment on column user_campaign_history.status is 'Trạng thái';

comment on column user_campaign_history.sent_at is 'Thời gian gửi';

comment on column user_campaign_history.created_at is 'Thời gian tạo';

alter table user_campaign_history
    owner to root;

grant delete, insert, select, update on user_campaign_history to member;

create table user_memory
(
    id         uuid   default uuid_generate_v4()                                      not null
        primary key,
    user_id    integer                                                                not null
        constraint fk_user
            references users
            on delete cascade,
    fact       text                                                                   not null,
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    embedding  vector(1536)
);

comment on table user_memory is 'Lưu trữ thông tin ghi nhớ (facts) của người dùng với vector embedding để tìm kiếm ngữ nghĩa';

comment on column user_memory.id is 'ID định danh duy nhất cho mỗi ghi nhớ';

comment on column user_memory.user_id is 'ID của người dùng sở hữu ghi nhớ này';

comment on column user_memory.fact is 'Nội dung thông tin ghi nhớ';

comment on column user_memory.created_at is 'Thời điểm ghi nhớ được tạo (epoch milliseconds)';

comment on column user_memory.embedding is 'Vector embedding của nội dung ghi nhớ để hỗ trợ tìm kiếm ngữ nghĩa';

alter table user_memory
    owner to root;

create index idx_user_memory_user_id
    on user_memory (user_id);

create index idx_user_memory_created_at
    on user_memory (created_at);

create index idx_user_memory_embedding
    on user_memory using ivfflat (embedding vector_cosine_ops);

grant delete, insert, select, update on user_memory to member;

create table user_keys
(
    id          integer default nextval('user_configurations_id_seq'::regclass)         not null
        constraint user_configurations_pkey
            primary key,
    user_id     integer                                                                 not null
        constraint user_configurations_user_id_fkey
            references users
            on delete cascade,
    provider_id integer                                                                 not null
        constraint user_configurations_provider_id_fkey
            references providers,
    credentials jsonb   default '{"api_key": null, "api_secret": null}'::jsonb,
    settings    jsonb   default '{"is_active": true, "is_default": false}'::jsonb,
    created_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    constraint user_configurations_user_id_provider_id_key
        unique (user_id, provider_id)
);

comment on table user_keys is 'Cấu hình của người dùng cho nhà cung cấp vận chuyển';

comment on column user_keys.user_id is 'ID người dùng';

comment on column user_keys.provider_id is 'ID nhà cung cấp vận chuyển';

comment on column user_keys.credentials is 'Thông tin xác thực';

comment on column user_keys.settings is 'Cài đặt người dùng';

alter table user_keys
    owner to root;

create index idx_user_configurations_user
    on user_keys (user_id);

create index idx_user_configurations_provider
    on user_keys (provider_id);

create index idx_user_configurations_status
    on user_keys ((settings ->> 'is_active'::text));

grant delete, insert, select, update on user_keys to member;

create table config_finetuning
(
    id         uuid    default uuid_generate_v4()                                      not null
        primary key,
    training   jsonb                                                                   not null,
    validation jsonb                                                                   not null,
    is_active  boolean default false,
    create_by  integer
        references users,
    create_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at  bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table config_finetuning is 'Bảng lưu trữ cấu hình cho quá trình fine-tuning mô hình';

comment on column config_finetuning.id is 'ID cấu hình fine-tuning, là UUID duy nhất';

comment on column config_finetuning.training is 'Dữ liệu huấn luyện và tham số cấu hình';

comment on column config_finetuning.validation is 'Dữ liệu kiểm thử và các metric đánh giá';

comment on column config_finetuning.is_active is 'Trạng thái kích hoạt của cấu hình';

comment on column config_finetuning.create_by is 'ID người tạo cấu hình';

comment on column config_finetuning.create_at is 'Thời điểm tạo cấu hình (milliseconds)';

comment on column config_finetuning.update_at is 'Thời điểm cập nhật cấu hình gần nhất (milliseconds)';

alter table config_finetuning
    owner to root;

create index idx_config_finetuning_is_active
    on config_finetuning (is_active);

create index idx_config_finetuning_create_by
    on config_finetuning (create_by);

create index idx_config_finetuning_create_at
    on config_finetuning (create_at);

create index idx_config_finetuning_training_model
    on config_finetuning ((training ->> 'model_id'::text));

create index idx_config_finetuning_training_dataset
    on config_finetuning ((training ->> 'dataset_id'::text));

grant delete, insert, select, update on config_finetuning to member;

create index idx_vector_store_key_provider_id
    on vector_store_key (provider_id);

create index idx_vector_store_key_create_by
    on vector_store_key (create_by);

create index idx_vector_store_key_update_by
    on vector_store_key (update_by);

create index idx_vector_store_key_storage
    on vector_store_key (storage);

grant delete, insert, select, update on vector_store_key to member;

create table facebook_personal
(
    user_id              integer      not null,
    access_token         varchar(1000),
    facebook_personal_id varchar(255) not null,
    expiration_date      timestamp,
    id                   integer generated always as identity
        constraint facebook_personal_pk
            primary key,
    expiration_date_unix bigint default (EXTRACT(epoch FROM now()) * (1000)::numeric)
);

alter table facebook_personal
    owner to root;

grant select, update, usage on sequence facebook_personal_id_seq to member;

grant delete, insert, select, update on facebook_personal to member;

create table order_plan_history
(
    id            bigserial
        constraint order_plan_history_pk
            primary key,
    plan_name     varchar(255),
    point         bigint default 0 not null,
    user_id       integer
        constraint order_plan_history_users_id_fk
            references users,
    billing_cycle varchar(50),
    usage_limit   bigint,
    usage_unit    varchar(50),
    created_at    bigint           not null
);

comment on table order_plan_history is 'lịch sử đơn hàng gói dịch vụ';

alter table order_plan_history
    owner to root;

grant select, update, usage on sequence order_plan_history_id_seq to member;

grant delete, insert, select, update on order_plan_history to member;

create table url_data
(
    id                uuid    default uuid_generate_v4()                                      not null
        constraint url_resources_pkey
            primary key,
    url               varchar(2048)                                                           not null,
    url_embedding     vector(1536),
    title             varchar(512)                                                            not null,
    title_embedding   vector(1536),
    content           text                                                                    not null,
    content_embedding vector(1536),
    type              varchar(50),
    tags              jsonb,
    owned_by          integer                                                                 not null,
    created_at        bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at        bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    is_active         boolean default true
);

comment on table url_data is 'Bảng lưu thông tin tài nguyên URL';

comment on column url_data.id is 'Mã định danh tài nguyên URL';

comment on column url_data.url is 'Đường dẫn URL';

comment on column url_data.url_embedding is 'Vector embedding cho URL (1536d)';

comment on column url_data.title is 'Tiêu đề của tài nguyên URL';

comment on column url_data.title_embedding is 'Vector embedding cho tiêu đề (1536d)';

comment on column url_data.content is 'Nội dung về tài nguyên URL';

comment on column url_data.content_embedding is 'Vector embedding cho content URL (1536d)';

comment on column url_data.type is 'Loại tài nguyên URL (web, doc, etc.)';

comment on column url_data.tags is 'Các thẻ phân loại URL (dạng JSONB)';

comment on column url_data.owned_by is 'Mã người sở hữu tài nguyên URL (FK tới users)';

comment on column url_data.created_at is 'Thời điểm tạo bản ghi (unix timestamp)';

comment on column url_data.updated_at is 'Thời điểm cập nhật bản ghi (unix timestamp)';

comment on column url_data.is_active is 'Trang thai hoat dong';

alter table url_data
    owner to root;

grant delete, insert, select, update on url_data to member;

create table media_data
(
    id                    uuid         default uuid_generate_v4()                                      not null
        constraint media_resources_pkey
            primary key,
    name                  varchar(255)                                                                 not null,
    description           text                                                                         not null,
    size                  bigint                                                                       not null,
    tags                  jsonb,
    storage_key           varchar(512)                                                                 not null
        constraint media_resources_storage_key_unique
            unique,
    owned_by              integer                                                                      not null,
    created_at            bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    name_embedding        vector(1536),
    description_embedding vector(1536),
    updated_at            bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status                media_status default 'DRAFT'::media_status                                   not null
);

comment on table media_data is 'Bảng lưu thông tin tài nguyên media';

comment on column media_data.id is 'Mã định danh media';

comment on column media_data.name is 'Tên media';

comment on column media_data.description is 'Mô tả về tài nguyên media';

comment on column media_data.size is 'Kích thước media (byte)';

comment on column media_data.tags is 'Các thẻ phân loại media (dạng JSONB)';

comment on column media_data.storage_key is 'Khóa định danh trên hệ thống lưu trữ';

comment on column media_data.owned_by is 'Mã người sở hữu media (FK tới users)';

comment on column media_data.created_at is 'Thời điểm tạo bản ghi (unix timestamp)';

comment on column media_data.name_embedding is 'Vector embedding cho tên media (1536d)';

comment on column media_data.description_embedding is 'Vector embedding cho mô tả media (1536d)';

comment on column media_data.updated_at is 'Thời điểm cập nhật bản ghi (unix timestamp)';

alter table media_data
    owner to root;

grant delete, insert, select, update on media_data to member;

create table knowledge_files
(
    id          uuid                  default uuid_generate_v4()                                      not null
        primary key,
    name        varchar(255)                                                                          not null,
    storage_key varchar(512)                                                                          not null
        unique,
    owner_type  owner_type            default 'USER'::owner_type                                      not null,
    owned_by    integer                                                                               not null,
    is_owner    boolean               default true,
    is_for_sale boolean               default false                                                   not null,
    created_at  bigint                default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    storage     bigint                default 0                                                       not null,
    file_id     varchar(100),
    status      knowledge_file_status default 'PENDING'::knowledge_file_status                        not null,
    source_id   uuid
        constraint knowledge_files_knowledge_files_id_fk
            references knowledge_files
);

comment on table knowledge_files is 'Bảng lưu trữ thông tin về các file tri thức trong hệ thống, bao gồm thông tin về quyền sở hữu và trạng thái bán hàng';

comment on column knowledge_files.id is 'Mã định danh duy nhất cho mỗi file tri thức, tự động tăng';

comment on column knowledge_files.name is 'Tên hiển thị của file tri thức, dùng để hiển thị cho người dùng';

comment on column knowledge_files.storage_key is 'Khóa định danh file trên hệ thống lưu trữ, dùng để truy xuất nội dung file từ storage system';

comment on column knowledge_files.owner_type is 'Loại người sở hữu file: "user" (người dùng thông thường) hoặc "employee" (nhân viên hệ thống)';

comment on column knowledge_files.owned_by is 'ID của người sở hữu file, tham chiếu đến bảng users hoặc employees tùy theo owner_type';

comment on column knowledge_files.is_for_sale is 'Trạng thái đăng bán file trên chợ tri thức: TRUE - đang được đăng bán, FALSE - không đăng bán';

comment on column knowledge_files.created_at is 'Thời điểm tạo bản ghi file, dạng UNIX timestamp với millisecond';

comment on column knowledge_files.storage is 'Dung lượng của file tri thức tính bằng byte, dùng để tính toán không gian lưu trữ và giới hạn';

alter table knowledge_files
    owner to root;

create index idx_knowledge_files_created_at
    on knowledge_files (created_at);

create index idx_knowledge_files_for_sale
    on knowledge_files (is_for_sale)
    where (is_for_sale = true);

create index idx_knowledge_files_name
    on knowledge_files (name);

create index idx_knowledge_files_storage
    on knowledge_files (storage);

create index idx_knowledge_files_status
    on knowledge_files (status);

create index idx_knowledge_files_owner
    on knowledge_files (owner_type, owned_by);

grant delete, insert, select, update on knowledge_files to member;

create table vector_stores
(
    id         varchar(100)                                                           not null
        primary key,
    name       varchar(255)                                                           not null,
    storage    bigint default 0                                                       not null,
    owner_type varchar(20)                                                            not null,
    owner_id   integer                                                                not null,
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    update_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table vector_stores is 'Lưu thông tin các kho vector phục vụ AI/RAG';

comment on column vector_stores.id is 'ID duy nhất (UUID hoặc mã định danh)';

comment on column vector_stores.name is 'Tên vector store';

comment on column vector_stores.storage is 'Dung lượng đã dùng (bytes/tokens)';

comment on column vector_stores.owner_type is 'Loại chủ sở hữu: user hoặc employee';

comment on column vector_stores.owner_id is 'ID của user hoặc employee';

comment on column vector_stores.created_at is 'Thời điểm tạo';

comment on column vector_stores.update_at is 'Thời điểm cập nhật';

alter table vector_stores
    owner to root;

create index idx_vector_stores_owner
    on vector_stores (owner_type, owner_id);

create index idx_vector_stores_name
    on vector_stores (name);

grant delete, insert, select, update on vector_stores to member;

create table vector_store_files
(
    vector_store_id varchar(100) not null
        references vector_stores
            on delete cascade,
    file_id         uuid         not null
        references knowledge_files
            on delete cascade,
    primary key (vector_store_id, file_id)
);

comment on table vector_store_files is 'Bảng trung gian lưu cặp vector_store và file';

comment on column vector_store_files.vector_store_id is 'ID của vector_stores';

comment on column vector_store_files.file_id is 'ID của knowledge_files';

alter table vector_store_files
    owner to root;

create index idx_file_id
    on vector_store_files (file_id);

grant delete, insert, select, update on vector_store_files to member;

create table user_conversation_thread
(
    thread_id  uuid   default uuid_generate_v4()                                      not null
        constraint conversation_thread_pkey
            primary key,
    name       varchar(255)                                                           not null,
    user_id    integer                                                                not null
        constraint fk_user
            references users
            on delete cascade,
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_conversation_thread is 'Lưu trữ thông tin về các cuộc hội thoại của người dùng';

comment on column user_conversation_thread.thread_id is 'ID định danh duy nhất cho mỗi cuộc hội thoại';

comment on column user_conversation_thread.name is 'Tên của cuộc hội thoại';

comment on column user_conversation_thread.user_id is 'ID của người dùng sở hữu cuộc hội thoại';

comment on column user_conversation_thread.created_at is 'Thời điểm cuộc hội thoại được tạo (epoch milliseconds)';

comment on column user_conversation_thread.updated_at is 'Thời điểm cuộc hội thoại được cập nhật lần cuối (epoch milliseconds)';

alter table user_conversation_thread
    owner to root;

create index idx_conversation_thread_user_id
    on user_conversation_thread (user_id);

create index idx_conversation_thread_created_at
    on user_conversation_thread (created_at);

grant delete, insert, select, update on user_conversation_thread to member;

create table user_messages
(
    message_id uuid   default uuid_generate_v4()                                      not null
        constraint messages_pkey
            primary key,
    thread_id  uuid                                                                   not null
        constraint fk_thread
            references user_conversation_thread
            on delete cascade,
    role       varchar(50)                                                            not null
        constraint messages_role_check
            check ((role)::text = ANY
                   (ARRAY [('user'::character varying)::text, ('assistant'::character varying)::text])),
    content    jsonb                                                                  not null,
    timestamp  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_messages is 'Lưu trữ các tin nhắn trong cuộc hội thoại';

comment on column user_messages.message_id is 'ID định danh duy nhất cho mỗi tin nhắn';

comment on column user_messages.thread_id is 'ID của cuộc hội thoại chứa tin nhắn này';

comment on column user_messages.role is 'Vai trò của người gửi tin nhắn (user, assistant, system)';

comment on column user_messages.content is 'Nội dung tin nhắn dạng JSONB để hỗ trợ nhiều loại nội dung (văn bản, hình ảnh, v.v.)';

comment on column user_messages.timestamp is 'Thời điểm tin nhắn được gửi (epoch milliseconds)';

alter table user_messages
    owner to root;

create index idx_message_thread_id
    on user_messages (thread_id);

create index idx_message_timestamp
    on user_messages (timestamp);

create index idx_message_role
    on user_messages (role);

create index idx_message_content
    on user_messages using gin (content);

grant delete, insert, select, update on user_messages to member;

create table base_models
(
    id                      varchar(100)                                                                 not null
        primary key,
    name                    varchar(100)                                                                 not null,
    description             text,
    provider_id             integer
        references providers,
    base_input_rate         integer      default 0,
    base_output_rate        integer      default 0,
    base_train_rate         integer      default 0,
    fine_tuning_input_rate  integer      default 0,
    fine_tuning_output_rate integer      default 0,
    fine_tuning_train_rate  integer      default 0,
    token_count             integer      default 0,
    config                  jsonb        default '{"top_p": true, "function": true, "file_search": true, "temperature": true, "response_format": ["text", "json_object", "json_schema"], "code_interpreter": true, "reasoning_effort": ["low", "medium", "high"]}'::jsonb,
    created_by              integer
        references employees,
    updated_by              integer
        references employees,
    created_at              bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at              bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status                  model_status default 'DRAFT'::model_status                                   not null
);

comment on table base_models is 'Bảng lưu trữ thông tin về các mô hình nền tảng được hỗ trợ trong hệ thống';

comment on column base_models.id is 'ID định danh duy nhất cho mô hình, thường là mã của nhà cung cấp';

comment on column base_models.name is 'Tên hiển thị của mô hình nền tảng';

comment on column base_models.description is 'Mô tả chi tiết về mô hình và khả năng của nó';

comment on column base_models.provider_id is 'ID nhà cung cấp mô hình, tham chiếu đến bảng providers';

comment on column base_models.base_input_rate is 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình cơ bản';

comment on column base_models.base_output_rate is 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình cơ bản';

comment on column base_models.base_train_rate is 'Tỷ lệ tính phí cho huấn luyện với mô hình cơ bản';

comment on column base_models.fine_tuning_input_rate is 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình fine-tuning';

comment on column base_models.fine_tuning_output_rate is 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình fine-tuning';

comment on column base_models.fine_tuning_train_rate is 'Tỷ lệ tính phí cho huấn luyện với mô hình fine-tuning';

comment on column base_models.token_count is 'Số token tối đa mô hình có thể xử lý trong một lần request';

comment on column base_models.config is 'Cấu hình và khả năng của mô hình dạng JSONB';

comment on column base_models.created_by is 'ID nhân viên tạo bản ghi mô hình';

comment on column base_models.updated_by is 'ID nhân viên cập nhật bản ghi mô hình gần nhất';

comment on column base_models.created_at is 'Thời điểm tạo bản ghi (timestamp millis)';

comment on column base_models.updated_at is 'Thời điểm cập nhật bản ghi gần nhất (timestamp millis)';

alter table base_models
    owner to root;

create index idx_base_models_provider_id
    on base_models (provider_id);

create index idx_base_models_name
    on base_models (name);

grant delete, insert, select, update on base_models to member;

create table data_fine_tuning_models
(
    id              uuid default uuid_generate_v4() not null
        primary key,
    train_file      varchar(100),
    validation_file varchar(100),
    method          jsonb,
    metadata        jsonb
);

comment on table data_fine_tuning_models is 'Bảng lưu trữ chi tiết kỹ thuật của mô hình fine-tuning';

comment on column data_fine_tuning_models.id is 'UUID định danh duy nhất cho dữ liệu chi tiết của mô hình fine-tuning';

comment on column data_fine_tuning_models.train_file is 'Đường dẫn hoặc ID file dữ liệu huấn luyện';

comment on column data_fine_tuning_models.validation_file is 'Đường dẫn hoặc ID file dữ liệu kiểm thử';

comment on column data_fine_tuning_models.method is 'Phương pháp fine-tuning được sử dụng (dạng JSONB)';

comment on column data_fine_tuning_models.metadata is 'Thông tin bổ sung về quá trình fine-tuning (dạng JSONB)';

alter table data_fine_tuning_models
    owner to root;

grant delete, insert, select, update on data_fine_tuning_models to member;

create table fine_tuning_models
(
    id            varchar(100)                                                            not null
        primary key,
    name          varchar(64)                                                             not null,
    description   text,
    token         integer default 0,
    status        varchar(50),
    detail_id     uuid
        references data_fine_tuning_models,
    base_model_id varchar(100)
        references base_models,
    owner_type    varchar(50)                                                             not null,
    owner_by      integer                                                                 not null,
    created_at    bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at    bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table fine_tuning_models is 'Bảng quản lý các mô hình đã được fine-tuning từ các mô hình nền tảng';

comment on column fine_tuning_models.id is 'ID định danh duy nhất cho mô hình fine-tuning';

comment on column fine_tuning_models.name is 'Tên hiển thị của mô hình fine-tuning';

comment on column fine_tuning_models.description is 'Mô tả chi tiết về mô hình và mục đích sử dụng';

comment on column fine_tuning_models.token is 'Số lượng token đã sử dụng trong quá trình fine-tuning';

comment on column fine_tuning_models.status is 'Trạng thái hiện tại của mô hình (ví dụ: pending, training, completed, failed)';

comment on column fine_tuning_models.detail_id is 'Tham chiếu đến bảng chi tiết kỹ thuật của mô hình fine-tuning';

comment on column fine_tuning_models.base_model_id is 'ID của mô hình nền tảng được sử dụng để fine-tuning';

comment on column fine_tuning_models.owner_type is 'Loại người sở hữu mô hình (user, organization, system)';

comment on column fine_tuning_models.owner_by is 'ID của người sở hữu mô hình';

comment on column fine_tuning_models.created_at is 'Thời điểm tạo mô hình (timestamp millis)';

comment on column fine_tuning_models.updated_at is 'Thời điểm cập nhật mô hình gần nhất (timestamp millis)';

alter table fine_tuning_models
    owner to root;

create index idx_fine_tuning_models_base_model_id
    on fine_tuning_models (base_model_id);

create index idx_fine_tuning_models_owner
    on fine_tuning_models (owner_type, owner_by);

create index idx_fine_tuning_models_status
    on fine_tuning_models (status);

grant delete, insert, select, update on fine_tuning_models to member;

create table data_fine_tuning
(
    id              uuid                   default uuid_generate_v4()                                      not null
        primary key,
    train_data      jsonb,
    validation_data jsonb,
    owner_by        integer,
    created_at      bigint                 default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at      bigint                 default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    source_id       uuid
        references data_fine_tuning,
    is_for_sale     boolean                default false                                                   not null,
    name            varchar(100),
    status          data_finetuning_status default 'DRAFT'::data_finetuning_status,
    description     text,
    owner_type      owner_type             default 'USER'::owner_type                                      not null
);

comment on table data_fine_tuning is 'Bảng lưu trữ dữ liệu dùng cho quá trình fine-tuning các mô hình';

comment on column data_fine_tuning.id is 'UUID định danh duy nhất cho bộ dữ liệu fine-tuning';

comment on column data_fine_tuning.train_data is 'Dữ liệu huấn luyện dạng JSONB';

comment on column data_fine_tuning.validation_data is 'Dữ liệu kiểm thử dạng JSONB';

comment on column data_fine_tuning.owner_by is 'ID của người sở hữu dữ liệu';

comment on column data_fine_tuning.created_at is 'Thời điểm tạo bộ dữ liệu (timestamp millis)';

comment on column data_fine_tuning.updated_at is 'Thời điểm cập nhật bộ dữ liệu gần nhất (timestamp millis)';

alter table data_fine_tuning
    owner to root;

grant delete, insert, select, update on data_fine_tuning to member;

create table employee_conversation_thread
(
    thread_id       uuid   default uuid_generate_v4()                                      not null
        primary key,
    name            varchar(255)                                                           not null,
    employee_id     integer                                                                not null
        references employees
            on delete cascade,
    created_at      bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at      bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    vector_store_id varchar(255)
        references vector_stores
);

alter table employee_conversation_thread
    owner to root;

grant delete, insert, select, update on employee_conversation_thread to member;

create table employee_messages
(
    message_id uuid   default uuid_generate_v4()                                      not null
        primary key,
    thread_id  uuid                                                                   not null
        references employee_conversation_thread,
    role       varchar(50)                                                            not null,
    content    jsonb                                                                  not null,
    timestamp  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table employee_messages is 'Lưu trữ các tin nhắn trong cuộc hội thoại';

comment on column employee_messages.message_id is 'ID định danh duy nhất cho mỗi tin nhắn';

comment on column employee_messages.thread_id is 'ID của cuộc hội thoại chứa tin nhắn này';

comment on column employee_messages.role is 'Vai trò của người gửi tin nhắn (user, assistant, system)';

comment on column employee_messages.content is 'Nội dung tin nhắn dạng JSONB để hỗ trợ nhiều loại nội dung (văn bản, hình ảnh, v.v.)';

comment on column employee_messages.timestamp is 'Thời điểm tin nhắn được gửi (epoch milliseconds)';

alter table employee_messages
    owner to root;

grant delete, insert, select, update on employee_messages to member;

create table employee_memory
(
    id          uuid   default uuid_generate_v4()                                      not null
        primary key,
    employee_id integer                                                                not null
        references employees
            on delete cascade,
    fact        text                                                                   not null,
    created_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    embedding   vector(1536)
);

alter table employee_memory
    owner to root;

grant delete, insert, select, update on employee_memory to member;

create table api_llm_expense
(
    id           uuid    default uuid_generate_v4()                                      not null
        primary key,
    input_token  integer default 0,
    input_rate   integer default 1,
    output_token integer default 0,
    output_rate  integer default 1,
    cached_token integer default 0,
    cached_rate  integer default 1,
    type         varchar(20),
    metadata     jsonb,
    user_id      integer
        references users,
    create_at    bigint  default ((EXTRACT(epoch FROM now()) + (1000)::numeric))::bigint not null
);

comment on table api_llm_expense is 'Bảng lưu thông tin chi phí sử dụng api_llm của người dùng';

comment on column api_llm_expense.id is 'Mã định danh duy nhất của bản ghi chi phí (UUID)';

comment on column api_llm_expense.input_token is 'Số lượng token đầu vào đã sử dụng';

comment on column api_llm_expense.input_rate is 'Tỷ lệ quy đổi token đầu vào (mặc định là 1)';

comment on column api_llm_expense.output_token is 'Số lượng token đầu ra đã sử dụng';

comment on column api_llm_expense.output_rate is 'Tỷ lệ quy đổi token đầu ra (mặc định là 1)';

comment on column api_llm_expense.cached_token is 'Số lượng token lấy từ cache (ví dụ: từ bộ nhớ đệm AI)';

comment on column api_llm_expense.cached_rate is 'Tỷ lệ quy đổi token cache (mặc định là 1)';

comment on column api_llm_expense.metadata is 'Dữ liệu phụ bổ sung dưới dạng JSONB (ví dụ: loại model, cấu hình...)';

comment on column api_llm_expense.user_id is 'Mã người dùng liên kết, tham chiếu đến bảng users';

comment on column api_llm_expense.create_at is 'Thời điểm tạo bản ghi (UNIX timestamp - milliseconds)';

alter table api_llm_expense
    owner to root;

grant delete, insert, select, update on api_llm_expense to member;

create table routers
(
    id   serial
        primary key,
    name varchar(255) not null,
    path varchar(255) not null
);

comment on table routers is 'Bảng lưu thông tin các route hoặc page trong hệ thống';

comment on column routers.id is 'Khóa chính của bảng routers';

comment on column routers.name is 'Tên của route, dùng để hiển thị hoặc quản lý';

comment on column routers.path is 'Đường dẫn URL tương ứng với route';

alter table routers
    owner to root;

grant select, update, usage on sequence routers_id_seq to member;

grant delete, insert, select, update on routers to member;

create table page_keywords
(
    id        serial
        primary key,
    keywords  jsonb   not null,
    router_id integer not null
        references routers
            on delete cascade,
    user_id   integer not null
        references users
            on delete cascade
);

comment on table page_keywords is 'Bảng lưu từ khóa liên quan đến từng trang (router) và người dùng';

comment on column page_keywords.id is 'Khóa chính của bảng page_keywords, sẽ được AI tự động tạo ra';

comment on column page_keywords.keywords is 'Danh sách từ khóa ở dạng JSON, dùng để chuyển trang';

comment on column page_keywords.router_id is 'Tham chiếu đến bảng routers, xác định trang nào chứa từ khóa này';

comment on column page_keywords.user_id is 'Tham chiếu đến bảng users';

alter table page_keywords
    owner to root;

grant select, update, usage on sequence page_keywords_id_seq to member;

grant delete, insert, select, update on page_keywords to member;

create table admin_marketing_send_history
(
    id                   bigint not null
        constraint admin_marketing_send_history_pk
            primary key,
    send_by              integer
        constraint admin_marketing_send_history_employees_id_fk
            references employees,
    channels             varchar(20),
    message_info_content json,
    "to"                 varchar(255),
    sent_at              bigint,
    error_message        text,
    status               varchar(20)
);

comment on table admin_marketing_send_history is 'Lịch sử chăm sóc khách hàng đơn của admin';

comment on column admin_marketing_send_history.send_by is 'nhân viên gửi tin nhắn';

comment on column admin_marketing_send_history.channels is 'SMS EMAIL ZALO API';

comment on column admin_marketing_send_history.message_info_content is 'Nội dung của tin nhắn';

comment on column admin_marketing_send_history."to" is 'Người nhận';

comment on column admin_marketing_send_history.sent_at is 'Thời gian gửi';

comment on column admin_marketing_send_history.error_message is 'Thông tin lỗi';

comment on column admin_marketing_send_history.status is 'SENT FAILED QUEUED';

alter table admin_marketing_send_history
    owner to root;

grant delete, insert, select, update on admin_marketing_send_history to member;

create table user_marketing_send_history
(
    id                   bigint not null
        primary key,
    send_by              integer
        constraint user_marketing_send_history_employees_id_fk
            references users,
    channels             varchar(20),
    message_info_content json,
    "to"                 varchar(255),
    sent_at              bigint,
    error_message        text,
    audience_id          bigint,
    status               varchar(20)
);

comment on table user_marketing_send_history is 'Lịch sử chăm sóc khách hàng đơn của người dùng';

comment on column user_marketing_send_history.send_by is 'người dùng gửi tin nhắn';

comment on column user_marketing_send_history.channels is 'SMS EMAIL ZALO API';

comment on column user_marketing_send_history.message_info_content is 'Nội dung của tin nhắn';

comment on column user_marketing_send_history."to" is 'Người nhận';

comment on column user_marketing_send_history.sent_at is 'Thời gian gửi';

comment on column user_marketing_send_history.error_message is 'Thông tin lỗi';

comment on column user_marketing_send_history.audience_id is 'Mã audience nếu có';

comment on column user_marketing_send_history.status is 'SENT FAILED QUEUED';

alter table user_marketing_send_history
    owner to root;

grant delete, insert, select, update on user_marketing_send_history to member;

create table rank_strategy
(
    id          integer generated always as identity (cycle)
        primary key,
    image       varchar(255)                                                                         not null,
    name        varchar(255)                                                                         not null,
    description text,
    created_at  bigint               default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint               default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    created_by  integer
        constraint rank_strategy_created_by_fk
            references employees,
    updated_by  integer
        constraint rank_strategy_employees_id_fk
            references employees,
    status      rank_strategy_status default 'APPROVED'::rank_strategy_status                        not null
);

alter table rank_strategy
    owner to root;

create table strategy_agents
(
    id          uuid            default uuid_generate_v4()                                      not null
        primary key,
    name        varchar(255)                                                                    not null,
    description text,
    tags        jsonb,
    created_by  integer                                                                         not null
        references employees,
    updated_by  integer                                                                         not null
        references employees,
    created_at  bigint          default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint          default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status      strategy_status default 'DRAFT'::strategy_status                                not null,
    rank_id     integer
        constraint strategy_agents_rank_strategy_id_fk
            references rank_strategy,
    is_for_sale boolean         default false                                                   not null
);

comment on table strategy_agents is 'Bảng lưu trữ các chiến lược xử lý của agent, định nghĩa cách thức agent tương tác và xử lý yêu cầu';

comment on column strategy_agents.id is 'ID định danh duy nhất cho mỗi chiến lược, tự động tăng';

comment on column strategy_agents.name is 'Tên của chiến lược xử lý, dễ nhớ và mô tả ngắn gọn';

comment on column strategy_agents.description is 'Mô tả chi tiết về chiến lược xử lý, cách thức hoạt động và tình huống áp dụng';

comment on column strategy_agents.tags is 'Danh sách các tag dùng để phân loại và tìm kiếm chiến lược (dạng JSONB)';

comment on column strategy_agents.created_by is 'ID của nhân viên tạo chiến lược, liên kết với bảng employees';

comment on column strategy_agents.updated_by is 'ID của nhân viên cập nhật gần nhất, liên kết với bảng employees';

comment on column strategy_agents.created_at is 'Thời điểm tạo chiến lược, dạng UNIX timestamp với millisecond';

comment on column strategy_agents.updated_at is 'Thời điểm cập nhật cuối cùng, dạng UNIX timestamp với millisecond';

alter table strategy_agents
    owner to root;

create index idx_strategy_agents_created_by
    on strategy_agents (created_by);

create index idx_strategy_agents_name
    on strategy_agents (name);

create index idx_strategy_agents_tags
    on strategy_agents using gin (tags);

grant delete, insert, select, update on strategy_agents to member;

create table strategy_agent_versions
(
    id                 serial
        primary key,
    strategy_agent_id  uuid                                                                            not null
        references strategy_agents,
    version_number     integer                                                                         not null,
    version_name       varchar(255),
    change_description text,
    created_by         integer                                                                         not null
        references employees,
    updated_by         integer                                                                         not null
        references employees,
    created_at         bigint          default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at         bigint          default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status             strategy_status default 'DRAFT'::strategy_status                                not null,
    model_id           varchar(255),
    system_prompt      text,
    model_config       jsonb           default '{"top_p": 1, "temperature": 1}'::jsonb                 not null,
    unique (strategy_agent_id, version_number)
);

comment on table strategy_agent_versions is 'Bảng lưu trữ các phiên bản chính thức của chiến lược agent, do admin quản lý';

comment on column strategy_agent_versions.id is 'ID định danh duy nhất cho mỗi phiên bản chiến lược, tự động tăng';

comment on column strategy_agent_versions.strategy_agent_id is 'ID của chiến lược agent mà phiên bản này thuộc về, tham chiếu đến bảng strategy_agents';

comment on column strategy_agent_versions.version_number is 'Số thứ tự phiên bản, tăng dần theo thời gian, giúp theo dõi tiến trình phát triển';

comment on column strategy_agent_versions.version_name is 'Tên định danh cho phiên bản, dễ nhớ hơn số (ví dụ: "v1.0", "v2.0-beta")';

comment on column strategy_agent_versions.change_description is 'Mô tả những thay đổi so với phiên bản trước đó, giúp theo dõi lịch sử phát triển';

comment on column strategy_agent_versions.created_by is 'ID của nhân viên tạo phiên bản, tham chiếu đến bảng employees';

comment on column strategy_agent_versions.updated_by is 'ID của nhân viên cập nhật gần nhất phiên bản, tham chiếu đến bảng employees';

comment on column strategy_agent_versions.created_at is 'Thời điểm tạo phiên bản, dạng UNIX timestamp với millisecond';

comment on column strategy_agent_versions.updated_at is 'Thời điểm cập nhật cuối cùng, dạng UNIX timestamp với millisecond';

alter table strategy_agent_versions
    owner to root;

grant select, update, usage on sequence strategy_agent_versions_id_seq to member;

create index idx_strategy_agent_versions_strategy_agent_id
    on strategy_agent_versions (strategy_agent_id);

create index idx_strategy_agent_versions_created_by
    on strategy_agent_versions (created_by);

create index idx_strategy_agent_versions_status
    on strategy_agent_versions (status);

grant delete, insert, select, update on strategy_agent_versions to member;

create table strategy_content_steps
(
    id                        serial
        primary key,
    strategy_agent_version_id integer not null
        references strategy_agent_versions
            on delete cascade,
    step_order                integer not null,
    content                   text    not null,
    example                   text    not null,
    unique (strategy_agent_version_id, step_order)
);

comment on table strategy_content_steps is 'Bảng lưu trữ các bước nội dung của phiên bản chiến lược chính thức, bao gồm cả nội dung ẩn và nội dung có thể chỉnh sửa';

comment on column strategy_content_steps.id is 'ID định danh duy nhất cho mỗi bước nội dung, tự động tăng';

comment on column strategy_content_steps.strategy_agent_version_id is 'ID của phiên bản chiến lược agent mà bước này thuộc về, tham chiếu đến bảng strategy_agent_versions';

comment on column strategy_content_steps.step_order is 'Thứ tự của bước trong chuỗi xử lý';

alter table strategy_content_steps
    owner to root;

grant select, update, usage on sequence strategy_content_steps_id_seq to member;

grant delete, insert, select, update on strategy_content_steps to member;

create table user_strategy_agents
(
    id                serial
        primary key,
    user_id           integer                                                                         not null,
    strategy_agent_id uuid                                                                            not null
        references strategy_agents
            on delete cascade,
    purchase_date     bigint          default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status            strategy_status default 'APPROVED'::strategy_status                             not null,
    unique (user_id, strategy_agent_id)
);

comment on table user_strategy_agents is 'Bảng quản lý quyền sử dụng chiến lược agent của người dùng, xác định người dùng nào có quyền truy cập vào chiến lược và phiên bản chính thức nào';

comment on column user_strategy_agents.id is 'ID định danh duy nhất cho mỗi bản ghi quyền sử dụng, tự động tăng';

comment on column user_strategy_agents.user_id is 'ID của người dùng được cấp quyền sử dụng chiến lược, tham chiếu đến bảng users';

comment on column user_strategy_agents.strategy_agent_id is 'ID của chiến lược agent được cấp quyền, tham chiếu đến bảng strategy_agents';

comment on column user_strategy_agents.purchase_date is 'Thời điểm cấp quyền truy cập cho người dùng, dạng UNIX timestamp với millisecond';

alter table user_strategy_agents
    owner to root;

grant select, update, usage on sequence user_strategy_agents_id_seq to member;

create index idx_user_strategy_agents_user_id
    on user_strategy_agents (user_id);

create index idx_user_strategy_agents_strategy_agent_id
    on user_strategy_agents (strategy_agent_id);

create index idx_user_strategy_agents_access_time
    on user_strategy_agents (purchase_date);

grant delete, insert, select, update on user_strategy_agents to member;

create index idx_rank_s3key
    on rank_strategy (description);

grant delete, insert, select, update on rank_strategy to member;

create table ai_providers
(
    id                serial
        primary key,
    provider_key      varchar(255) not null
        unique,
    name              varchar(255) not null,
    icon              varchar(255),
    base_url          varchar(255),
    documentation_url varchar(255),
    status            varchar(25) default 'active'::character varying,
    created_at        bigint      default (EXTRACT(epoch FROM now()))::bigint,
    updated_at        bigint      default (EXTRACT(epoch FROM now()))::bigint
);

comment on table ai_providers is 'Lưu trữ thông tin về các nhà cung cấp AI';

comment on column ai_providers.provider_key is 'Key nội bộ định danh nhà cung cấp (openai, anthropic, gemini, etc)';

comment on column ai_providers.name is 'Tên hiển thị của nhà cung cấp';

comment on column ai_providers.icon is 'Đường dẫn icon của nhà cung cấp';

comment on column ai_providers.base_url is 'URL mặc định để gọi API';

comment on column ai_providers.documentation_url is 'Đường dẫn tài liệu API chính thức';

comment on column ai_providers.status is 'Trạng thái: active, inactive, deprecated';

comment on column ai_providers.created_at is 'Thời điểm tạo (epoch time)';

comment on column ai_providers.updated_at is 'Thời điểm cập nhật (epoch time)';

alter table ai_providers
    owner to root;

grant select, update, usage on sequence ai_providers_id_seq to member;

grant delete, insert, select, update on ai_providers to member;

create table ai_provider_models
(
    id              serial
        primary key,
    provider_id     integer not null
        references ai_providers,
    model_id        text    not null,
    display_name    text    not null,
    type            text    not null,
    capabilities    text[] default '{}'::text[],
    context_window  integer,
    max_tokens      integer,
    training_cutoff text,
    price_input     numeric,
    price_output    numeric,
    status          text   default 'active'::text,
    created_at      bigint default (EXTRACT(epoch FROM now()))::bigint,
    updated_at      bigint default (EXTRACT(epoch FROM now()))::bigint,
    unique (provider_id, model_id)
);

comment on table ai_provider_models is 'Lưu thông tin về các mô hình AI của từng nhà cung cấp';

comment on column ai_provider_models.provider_id is 'ID nhà cung cấp';

comment on column ai_provider_models.model_id is 'ID kỹ thuật của model (ví dụ: gpt-4o)';

comment on column ai_provider_models.display_name is 'Tên hiển thị của model';

comment on column ai_provider_models.type is 'Loại model: chat, vision, embedding, etc.';

comment on column ai_provider_models.capabilities is 'Mảng các khả năng hỗ trợ: image, text, etc.';

comment on column ai_provider_models.context_window is 'Dung lượng context window (tokens)';

comment on column ai_provider_models.max_tokens is 'Số tokens tối đa có thể sinh ra';

comment on column ai_provider_models.training_cutoff is 'Thời điểm cắt dữ liệu huấn luyện';

comment on column ai_provider_models.price_input is 'Giá / 1000 tokens đầu vào';

comment on column ai_provider_models.price_output is 'Giá / 1000 tokens đầu ra';

comment on column ai_provider_models.status is 'Trạng thái: active, deprecated, etc.';

comment on column ai_provider_models.created_at is 'Thời điểm tạo (epoch time)';

comment on column ai_provider_models.updated_at is 'Thời điểm cập nhật (epoch time)';

alter table ai_provider_models
    owner to root;

grant select, update, usage on sequence ai_provider_models_id_seq to member;

grant delete, insert, select, update on ai_provider_models to member;

create table ai_provider_configs
(
    id                serial
        primary key,
    user_id           integer
        references users,
    provider_id       integer not null
        references ai_providers,
    name              text    not null,
    is_default        boolean default false,
    api_key           text    not null,
    organization_id   text,
    project_id        text,
    additional_config jsonb   default '{}'::jsonb,
    created_at        bigint  default (EXTRACT(epoch FROM now()))::bigint,
    updated_at        bigint  default (EXTRACT(epoch FROM now()))::bigint
);

comment on table ai_provider_configs is 'Lưu trữ cấu hình API riêng cho từng người dùng hoặc hệ thống';

comment on column ai_provider_configs.user_id is 'Người dùng sở hữu cấu hình (NULL nếu là system)';

comment on column ai_provider_configs.provider_id is 'ID nhà cung cấp tương ứng';

comment on column ai_provider_configs.name is 'Tên cấu hình do người dùng đặt';

comment on column ai_provider_configs.is_default is 'Đánh dấu đây là cấu hình mặc định hay không';

comment on column ai_provider_configs.api_key is 'API key (nên được mã hóa trước khi lưu)';

comment on column ai_provider_configs.organization_id is 'Trường bổ sung (dùng cho OpenAI)';

comment on column ai_provider_configs.project_id is 'Trường bổ sung (dùng cho Google, etc)';

comment on column ai_provider_configs.additional_config is 'Các cấu hình bổ sung đặc thù của provider';

comment on column ai_provider_configs.created_at is 'Thời điểm tạo (epoch time)';

comment on column ai_provider_configs.updated_at is 'Thời điểm cập nhật (epoch time)';

alter table ai_provider_configs
    owner to root;

grant select, update, usage on sequence ai_provider_configs_id_seq to member;

grant delete, insert, select, update on ai_provider_configs to member;

create table ai_provider_usage
(
    id            serial
        primary key,
    user_id       integer not null
        references users,
    provider_id   integer not null
        references ai_providers,
    config_id     integer not null
        references ai_provider_configs,
    model_id      text    not null,
    request_type  text    not null,
    tokens_input  integer default 0,
    tokens_output integer default 0,
    cost          numeric default 0,
    request_id    text,
    metadata      jsonb   default '{}'::jsonb,
    created_at    bigint  default (EXTRACT(epoch FROM now()))::bigint
);

comment on table ai_provider_usage is 'Lưu lịch sử sử dụng API của người dùng';

comment on column ai_provider_usage.user_id is 'ID người dùng thực hiện request';

comment on column ai_provider_usage.provider_id is 'Nhà cung cấp được dùng';

comment on column ai_provider_usage.config_id is 'Cấu hình sử dụng để gọi API';

comment on column ai_provider_usage.model_id is 'Model cụ thể được gọi';

comment on column ai_provider_usage.request_type is 'Loại request: chat, image, audio...';

comment on column ai_provider_usage.tokens_input is 'Số tokens đầu vào';

comment on column ai_provider_usage.tokens_output is 'Số tokens đầu ra';

comment on column ai_provider_usage.cost is 'Chi phí request (đơn vị tiền)';

comment on column ai_provider_usage.request_id is 'ID của request trong hệ thống gốc (nếu có)';

comment on column ai_provider_usage.metadata is 'Thông tin bổ sung của request (JSON)';

comment on column ai_provider_usage.created_at is 'Thời điểm request (epoch time)';

alter table ai_provider_usage
    owner to root;

grant select, update, usage on sequence ai_provider_usage_id_seq to member;

grant delete, insert, select, update on ai_provider_usage to member;

create table document_chunks
(
    id                   uuid   default uuid_generate_v4()                                      not null
        primary key,
    knowledge_file_id    uuid                                                                   not null
        references knowledge_files
            on delete cascade,
    chunk_index          integer                                                                not null,
    content              text                                                                   not null,
    chunk_metadata       jsonb  default '{}'::jsonb,
    embedding            vector,
    created_at           bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at           bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    user_id              integer                                                                not null
        constraint document_chunks_users_id_fk
            references users,
    embedding_source     text   default 'openai'::character varying,
    embedding_dimensions integer
);

comment on table document_chunks is 'Bảng lưu trữ các phân mảnh nội dung của tài liệu sau khi xử lý';

comment on column document_chunks.id is 'ID duy nhất của phân mảnh tài liệu';

comment on column document_chunks.knowledge_file_id is 'ID của tài liệu gốc';

comment on column document_chunks.chunk_index is 'Vị trí của phân mảnh trong tài liệu gốc';

comment on column document_chunks.content is 'Nội dung văn bản của phân mảnh';

comment on column document_chunks.chunk_metadata is 'Metadata bổ sung về phân mảnh dưới dạng JSON';

comment on column document_chunks.embedding is 'Vector embedding của nội dung phân mảnh (chiều linh hoạt)';

comment on column document_chunks.created_at is 'Thời điểm tạo phân mảnh (milliseconds)';

comment on column document_chunks.updated_at is 'Thời điểm cập nhật phân mảnh gần nhất (milliseconds)';

comment on column document_chunks.embedding_dimensions is 'Số chiều thực tế của vector embedding';

alter table document_chunks
    owner to root;

create index idx_document_chunks_knowledge_file_id
    on document_chunks (knowledge_file_id);

create index idx_document_chunks_created_at
    on document_chunks (created_at);

create index idx_document_chunks_chunk_index
    on document_chunks (chunk_index);

grant delete, insert, select, update on document_chunks to member;

create table checkpoint_migrations
(
    v integer not null
        primary key
);

alter table checkpoint_migrations
    owner to root;

grant delete, insert, select, update on checkpoint_migrations to member;

create table checkpoints
(
    thread_id            text                      not null,
    checkpoint_ns        text  default ''::text    not null,
    checkpoint_id        text                      not null,
    parent_checkpoint_id text,
    type                 text,
    checkpoint           jsonb                     not null,
    metadata             jsonb default '{}'::jsonb not null,
    primary key (thread_id, checkpoint_ns, checkpoint_id)
);

alter table checkpoints
    owner to root;

grant delete, insert, select, update on checkpoints to member;

create table checkpoint_blobs
(
    thread_id     text                  not null,
    checkpoint_ns text default ''::text not null,
    channel       text                  not null,
    version       text                  not null,
    type          text                  not null,
    blob          bytea,
    primary key (thread_id, checkpoint_ns, channel, version)
);

alter table checkpoint_blobs
    owner to root;

grant delete, insert, select, update on checkpoint_blobs to member;

create table checkpoint_writes
(
    thread_id     text                  not null,
    checkpoint_ns text default ''::text not null,
    checkpoint_id text                  not null,
    task_id       text                  not null,
    idx           integer               not null,
    channel       text                  not null,
    type          text,
    blob          bytea                 not null,
    primary key (thread_id, checkpoint_ns, checkpoint_id, task_id, idx)
);

alter table checkpoint_writes
    owner to root;

grant delete, insert, select, update on checkpoint_writes to member;

create table google_user_auth
(
    id             serial
        primary key,
    user_id        integer not null
        constraint fk_user
            references users
            on delete cascade,
    google_user_id varchar(255),
    email          varchar(255),
    access_token   text    not null,
    refresh_token  text    not null,
    expires_at     bigint  not null,
    scopes         text[]  default '{}'::text[],
    is_active      boolean default true,
    created_at     bigint  default (EXTRACT(epoch FROM now()))::bigint,
    updated_at     bigint  default (EXTRACT(epoch FROM now()))::bigint
);

alter table google_user_auth
    owner to root;

grant select, update, usage on sequence google_user_auth_id_seq to member;

create index idx_google_user_auth_user_id
    on google_user_auth (user_id);

create index idx_google_user_auth_email
    on google_user_auth (email);

grant delete, insert, select, update on google_user_auth to member;

create table custom_fields
(
    id          serial
        primary key,
    component   varchar(255)                                                                        not null,
    config_id   varchar(255)                                                                        not null
        unique,
    label       varchar(255)                                                                        not null,
    type        varchar(50)                                                                         not null,
    required    boolean                                                                             not null,
    config_json jsonb                                                                               not null,
    employee_id integer
        references employees,
    user_id     integer
        references users,
    create_at   bigint              default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status      custom_field_status default 'PENDING'::custom_field_status                          not null
);

alter table custom_fields
    owner to root;

grant select, update, usage on sequence custom_fields_id_seq to member;

create index idx_config_id
    on custom_fields (config_id);

create index idx_type
    on custom_fields (type);

grant delete, insert, select, update on custom_fields to member;

create table invoice
(
    id               bigserial
        constraint invoice_pk
            primary key,
    order_id         bigserial
        constraint invoice_point_purchase_transactions_id_fk
            references point_purchase_transactions,
    invoice_path_pdf varchar(255),
    inv_pattern      varchar(20),
    inv_serial       varchar(20),
    created_at       bigint,
    updated_at       bigint,
    buyer_full_name  varchar(255),
    company_name     varchar(255),
    tax_code         varchar(20),
    address          varchar(500),
    payment_method   varchar(50),
    currency         varchar(10),
    account_number   varchar(50),
    bank_code        varchar(20),
    exchange_rate    double precision,
    status           varchar(20),
    vat_amount       double precision,
    item_name        varchar(255),
    unit_of_measure  integer,
    quantity         integer,
    unit_price       double precision,
    amount           double precision,
    vat_rate         double precision,
    total_amount     double precision
);

comment on table invoice is 'hóa đơn mua point';

comment on column invoice.order_id is 'mã đơn hàng';

comment on column invoice.invoice_path_pdf is 'Đường link PDF của hóa đơn';

comment on column invoice.inv_pattern is 'Mẫu số hóa đơn';

comment on column invoice.inv_serial is 'Mẫu số ký hiệu';

comment on column invoice.created_at is 'Thời điểm tạo hóa đơn (Unix timestamp)';

comment on column invoice.updated_at is 'Thời điểm cập nhật gần nhất (Unix timestamp)';

comment on column invoice.buyer_full_name is 'Họ tên người mua hàng';

comment on column invoice.company_name is 'Tên đơn vị (doanh nghiệp)';

comment on column invoice.tax_code is 'Mã số thuế của đơn vị mua hàng';

comment on column invoice.address is 'Địa chỉ người mua';

comment on column invoice.payment_method is 'Hình thức thanh toán (tiền mặt, chuyển khoản,...)';

comment on column invoice.currency is 'Đơn vị tiền tệ sử dụng trong hóa đơn (VD: VND, USD)';

comment on column invoice.account_number is 'Tài khoản ngân hàng của người mua (nếu có)';

comment on column invoice.bank_code is 'mã bank code';

comment on column invoice.exchange_rate is 'Tỷ giá nếu dùng đơn vị tiền tệ khác VND';

comment on column invoice.status is 'Trạng thái hóa đơn: PENDING (chưa xuất), ISSUED (đã xuất)';

comment on column invoice.vat_amount is 'Số tiền thuế GTGT phải nộp';

comment on column invoice.item_name is 'Tên hàng hóa hoặc dịch vụ';

comment on column invoice.unit_of_measure is 'Đơn vị tính (VD: cái, kg, giờ...)';

comment on column invoice.quantity is 'Số lượng hàng hóa/dịch vụ';

comment on column invoice.unit_price is 'Đơn giá của từng đơn vị sản phẩm';

comment on column invoice.amount is 'Thành tiền (subtotal: quantity * unit_price)';

comment on column invoice.vat_rate is 'Thuế suất giá trị gia tăng (GTGT) theo %';

comment on column invoice.total_amount is 'Tổng tiền thanh toán (đã bao gồm VAT)';

alter table invoice
    owner to root;

grant select, update, usage on sequence invoice_id_seq to member;

grant select, update, usage on sequence invoice_order_id_seq to member;

grant delete, insert, select, update on invoice to member;

create table user_products
(
    id                    bigserial
        primary key,
    name                  varchar(255)                                                                                           not null,
    name_embedding        vector(1536),
    price                 jsonb                                                                                                  not null,
    type_price            price_type          default 'HAS_PRICE'::price_type                                                    not null,
    description           text,
    description_embedding vector(1536),
    images                jsonb,
    tags                  jsonb,
    tags_embedding        vector(1536),
    created_by            integer
        references users,
    created_at            bigint              default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint                    not null,
    updated_at            bigint              default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint                    not null,
    shipment_config       jsonb               default '{"widthCm": 25, "heightCm": 5, "lengthCm": 30, "weightGram": 200}'::jsonb not null,
    status                user_product_status default 'DRAFT'::user_product_status                                               not null
);

comment on table user_products is 'Bảng quản lý sản phẩm của người dùng';

comment on column user_products.name is 'Tên sản phẩm';

comment on column user_products.name_embedding is 'Vector nhúng từ tên sản phẩm';

comment on column user_products.price is 'Giá sản phẩm (dưới dạng JSON)';

comment on column user_products.type_price is 'Kiểu giá (ví dụ: cố định, theo giờ...)';

comment on column user_products.description is 'Mô tả sản phẩm';

comment on column user_products.description_embedding is 'Vector nhúng mô tả';

comment on column user_products.images is 'URL hình ảnh sản phẩm';

comment on column user_products.tags is 'Các tag mô tả sản phẩm';

comment on column user_products.tags_embedding is 'Vector nhúng từ tag';

comment on column user_products.created_by is 'Người tạo sản phẩm';

comment on column user_products.created_at is 'Thời gian tạo (millis)';

comment on column user_products.updated_at is 'Thời gian cập nhật (millis)';

alter table user_products
    owner to root;

grant select, update, usage on sequence user_products_id_seq to member;

create table custom_group_form
(
    id          serial
        primary key,
    label       varchar(255)                                                           not null,
    employee_id integer
        references employees,
    user_id     integer
        references users,
    create_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    product_id  integer
        constraint custom_group_form_user_products_id_fk
            references user_products
);

alter table custom_group_form
    owner to root;

grant select, update, usage on sequence custom_group_form_id_seq to member;

grant delete, insert, select, update on custom_group_form to member;

create table custom_group_form_field
(
    field_id      integer
        references custom_fields,
    form_group_id integer
        references custom_group_form,
    gird          jsonb,
    value         jsonb default '{"value": "example"}'::jsonb not null
);

alter table custom_group_form_field
    owner to root;

create index idx_field_id
    on custom_group_form_field (field_id);

create index idx_custom_form_id
    on custom_group_form_field (form_group_id);

grant delete, insert, select, update on custom_group_form_field to member;

create index idx_user_products_created_by
    on user_products (created_by);

grant delete, insert, select, update on user_products to member;

create table warehouse
(
    warehouse_id serial
        primary key,
    name         varchar(100)                                      not null,
    description  text,
    type         warehouse_type default 'PHYSICAL'::warehouse_type not null
);

alter table warehouse
    owner to root;

grant select, update, usage on sequence warehouse_warehouse_id_seq to member;

grant delete, insert, select, update on warehouse to member;

create table physical_warehouse
(
    warehouse_id integer      not null
        constraint physicalwarehouse_pkey
            primary key
        constraint physicalwarehouse_warehouse_id_fkey
            references warehouse,
    address      varchar(255) not null,
    capacity     integer
);

alter table physical_warehouse
    owner to root;

grant delete, insert, select, update on physical_warehouse to member;

create table virtual_warehouse
(
    warehouse_id      integer not null
        constraint virtualwarehouse_pkey
            primary key
        constraint virtualwarehouse_warehouse_id_fkey
            references warehouse,
    associated_system varchar(100),
    purpose           text
);

alter table virtual_warehouse
    owner to root;

grant delete, insert, select, update on virtual_warehouse to member;

create table warehouse_custom_field
(
    warehouse_id integer                                     not null
        references warehouse,
    field_id     integer                                     not null
        references custom_fields,
    value        jsonb default '{"value": "example"}'::jsonb not null,
    primary key (warehouse_id, field_id)
);

alter table warehouse_custom_field
    owner to root;

grant delete, insert, select, update on warehouse_custom_field to member;

create table suppliers
(
    id           bigserial
        primary key,
    name         varchar(100)                                                           not null,
    contact_info jsonb,
    address      varchar(255),
    created_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at   bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table suppliers is 'Lưu trữ thông tin nhà cung cấp';

comment on column suppliers.id is 'ID nhà cung cấp';

comment on column suppliers.name is 'Tên nhà cung cấp';

comment on column suppliers.contact_info is 'Thông tin liên hệ (số điện thoại, email, v.v.) dưới dạng JSON';

comment on column suppliers.address is 'Địa chỉ nhà cung cấp';

comment on column suppliers.created_at is 'Thời gian tạo bản ghi';

comment on column suppliers.updated_at is 'Thời gian cập nhật bản ghi';

alter table suppliers
    owner to root;

grant select, update, usage on sequence suppliers_id_seq to member;

grant delete, insert, select, update on suppliers to member;

create table inventory
(
    id                 bigserial
        primary key,
    product_id         bigint                                                                  not null,
    warehouse_id       integer
        references warehouse,
    current_quantity   integer default 0                                                       not null,
    total_quantity     integer default 0                                                       not null,
    available_quantity integer default 0                                                       not null,
    reserved_quantity  integer default 0                                                       not null,
    defective_quantity integer default 0                                                       not null,
    last_updated       bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table inventory is 'Quản lý số lượng tồn kho hiện tại với chi tiết số lượng';

comment on column inventory.id is 'ID bản ghi tồn kho';

comment on column inventory.product_id is 'ID sản phẩm';

comment on column inventory.warehouse_id is 'ID kho chứa sản phẩm';

comment on column inventory.current_quantity is 'Số lượng hiện tại trong kho';

comment on column inventory.total_quantity is 'Tổng số lượng đã nhập vào kho (bao gồm cả đã xuất)';

comment on column inventory.available_quantity is 'Số lượng sẵn sàng để bán hoặc sử dụng';

comment on column inventory.reserved_quantity is 'Số lượng bị giữ chỗ (ví dụ: cho đơn hàng chưa hoàn tất)';

comment on column inventory.defective_quantity is 'Số lượng sản phẩm lỗi hoặc không sử dụng được';

comment on column inventory.last_updated is 'Thời gian cập nhật tồn kho gần nhất';

alter table inventory
    owner to root;

grant select, update, usage on sequence inventory_id_seq to member;

grant delete, insert, select, update on inventory to member;

create table folders
(
    id         serial
        primary key,
    name       varchar(255)                                                           not null,
    parent_id  integer
        references folders
            on delete cascade,
    user_id    integer                                                                not null
        references users
            on delete cascade,
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    path       varchar(1000),
    root       integer
        constraint folders_virtual_warehouse_warehouse_id_fk
            references virtual_warehouse
);

alter table folders
    owner to root;

grant select, update, usage on sequence folders_id_seq to member;

grant delete, insert, select, update on folders to member;

create table files
(
    id          serial
        primary key,
    name        varchar(255)                                                           not null,
    folder_id   integer                                                                not null
        references folders
            on delete cascade,
    size        bigint default 0,
    created_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    storage_key varchar(255)                                                           not null
);

alter table files
    owner to root;

grant select, update, usage on sequence files_id_seq to member;

grant delete, insert, select, update on files to member;

create table user_classifications
(
    id         serial
        primary key,
    type       text not null,
    price      jsonb,
    product_id integer
        references user_products
);

alter table user_classifications
    owner to root;

grant select, update, usage on sequence user_classifications_id_seq to member;

grant delete, insert, select, update on user_classifications to member;

create table custom_field_classifications
(
    classification_id integer
        references user_classifications,
    custom_field_id   integer
        references custom_fields,
    value             jsonb default '{"value": ""}'::jsonb not null
);

alter table custom_field_classifications
    owner to root;

grant delete, insert, select, update on custom_field_classifications to member;

create table zalo_official_accounts
(
    id            serial
        primary key,
    user_id       integer                                                 not null,
    oa_id         varchar(50)                                             not null,
    name          varchar(255)                                            not null,
    description   varchar(500),
    avatar_url    varchar(500),
    access_token  varchar(500)                                            not null,
    refresh_token varchar(500),
    expires_at    bigint                                                  not null,
    agent_id      integer,
    status        varchar(20) default 'active'::character varying,
    created_at    bigint      default (EXTRACT(epoch FROM now()))::bigint not null,
    updated_at    bigint      default (EXTRACT(epoch FROM now()))::bigint not null,
    unique (user_id, oa_id)
);

comment on table zalo_official_accounts is 'Lưu trữ thông tin về các Official Account của Zalo mà người dùng đã kết nối với hệ thống';

comment on column zalo_official_accounts.id is 'ID tự động tăng';

comment on column zalo_official_accounts.user_id is 'ID người dùng sở hữu Official Account';

comment on column zalo_official_accounts.oa_id is 'ID của Official Account trên Zalo';

comment on column zalo_official_accounts.name is 'Tên của Official Account';

comment on column zalo_official_accounts.description is 'Mô tả của Official Account';

comment on column zalo_official_accounts.avatar_url is 'URL avatar của Official Account';

comment on column zalo_official_accounts.access_token is 'Access token của Official Account';

comment on column zalo_official_accounts.refresh_token is 'Refresh token của Official Account';

comment on column zalo_official_accounts.expires_at is 'Thời gian hết hạn của access token (Unix timestamp)';

comment on column zalo_official_accounts.agent_id is 'ID của agent được kết nối với Official Account';

comment on column zalo_official_accounts.status is 'Trạng thái kết nối (active, inactive)';

comment on column zalo_official_accounts.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column zalo_official_accounts.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table zalo_official_accounts
    owner to root;

grant select, update, usage on sequence zalo_official_accounts_id_seq to member;

create index idx_zalo_oa_user_id
    on zalo_official_accounts (user_id);

create index idx_zalo_oa_agent_id
    on zalo_official_accounts (agent_id);

grant delete, insert, select, update on zalo_official_accounts to member;

create table zalo_zns_templates
(
    id               serial
        primary key,
    user_id          integer                                            not null,
    oa_id            varchar(50)                                        not null,
    template_id      varchar(50)                                        not null,
    template_name    varchar(255)                                       not null,
    template_content text                                               not null,
    params           jsonb  default '[]'::jsonb,
    status           varchar(20)                                        not null,
    created_at       bigint default (EXTRACT(epoch FROM now()))::bigint not null,
    updated_at       bigint default (EXTRACT(epoch FROM now()))::bigint not null,
    unique (oa_id, template_id)
);

comment on table zalo_zns_templates is 'Lưu trữ thông tin về các template ZNS (Zalo Notification Service) mà người dùng đã tạo hoặc được cấp quyền sử dụng';

comment on column zalo_zns_templates.id is 'ID tự động tăng';

comment on column zalo_zns_templates.user_id is 'ID người dùng sở hữu template';

comment on column zalo_zns_templates.oa_id is 'ID của Official Account';

comment on column zalo_zns_templates.template_id is 'ID của template trên Zalo';

comment on column zalo_zns_templates.template_name is 'Tên của template';

comment on column zalo_zns_templates.template_content is 'Nội dung của template';

comment on column zalo_zns_templates.params is 'Các tham số của template (JSON)';

comment on column zalo_zns_templates.status is 'Trạng thái của template (approved, pending, rejected)';

comment on column zalo_zns_templates.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column zalo_zns_templates.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table zalo_zns_templates
    owner to root;

grant select, update, usage on sequence zalo_zns_templates_id_seq to member;

create index idx_zalo_zns_templates_user_id
    on zalo_zns_templates (user_id);

create index idx_zalo_zns_templates_oa_id
    on zalo_zns_templates (oa_id);

grant delete, insert, select, update on zalo_zns_templates to member;

create table zalo_messages
(
    id           serial
        primary key,
    oa_id        varchar(50)                                        not null,
    user_id      varchar(50)                                        not null,
    message_id   varchar(50),
    message_type varchar(20)                                        not null,
    content      text,
    data         jsonb,
    direction    varchar(10)                                        not null,
    agent_id     integer,
    timestamp    bigint                                             not null,
    created_at   bigint default (EXTRACT(epoch FROM now()))::bigint not null
);

comment on table zalo_messages is 'Lưu trữ lịch sử tin nhắn giữa người dùng Zalo và Official Account';

comment on column zalo_messages.id is 'ID tự động tăng';

comment on column zalo_messages.oa_id is 'ID của Official Account';

comment on column zalo_messages.user_id is 'ID của người dùng Zalo';

comment on column zalo_messages.message_id is 'ID của tin nhắn trên Zalo';

comment on column zalo_messages.message_type is 'Loại tin nhắn (text, image, file, template)';

comment on column zalo_messages.content is 'Nội dung tin nhắn';

comment on column zalo_messages.data is 'Dữ liệu bổ sung của tin nhắn (JSON)';

comment on column zalo_messages.direction is 'Hướng tin nhắn (incoming, outgoing)';

comment on column zalo_messages.agent_id is 'ID của agent xử lý tin nhắn';

comment on column zalo_messages.timestamp is 'Thời điểm gửi/nhận (Unix timestamp)';

comment on column zalo_messages.created_at is 'Thời điểm tạo bản ghi (Unix timestamp)';

alter table zalo_messages
    owner to root;

grant select, update, usage on sequence zalo_messages_id_seq to member;

create index idx_zalo_messages_oa_id
    on zalo_messages (oa_id);

create index idx_zalo_messages_user_id
    on zalo_messages (user_id);

create index idx_zalo_messages_timestamp
    on zalo_messages (timestamp);

create index idx_zalo_messages_agent_id
    on zalo_messages (agent_id);

grant delete, insert, select, update on zalo_messages to member;

create table zalo_zns_messages
(
    id             serial
        primary key,
    user_id        integer                                                 not null,
    oa_id          varchar(50)                                             not null,
    template_id    varchar(50)                                             not null,
    phone          varchar(20)                                             not null,
    message_id     varchar(50),
    tracking_id    varchar(50)                                             not null,
    template_data  jsonb                                                   not null,
    status         varchar(20) default 'pending'::character varying,
    delivered_time bigint,
    created_at     bigint      default (EXTRACT(epoch FROM now()))::bigint not null,
    updated_at     bigint      default (EXTRACT(epoch FROM now()))::bigint not null
);

comment on table zalo_zns_messages is 'Lưu trữ lịch sử các tin nhắn ZNS đã gửi';

comment on column zalo_zns_messages.id is 'ID tự động tăng';

comment on column zalo_zns_messages.user_id is 'ID người dùng gửi tin nhắn';

comment on column zalo_zns_messages.oa_id is 'ID của Official Account';

comment on column zalo_zns_messages.template_id is 'ID của template';

comment on column zalo_zns_messages.phone is 'Số điện thoại người nhận';

comment on column zalo_zns_messages.message_id is 'ID của tin nhắn trên Zalo';

comment on column zalo_zns_messages.tracking_id is 'ID giao dịch';

comment on column zalo_zns_messages.template_data is 'Dữ liệu đã gửi cho template (JSON)';

comment on column zalo_zns_messages.status is 'Trạng thái tin nhắn (pending, delivered, failed)';

comment on column zalo_zns_messages.delivered_time is 'Thời điểm gửi thành công (Unix timestamp)';

comment on column zalo_zns_messages.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column zalo_zns_messages.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table zalo_zns_messages
    owner to root;

grant select, update, usage on sequence zalo_zns_messages_id_seq to member;

create index idx_zalo_zns_messages_user_id
    on zalo_zns_messages (user_id);

create index idx_zalo_zns_messages_oa_id
    on zalo_zns_messages (oa_id);

create index idx_zalo_zns_messages_phone
    on zalo_zns_messages (phone);

create index idx_zalo_zns_messages_status
    on zalo_zns_messages (status);

grant delete, insert, select, update on zalo_zns_messages to member;

create table zalo_followers
(
    id            serial
        primary key,
    oa_id         varchar(50)                                             not null,
    user_id       varchar(50)                                             not null,
    display_name  varchar(255),
    avatar_url    varchar(500),
    phone         varchar(20),
    gender        smallint,
    birth_date    varchar(20),
    tags          jsonb       default '[]'::jsonb,
    followed_at   bigint                                                  not null,
    unfollowed_at bigint,
    status        varchar(20) default 'active'::character varying,
    created_at    bigint      default (EXTRACT(epoch FROM now()))::bigint not null,
    updated_at    bigint      default (EXTRACT(epoch FROM now()))::bigint not null,
    unique (oa_id, user_id)
);

comment on table zalo_followers is 'Lưu trữ thông tin về người dùng Zalo đã theo dõi Official Account';

comment on column zalo_followers.id is 'ID tự động tăng';

comment on column zalo_followers.oa_id is 'ID của Official Account';

comment on column zalo_followers.user_id is 'ID của người dùng Zalo';

comment on column zalo_followers.display_name is 'Tên hiển thị của người dùng';

comment on column zalo_followers.avatar_url is 'URL avatar của người dùng';

comment on column zalo_followers.phone is 'Số điện thoại của người dùng (nếu được cấp quyền)';

comment on column zalo_followers.gender is 'Giới tính của người dùng (1: Nam, 2: Nữ)';

comment on column zalo_followers.birth_date is 'Ngày sinh của người dùng (định dạng dd/mm/yyyy)';

comment on column zalo_followers.tags is 'Các tag gán cho người dùng (JSON)';

comment on column zalo_followers.followed_at is 'Thời điểm theo dõi (Unix timestamp)';

comment on column zalo_followers.unfollowed_at is 'Thời điểm hủy theo dõi (Unix timestamp)';

comment on column zalo_followers.status is 'Trạng thái (active, unfollowed)';

comment on column zalo_followers.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column zalo_followers.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table zalo_followers
    owner to root;

grant select, update, usage on sequence zalo_followers_id_seq to member;

create index idx_zalo_followers_oa_id
    on zalo_followers (oa_id);

create index idx_zalo_followers_status
    on zalo_followers (status);

grant delete, insert, select, update on zalo_followers to member;

create table zalo_webhook_logs
(
    id         serial
        primary key,
    oa_id      varchar(50)                                         not null,
    event_name varchar(50)                                         not null,
    event_id   varchar(50)                                         not null,
    data       jsonb                                               not null,
    processed  boolean default false,
    timestamp  bigint                                              not null,
    created_at bigint  default (EXTRACT(epoch FROM now()))::bigint not null
);

comment on table zalo_webhook_logs is 'Lưu trữ lịch sử các sự kiện webhook từ Zalo';

comment on column zalo_webhook_logs.id is 'ID tự động tăng';

comment on column zalo_webhook_logs.oa_id is 'ID của Official Account';

comment on column zalo_webhook_logs.event_name is 'Tên sự kiện';

comment on column zalo_webhook_logs.event_id is 'ID của sự kiện';

comment on column zalo_webhook_logs.data is 'Dữ liệu của sự kiện (JSON)';

comment on column zalo_webhook_logs.processed is 'Đã xử lý chưa';

comment on column zalo_webhook_logs.timestamp is 'Thời điểm xảy ra sự kiện (Unix timestamp)';

comment on column zalo_webhook_logs.created_at is 'Thời điểm tạo bản ghi (Unix timestamp)';

alter table zalo_webhook_logs
    owner to root;

grant select, update, usage on sequence zalo_webhook_logs_id_seq to member;

create index idx_zalo_webhook_logs_oa_id
    on zalo_webhook_logs (oa_id);

create index idx_zalo_webhook_logs_event_name
    on zalo_webhook_logs (event_name);

create index idx_zalo_webhook_logs_processed
    on zalo_webhook_logs (processed);

create index idx_zalo_webhook_logs_timestamp
    on zalo_webhook_logs (timestamp);

grant delete, insert, select, update on zalo_webhook_logs to member;

create table google_ads_accounts
(
    id            bigserial
        primary key,
    user_id       bigint                                          not null,
    customer_id   varchar(255)                                    not null,
    refresh_token text                                            not null,
    name          varchar(255),
    status        varchar(20) default 'active'::character varying not null,
    created_at    bigint                                          not null,
    updated_at    bigint
);

comment on table google_ads_accounts is 'Lưu trữ thông tin tài khoản Google Ads của người dùng';

comment on column google_ads_accounts.id is 'ID của tài khoản Google Ads trong hệ thống';

comment on column google_ads_accounts.user_id is 'ID của người dùng';

comment on column google_ads_accounts.customer_id is 'Customer ID của tài khoản Google Ads';

comment on column google_ads_accounts.refresh_token is 'Refresh token để truy cập Google Ads API';

comment on column google_ads_accounts.name is 'Tên tài khoản';

comment on column google_ads_accounts.status is 'Trạng thái tài khoản';

comment on column google_ads_accounts.created_at is 'Thời gian tạo';

comment on column google_ads_accounts.updated_at is 'Thời gian cập nhật';

alter table google_ads_accounts
    owner to root;

grant select, update, usage on sequence google_ads_accounts_id_seq to member;

create index idx_google_ads_accounts_user_id
    on google_ads_accounts (user_id);

create index idx_google_ads_accounts_customer_id
    on google_ads_accounts (customer_id);

grant delete, insert, select, update on google_ads_accounts to member;

create table google_ads_campaigns
(
    id               bigserial
        primary key,
    user_id          bigint       not null,
    account_id       bigint       not null
        references google_ads_accounts
            on delete cascade,
    campaign_id      varchar(255) not null,
    name             varchar(255) not null,
    status           varchar(20)  not null,
    type             varchar(50)  not null,
    budget           bigint       not null,
    start_date       varchar(10),
    end_date         varchar(10),
    user_campaign_id bigint,
    created_at       bigint       not null,
    updated_at       bigint
);

comment on table google_ads_campaigns is 'Lưu trữ thông tin chiến dịch Google Ads';

comment on column google_ads_campaigns.id is 'ID của chiến dịch Google Ads trong hệ thống';

comment on column google_ads_campaigns.user_id is 'ID của người dùng';

comment on column google_ads_campaigns.account_id is 'ID của tài khoản Google Ads';

comment on column google_ads_campaigns.campaign_id is 'ID của chiến dịch trên Google Ads';

comment on column google_ads_campaigns.name is 'Tên chiến dịch';

comment on column google_ads_campaigns.status is 'Trạng thái chiến dịch';

comment on column google_ads_campaigns.type is 'Loại chiến dịch (SEARCH, DISPLAY, ...)';

comment on column google_ads_campaigns.budget is 'Ngân sách hàng ngày (micro amount)';

comment on column google_ads_campaigns.start_date is 'Ngày bắt đầu (YYYY-MM-DD)';

comment on column google_ads_campaigns.end_date is 'Ngày kết thúc (YYYY-MM-DD)';

comment on column google_ads_campaigns.user_campaign_id is 'ID của chiến dịch marketing';

comment on column google_ads_campaigns.created_at is 'Thời gian tạo';

comment on column google_ads_campaigns.updated_at is 'Thời gian cập nhật';

alter table google_ads_campaigns
    owner to root;

grant select, update, usage on sequence google_ads_campaigns_id_seq to member;

create index idx_google_ads_campaigns_user_id
    on google_ads_campaigns (user_id);

create index idx_google_ads_campaigns_account_id
    on google_ads_campaigns (account_id);

create index idx_google_ads_campaigns_campaign_id
    on google_ads_campaigns (campaign_id);

create index idx_google_ads_campaigns_user_campaign_id
    on google_ads_campaigns (user_campaign_id);

grant delete, insert, select, update on google_ads_campaigns to member;

create table google_ads_ad_groups
(
    id             bigserial
        primary key,
    user_id        bigint       not null,
    campaign_id    bigint       not null
        references google_ads_campaigns
            on delete cascade,
    ad_group_id    varchar(255) not null,
    name           varchar(255) not null,
    status         varchar(20)  not null,
    cpc_bid_micros bigint,
    created_at     bigint       not null,
    updated_at     bigint
);

comment on table google_ads_ad_groups is 'Lưu trữ thông tin nhóm quảng cáo Google Ads';

comment on column google_ads_ad_groups.id is 'ID của nhóm quảng cáo trong hệ thống';

comment on column google_ads_ad_groups.user_id is 'ID của người dùng';

comment on column google_ads_ad_groups.campaign_id is 'ID của chiến dịch trong hệ thống';

comment on column google_ads_ad_groups.ad_group_id is 'ID của nhóm quảng cáo trên Google Ads';

comment on column google_ads_ad_groups.name is 'Tên nhóm quảng cáo';

comment on column google_ads_ad_groups.status is 'Trạng thái nhóm quảng cáo';

comment on column google_ads_ad_groups.cpc_bid_micros is 'CPC tối đa (micro amount)';

comment on column google_ads_ad_groups.created_at is 'Thời gian tạo';

comment on column google_ads_ad_groups.updated_at is 'Thời gian cập nhật';

alter table google_ads_ad_groups
    owner to root;

grant select, update, usage on sequence google_ads_ad_groups_id_seq to member;

create index idx_google_ads_ad_groups_user_id
    on google_ads_ad_groups (user_id);

create index idx_google_ads_ad_groups_campaign_id
    on google_ads_ad_groups (campaign_id);

create index idx_google_ads_ad_groups_ad_group_id
    on google_ads_ad_groups (ad_group_id);

grant delete, insert, select, update on google_ads_ad_groups to member;

create table google_ads_keywords
(
    id             bigserial
        primary key,
    user_id        bigint       not null,
    ad_group_id    bigint       not null
        references google_ads_ad_groups
            on delete cascade,
    keyword_id     varchar(255) not null,
    text           varchar(255) not null,
    match_type     varchar(20)  not null,
    status         varchar(20)  not null,
    cpc_bid_micros bigint,
    created_at     bigint       not null,
    updated_at     bigint
);

comment on table google_ads_keywords is 'Lưu trữ thông tin từ khóa Google Ads';

comment on column google_ads_keywords.id is 'ID của từ khóa trong hệ thống';

comment on column google_ads_keywords.user_id is 'ID của người dùng';

comment on column google_ads_keywords.ad_group_id is 'ID của nhóm quảng cáo trong hệ thống';

comment on column google_ads_keywords.keyword_id is 'ID của từ khóa trên Google Ads';

comment on column google_ads_keywords.text is 'Văn bản từ khóa';

comment on column google_ads_keywords.match_type is 'Loại đối sánh (EXACT, PHRASE, BROAD)';

comment on column google_ads_keywords.status is 'Trạng thái từ khóa';

comment on column google_ads_keywords.cpc_bid_micros is 'CPC tối đa (micro amount)';

comment on column google_ads_keywords.created_at is 'Thời gian tạo';

comment on column google_ads_keywords.updated_at is 'Thời gian cập nhật';

alter table google_ads_keywords
    owner to root;

grant select, update, usage on sequence google_ads_keywords_id_seq to member;

create index idx_google_ads_keywords_user_id
    on google_ads_keywords (user_id);

create index idx_google_ads_keywords_ad_group_id
    on google_ads_keywords (ad_group_id);

create index idx_google_ads_keywords_keyword_id
    on google_ads_keywords (keyword_id);

grant delete, insert, select, update on google_ads_keywords to member;

create table google_ads_performance
(
    id               bigserial
        primary key,
    user_id          bigint                     not null,
    campaign_id      bigint                     not null
        references google_ads_campaigns
            on delete cascade,
    date             varchar(10)                not null,
    impressions      integer          default 0 not null,
    clicks           integer          default 0 not null,
    cost             bigint           default 0 not null,
    ctr              double precision default 0 not null,
    average_cpc      bigint           default 0 not null,
    conversions      double precision default 0 not null,
    conversion_value double precision default 0 not null,
    created_at       bigint                     not null
);

comment on table google_ads_performance is 'Lưu trữ thông tin hiệu suất chiến dịch Google Ads';

comment on column google_ads_performance.id is 'ID của báo cáo hiệu suất trong hệ thống';

comment on column google_ads_performance.user_id is 'ID của người dùng';

comment on column google_ads_performance.campaign_id is 'ID của chiến dịch trong hệ thống';

comment on column google_ads_performance.date is 'Ngày của báo cáo (YYYY-MM-DD)';

comment on column google_ads_performance.impressions is 'Số lần hiển thị';

comment on column google_ads_performance.clicks is 'Số lần nhấp chuột';

comment on column google_ads_performance.cost is 'Chi phí (micro amount)';

comment on column google_ads_performance.ctr is 'Tỷ lệ nhấp chuột (%)';

comment on column google_ads_performance.average_cpc is 'CPC trung bình';

comment on column google_ads_performance.conversions is 'Số lượt chuyển đổi';

comment on column google_ads_performance.conversion_value is 'Giá trị chuyển đổi';

comment on column google_ads_performance.created_at is 'Thời gian tạo';

alter table google_ads_performance
    owner to root;

grant select, update, usage on sequence google_ads_performance_id_seq to member;

create index idx_google_ads_performance_user_id
    on google_ads_performance (user_id);

create index idx_google_ads_performance_campaign_id
    on google_ads_performance (campaign_id);

create index idx_google_ads_performance_date
    on google_ads_performance (date);

grant delete, insert, select, update on google_ads_performance to member;

create table type_agents
(
    id            serial
        primary key,
    name          varchar(255)                                                                      not null,
    description   text,
    created_by    integer
        references employees,
    updated_by    integer
        references employees,
    created_at    bigint            default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at    bigint            default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    icon          varchar(255),
    system_prompt text,
    user_id       integer
        references users,
    deleted_at    bigint,
    config        jsonb             default '{}'::jsonb                                             not null,
    deleted_by    integer
        constraint type_agents_employees_id_fk
            references employees,
    status        type_agent_status default 'DRAFT'::type_agent_status                              not null
);

comment on table type_agents is 'Bảng quản lý các loại agent, định nghĩa các nhóm chức năng mà agent có thể thực hiện';

comment on column type_agents.id is 'Mã định danh duy nhất cho mỗi loại agent, tự động tăng';

comment on column type_agents.name is 'Tên của loại agent, ví dụ: Hỗ trợ khách hàng, Đại lý bán hàng';

comment on column type_agents.description is 'Mô tả chi tiết về phạm vi và mục đích của loại agent';

comment on column type_agents.created_by is 'Mã nhân viên tạo loại agent';

comment on column type_agents.updated_by is 'Mã nhân viên cập nhật loại agent gần nhất';

comment on column type_agents.created_at is 'Thời điểm tạo, tính bằng mili giây';

comment on column type_agents.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

comment on column type_agents.icon is 'Biểu tượng hiển thị trên giao diện';

comment on column type_agents.system_prompt is 'Hướng dẫn hệ thống cho agent';

comment on column type_agents.user_id is 'Mã người dùng liên kết với loại agent';

alter table type_agents
    owner to root;

grant select, update, usage on sequence type_agents_id_seq to member;

create index idx_type_agents_name
    on type_agents (name);

create index idx_type_agents_created_by
    on type_agents (created_by);

create index idx_type_agents_updated_by
    on type_agents (updated_by);

grant delete, insert, select, update on type_agents to member;

create table admin_tools
(
    id          uuid         default uuid_generate_v4()                                      not null
        primary key,
    name        varchar(255)                                                                 not null,
    description text,
    created_by  integer                                                                      not null
        references employees,
    updated_by  integer                                                                      not null
        references employees,
    created_at  bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status      tools_status default 'DRAFT'::tools_status                                   not null,
    access_type access_type  default 'PUBLIC'::access_type                                   not null,
    is_for_sale boolean      default false                                                   not null
);

comment on table admin_tools is 'Bảng lưu trữ thông tin cơ bản của các công cụ do admin tạo';

comment on column admin_tools.id is 'Mã định danh duy nhất của công cụ, sử dụng UUID';

comment on column admin_tools.name is 'Tên của công cụ để nhận diện';

comment on column admin_tools.description is 'Mô tả chi tiết về mục đích của công cụ';

comment on column admin_tools.created_by is 'Mã nhân viên tạo công cụ';

comment on column admin_tools.updated_by is 'Mã nhân viên cập nhật công cụ gần nhất';

comment on column admin_tools.created_at is 'Thời điểm tạo công cụ, tính bằng mili giây';

comment on column admin_tools.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

comment on column admin_tools.status is 'Trạng thái của công cụ (NHÁP, ĐÃ PHÊ DUYỆT, KHÔNG SỬ DỤNG)';

comment on column admin_tools.access_type is 'Mức độ truy cập của công cụ (CÔNG KHAI, RIÊNG TƯ, HẠN CHẾ)';

alter table admin_tools
    owner to root;

grant delete, insert, select, update on admin_tools to member;

create table admin_tool_versions
(
    id                 uuid         default uuid_generate_v4()                                      not null
        primary key,
    tool_id            uuid                                                                         not null
        constraint admin_tool_versions_function_id_fkey
            references admin_tools
            on delete cascade,
    version_number     integer                                                                      not null,
    change_description text,
    created_by         integer                                                                      not null
        references employees,
    updates_by         integer                                                                      not null
        references employees,
    created_at         bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at         bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status             tools_status default 'DRAFT'::tools_status                                   not null,
    tool_name          varchar(64)                                                                  not null
        constraint admin_tool_versions_function_name_key
            unique,
    tool_description   text,
    parameters         jsonb        default '{}'::jsonb                                             not null,
    constraint unique_tool_version
        unique (tool_id, version_number)
);

comment on table admin_tool_versions is 'Bảng lưu trữ các phiên bản của công cụ do admin tạo hoặc cập nhật';

comment on column admin_tool_versions.id is 'Mã định danh duy nhất của phiên bản, sử dụng UUID';

comment on column admin_tool_versions.tool_id is 'Mã công cụ gốc, tham chiếu đến admin_tools';

comment on column admin_tool_versions.version_number is 'Số thứ tự phiên bản, tăng dần theo thời gian';

comment on column admin_tool_versions.change_description is 'Mô tả thay đổi so với phiên bản trước';

comment on column admin_tool_versions.created_by is 'Mã nhân viên tạo phiên bản này';

comment on column admin_tool_versions.updates_by is 'Mã nhân viên cập nhật phiên bản này gần nhất';

comment on column admin_tool_versions.created_at is 'Thời điểm tạo phiên bản, tính bằng mili giây';

comment on column admin_tool_versions.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

comment on column admin_tool_versions.status is 'Trạng thái của phiên bản (NHÁP, ĐÃ PHÊ DUYỆT, KHÔNG SỬ DỤNG)';

comment on column admin_tool_versions.tool_name is 'Tên duy nhất của phiên bản công cụ';

comment on column admin_tool_versions.tool_description is 'Mô tả chi tiết về chức năng của phiên bản';

comment on column admin_tool_versions.parameters is 'Đối tượng JSONB lưu trữ tham số của công cụ';

alter table admin_tool_versions
    owner to root;

create table tools_strategy
(
    strategy_version_id integer not null
        constraint functions_strategy_strategy_version_id_fkey
            references strategy_agent_versions,
    tool_id             uuid    not null
        constraint tools_strategy_admin_tools_id_fk
            references admin_tools,
    version_tool_id     uuid    not null
        constraint tools_strategy_admin_tool_versions_id_fk
            references admin_tool_versions,
    constraint functions_strategy_pkey
        primary key (strategy_version_id, tool_id)
);

alter table tools_strategy
    owner to root;

grant delete, insert, select, update on tools_strategy to member;

grant delete, insert, select, update on admin_tool_versions to member;

create table admin_group_tools
(
    id          serial
        primary key,
    name        varchar(100)                                                           not null,
    description text,
    created_by  integer
        references employees,
    updated_by  integer
        references employees,
    created_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table admin_group_tools is 'Bảng lưu trữ các nhóm công cụ của admin';

comment on column admin_group_tools.id is 'Mã định danh duy nhất của nhóm, tự động tăng';

comment on column admin_group_tools.name is 'Tên của nhóm công cụ';

comment on column admin_group_tools.description is 'Mô tả mục đích của nhóm công cụ';

comment on column admin_group_tools.created_by is 'Mã nhân viên tạo nhóm';

comment on column admin_group_tools.updated_by is 'Mã nhân viên cập nhật nhóm gần nhất';

comment on column admin_group_tools.created_at is 'Thời điểm tạo nhóm, tính bằng mili giây';

comment on column admin_group_tools.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

alter table admin_group_tools
    owner to root;

grant select, update, usage on sequence admin_group_tools_id_seq to member;

grant delete, insert, select, update on admin_group_tools to member;

create table admin_group_tool_mappings
(
    group_id integer not null
        references admin_group_tools
            on delete cascade,
    tool_id  uuid    not null
        references admin_tools
            on delete cascade,
    primary key (group_id, tool_id)
);

comment on table admin_group_tool_mappings is 'Bảng liên kết ánh xạ công cụ admin với nhóm công cụ admin';

comment on column admin_group_tool_mappings.group_id is 'Mã nhóm công cụ, tham chiếu đến admin_group_tools';

comment on column admin_group_tool_mappings.tool_id is 'Mã công cụ, tham chiếu đến admin_tools';

alter table admin_group_tool_mappings
    owner to root;

grant delete, insert, select, update on admin_group_tool_mappings to member;

create table admin_group_tools_type_agents
(
    group_id      integer not null
        references admin_group_tools
            on delete cascade,
    type_agent_id integer not null
        references type_agents
            on delete cascade,
    primary key (group_id, type_agent_id)
);

comment on table admin_group_tools_type_agents is 'Bảng liên kết ánh xạ nhóm công cụ admin với loại agent';

comment on column admin_group_tools_type_agents.group_id is 'Mã nhóm công cụ, tham chiếu đến admin_group_tools';

comment on column admin_group_tools_type_agents.type_agent_id is 'Mã loại agent, tham chiếu đến type_agents';

alter table admin_group_tools_type_agents
    owner to root;

grant delete, insert, select, update on admin_group_tools_type_agents to member;

create table user_group_tools
(
    id          serial
        primary key,
    name        varchar(100)                                                           not null,
    description text,
    user_id     integer
        references users,
    created_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_group_tools is 'Bảng lưu trữ các nhóm công cụ của người dùng';

comment on column user_group_tools.id is 'Mã định danh duy nhất của nhóm, tự động tăng';

comment on column user_group_tools.name is 'Tên của nhóm công cụ';

comment on column user_group_tools.description is 'Mô tả mục đích của nhóm công cụ';

comment on column user_group_tools.user_id is 'Mã người dùng tạo nhóm';

comment on column user_group_tools.created_at is 'Thời điểm tạo nhóm, tính bằng mili giây';

comment on column user_group_tools.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

alter table user_group_tools
    owner to root;

grant select, update, usage on sequence user_group_tools_id_seq to member;

grant delete, insert, select, update on user_group_tools to member;

create table user_group_tools_type_agents
(
    group_id      integer not null
        references user_group_tools
            on delete cascade,
    type_agent_id integer not null
        references type_agents
            on delete cascade,
    primary key (group_id, type_agent_id)
);

comment on table user_group_tools_type_agents is 'Bảng liên kết ánh xạ nhóm công cụ người dùng với loại agent';

comment on column user_group_tools_type_agents.group_id is 'Mã nhóm công cụ, tham chiếu đến user_group_tools';

comment on column user_group_tools_type_agents.type_agent_id is 'Mã loại agent, tham chiếu đến type_agents';

alter table user_group_tools_type_agents
    owner to root;

grant delete, insert, select, update on user_group_tools_type_agents to member;

create table user_tools
(
    id          uuid         default uuid_generate_v4()                                      not null
        primary key,
    name        varchar(255)                                                                 not null,
    description text,
    created_at  bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at  bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    user_id     integer
        references users,
    original_id uuid
        references admin_tools,
    has_update  boolean      default false                                                   not null,
    status      tools_status default 'APPROVED'::tools_status                                not null
);

comment on table user_tools is 'Bảng lưu trữ thông tin cơ bản của các công cụ do người dùng sử dụng hoặc chỉnh sửa';

comment on column user_tools.id is 'Mã định danh duy nhất của công cụ, sử dụng UUID';

comment on column user_tools.name is 'Tên của công cụ để nhận diện';

comment on column user_tools.description is 'Mô tả chi tiết về mục đích của công cụ';

comment on column user_tools.created_at is 'Thời điểm tạo công cụ, tính bằng mili giây';

comment on column user_tools.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

comment on column user_tools.user_id is 'Mã người dùng liên kết với công cụ';

comment on column user_tools.original_id is 'Mã công cụ gốc, tham chiếu đến admin_tools';

comment on column user_tools.has_update is 'Cờ xác định công cụ có bản cập nhật hay không';

comment on column user_tools.status is 'Trạng thái của công cụ (NHÁP, ĐÃ PHÊ DUYỆT, KHÔNG SỬ DỤNG)';

alter table user_tools
    owner to root;

grant delete, insert, select, update on user_tools to member;

create table user_group_tool_mappings
(
    group_id integer not null
        references user_group_tools
            on delete cascade,
    tool_id  uuid    not null
        references user_tools
            on delete cascade,
    primary key (group_id, tool_id)
);

comment on table user_group_tool_mappings is 'Bảng liên kết ánh xạ công cụ người dùng với nhóm công cụ người dùng';

comment on column user_group_tool_mappings.group_id is 'Mã nhóm công cụ, tham chiếu đến user_group_tools';

comment on column user_group_tool_mappings.tool_id is 'Mã công cụ, tham chiếu đến user_tools_custom';

alter table user_group_tool_mappings
    owner to root;

grant delete, insert, select, update on user_group_tool_mappings to member;

create table user_tool_versions
(
    id                   uuid         default uuid_generate_v4()                                      not null
        primary key,
    user_id              integer                                                                      not null
        references users,
    original_function_id uuid                                                                         not null
        references user_tools
            on delete cascade,
    version_number       integer                                                                      not null,
    change_description   text,
    created_at           bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at           bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status               tools_status default 'APPROVED'::tools_status                                not null,
    tool_name            varchar(64)                                                                  not null
        constraint user_tool_versions_function_name_key
            unique,
    tool_description     text,
    parameters           jsonb        default '{}'::jsonb                                             not null,
    edited               boolean      default false                                                   not null,
    constraint unique_user_function_version
        unique (user_id, original_function_id, version_number)
);

comment on table user_tool_versions is 'Bảng lưu trữ các phiên bản công cụ do người dùng chỉnh sửa';

comment on column user_tool_versions.id is 'Mã định danh duy nhất của phiên bản, sử dụng UUID';

comment on column user_tool_versions.user_id is 'Mã người dùng chỉnh sửa, tham chiếu đến users';

comment on column user_tool_versions.original_function_id is 'Mã công cụ gốc, tham chiếu đến user_tools';

comment on column user_tool_versions.version_number is 'Số thứ tự phiên bản, tăng dần';

comment on column user_tool_versions.change_description is 'Mô tả thay đổi so với phiên bản gốc hoặc trước đó';

comment on column user_tool_versions.created_at is 'Thời điểm tạo phiên bản, tính bằng mili giây';

comment on column user_tool_versions.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

comment on column user_tool_versions.status is 'Trạng thái của phiên bản (NHÁP, ĐÃ PHÊ DUYỆT, KHÔNG SỬ DỤNG)';

comment on column user_tool_versions.tool_name is 'Tên duy nhất của phiên bản công cụ';

comment on column user_tool_versions.tool_description is 'Mô tả chi tiết về chức năng của phiên bản';

comment on column user_tool_versions.parameters is 'Đối tượng JSONB lưu trữ tham số của công cụ';

comment on column user_tool_versions.edited is 'Cờ xác định phiên bản đã được chỉnh sửa';

alter table user_tool_versions
    owner to root;

grant delete, insert, select, update on user_tool_versions to member;

create table market_order_line
(
    id                   bigserial
        constraint market_order_line_pk
            primary key,
    product_id           bigint
        constraint market_order_line_products_id_fk
            references products,
    created_at           bigint,
    updated_at           bigint,
    point                bigint,
    product_name         varchar(500),
    platform_fee_percent double precision,
    seller_receive_price bigint,
    quantity             integer                   not null,
    order_info           jsonb default '{}'::jsonb not null,
    order_id             integer                   not null
        constraint market_order_line_market_order_id_fk
            references market_order
);

comment on column market_order_line.product_id is 'Thông tin sản phẩm của order line, gồm: tên sản phẩm, ảnh, mô tả, thông tin người bán, category  {   "product": {     "id": 12345,     "name": "File tri thức toán học",     "description": "kiến thức toán phổ thông + cao cấp",     "images": [       "https://cdn.example.com/products/12345/image1.jpg",       "https://cdn.example.com/products/12345/image2.jpg"     ],     "category": "KNOWLEDGE_FILE",     "seller": {       "type": "user",       "id": 6789,       "name": "Sơn",       "avatar": "https://cdn.example.com/sellers/6789/avatar.jpg"     }   } } ';

comment on column market_order_line.updated_at is 'Thời gian cập nhật';

comment on column market_order_line.point is 'Số point của sản phẩm';

comment on column market_order_line.product_name is 'Tên sản phẩm';

comment on column market_order_line.platform_fee_percent is 'Phần trăm phí sàn mức';

comment on column market_order_line.seller_receive_price is 'Giá người bán nhận được sau khi trừ phí sàn';

comment on column market_order_line.quantity is 'Số lượng';

comment on column market_order_line.order_info is 'Thông tin sản phẩm của order line, gồm: tên sản phẩm, ảnh, mô tả, thông tin người bán, category  {   "product": {     "id": 12345,     "name": "File tri thức toán học",     "description": "kiến thức toán phổ thông + cao cấp",     "image": "https://cdn.example.com/products/12345/image2.jpg",     "category": "KNOWLEDGE_FILE",     "seller": {       "type": "user",       "id": 6789,     } 	// Hoặc 	"seller": { 		"type" "admin", 		"id": 7890 	}   } } ';

alter table market_order_line
    owner to root;

grant select, update, usage on sequence market_order_line_id_seq to member;

grant delete, insert, select, update on market_order_line to member;

create table agents
(
    id              uuid              default uuid_generate_v4()                                      not null
        primary key,
    name            varchar(255)                                                                      not null,
    avatar          varchar(255),
    model_config    jsonb             default '{}'::jsonb                                             not null,
    instruction     text,
    vector_store_id varchar(100)
        references vector_stores,
    created_at      bigint            default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at      bigint            default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    deleted_at      bigint,
    is_for_sale     boolean           default false                                                   not null,
    status          agent_status_enum default 'DRAFT'::agent_status_enum                              not null
);

comment on table agents is 'Bảng lưu thông tin chung của tất cả agent trong hệ thống';

comment on column agents.id is 'UUID định danh duy nhất cho mỗi agent';

comment on column agents.name is 'Tên hiển thị của agent';

comment on column agents.avatar is 'Lưu key s3 của avatar agent';

comment on column agents.model_config is 'Cấu hình AI model dạng JSONB (ví dụ: {"temperature": 0.7, "max_tokens": 1000})';

comment on column agents.instruction is 'Hướng dẫn hoặc system prompt cho agent';

comment on column agents.vector_store_id is 'ID của kho vector sử dụng bởi agent';

comment on column agents.created_at is 'Thời điểm tạo (timestamp millis)';

comment on column agents.updated_at is 'Thời điểm cập nhật gần nhất (timestamp millis)';

comment on column agents.deleted_at is 'Thời điểm xóa mềm (timestamp millis)';

comment on column agents.is_for_sale is 'Trạng thái có thể bán';

alter table agents
    owner to root;

create table memory_agents
(
    id         uuid   default uuid_generate_v4()                                      not null
        primary key,
    agent_id   uuid
        constraint memory_agents_agents_id_fk
            references agents,
    fact       text                                                                   not null,
    embedding  vector(1536),
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table memory_agents is 'Bảng lưu trữ bộ nhớ của agent theo dạng fact';

comment on column memory_agents.id is 'UUID định danh duy nhất cho mỗi bản ghi bộ nhớ';

comment on column memory_agents.agent_id is 'ID của agent sở hữu bộ nhớ này';

comment on column memory_agents.fact is 'Nội dung thông tin, sự kiện được lưu trong bộ nhớ';

comment on column memory_agents.embedding is 'Vector nhúng (1536 chiều) biểu diễn ngữ nghĩa của fact';

comment on column memory_agents.created_at is 'Thời điểm tạo bộ nhớ (timestamp millis)';

comment on column memory_agents.updated_at is 'Thời điểm cập nhật bộ nhớ gần nhất (timestamp millis)';

alter table memory_agents
    owner to root;

grant delete, insert, select, update on memory_agents to member;

create table tasks
(
    task_id          uuid default uuid_generate_v4() not null
        primary key,
    user_id          integer                         not null,
    agent_id         uuid                            not null
        constraint tasks_agents_id_fk
            references agents,
    task_name        varchar(255)                    not null,
    task_description text,
    status           varchar(50),
    created_at       bigint,
    updated_at       bigint
);

comment on table tasks is 'Bảng lưu thông tin định nghĩa của nhiệm vụ';

comment on column tasks.task_id is 'Khóa chính của bảng tasks, sử dụng UUID';

comment on column tasks.user_id is 'ID người dùng tạo nhiệm vụ';

comment on column tasks.agent_id is 'ID của agent (tham chiếu đến bảng agents)';

comment on column tasks.task_name is 'Tên của nhiệm vụ';

comment on column tasks.task_description is 'Mô tả chi tiết nhiệm vụ';

comment on column tasks.status is 'Trạng thái nhiệm vụ (active, suspended, v.v.)';

comment on column tasks.created_at is 'Thời điểm tạo nhiệm vụ (Unix epoch)';

comment on column tasks.updated_at is 'Thời điểm cập nhật nhiệm vụ (Unix epoch)';

alter table tasks
    owner to root;

grant delete, insert, select, update on tasks to member;

create table steps
(
    step_id          uuid default uuid_generate_v4() not null
        primary key,
    task_id          uuid                            not null
        references tasks,
    order_index      integer                         not null,
    step_name        varchar(255)                    not null,
    step_description text,
    step_type        varchar(50)                     not null,
    step_config      jsonb,
    created_at       bigint,
    updated_at       bigint
);

comment on table steps is 'Bảng lưu định nghĩa các bước trong nhiệm vụ';

comment on column steps.step_id is 'Khóa chính của bảng steps, sử dụng UUID';

comment on column steps.task_id is 'ID của nhiệm vụ, tham chiếu đến bảng tasks';

comment on column steps.order_index is 'Thứ tự xuất hiện của bước trong nhiệm vụ';

comment on column steps.step_name is 'Tên của bước';

comment on column steps.step_description is 'Mô tả chi tiết của bước';

comment on column steps.step_type is 'Loại bước (Prompt, Trigger, Action, Media)';

comment on column steps.step_config is 'Cấu hình chi tiết của bước ở dạng JSONB. Ví dụ:
   - Bước Prompt: {"prompt_text": "Enter your input:"}
   - Bước Trigger: {"trigger_time": 1680000000}
   - Bước Action: {"function": "generate_text", "parameters": {"length": "long"}}
   - Bước Media (image): {"media_type": "image", "resolution": "1024x768", "input_mapping": {"source_order_index": 3, "var_name": "generated_text", "destination_field": "caption_text"}}
   - Bước Media (video): {"media_type": "video", "resolution": "1920x1080", "frame_rate": 30, "duration": 60, "input_mapping": {"source_order_index": 2, "var_name": "video_description", "destination_field": "overlay_text"}}
   Các trường này cho phép tùy biến cấu hình cho từng loại bước theo nghiệp vụ.';

comment on column steps.created_at is 'Thời điểm tạo bước (Unix epoch)';

comment on column steps.updated_at is 'Thời điểm cập nhật bước (Unix epoch)';

alter table steps
    owner to root;

grant delete, insert, select, update on steps to member;

create table task_executions
(
    task_execution_id uuid   default uuid_generate_v4() not null
        primary key,
    task_id           uuid                              not null
        references tasks,
    start_time        bigint                            not null,
    end_time          bigint,
    overall_status    varchar(50),
    execution_details jsonb,
    created_at        bigint default EXTRACT(epoch FROM CURRENT_TIMESTAMP)
);

comment on table task_executions is 'Bảng lưu lịch sử thực thi của nhiệm vụ, bao gồm log chi tiết từng bước dưới dạng JSON';

comment on column task_executions.task_execution_id is 'Khóa chính của bảng task_executions, sử dụng UUID';

comment on column task_executions.task_id is 'ID của nhiệm vụ, tham chiếu đến bảng tasks';

comment on column task_executions.start_time is 'Thời gian bắt đầu phiên thực thi (Unix epoch)';

comment on column task_executions.end_time is 'Thời gian kết thúc phiên thực thi (Unix epoch)';

comment on column task_executions.overall_status is 'Trạng thái tổng thể của phiên thực thi (success, fail, ...)';

comment on column task_executions.execution_details is 'Log chi tiết của từng bước trong phiên thực thi, lưu dưới dạng mảng JSON. Ví dụ:
   [
     {
       "step_id": "uuid-của-bước",
       "order_index": 1,
       "start_time": 1680000100,
       "end_time": 1680000120,
       "status": "success",
       "result": "User input captured",
       "error_message": null
     },
     {
       "step_id": "uuid-của-bước-tiếp",
       "order_index": 2,
       "start_time": 1680000120,
       "end_time": 1680000130,
       "status": "fail",
       "result": null,
       "error_message": "Timeout error"
     }
   ]';

comment on column task_executions.created_at is 'Thời điểm ghi log phiên thực thi (Unix epoch)';

alter table task_executions
    owner to root;

grant delete, insert, select, update on task_executions to member;

create index idx_agents_vector_store_id
    on agents (vector_store_id);

create index idx_agents_deleted_at
    on agents (deleted_at);

grant delete, insert, select, update on agents to member;

create table agents_system
(
    id         uuid         not null
        primary key
        references agents,
    created_by integer
        references employees,
    updated_by integer
        references employees,
    deleted_by integer
        references employees,
    name_code  varchar(100) not null
        constraint agents_system_pk
            unique
);

comment on table agents_system is 'Bảng lưu thông tin agent hệ thống, ví dụ: agent tự động hoặc mặc định của hệ thống';

comment on column agents_system.id is 'UUID tham chiếu từ agents.id';

comment on column agents_system.created_by is 'ID nhân viên tạo';

comment on column agents_system.updated_by is 'ID nhân viên cập nhật';

comment on column agents_system.deleted_by is 'ID nhân viên xóa';

comment on column agents_system.name_code is 'Tên định danh';

alter table agents_system
    owner to root;

create table agent_roles
(
    id                uuid   default uuid_generate_v4()                                      not null
        primary key,
    name              varchar(50)                                                            not null,
    description       text,
    created_by        integer
        references employees,
    updated_by        integer
        references employees,
    created_at        bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at        bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    deleted_at        bigint,
    agent_system_id   uuid
        constraint agent_roles_agents_system_id_fk
            references agents_system,
    deleted_by        integer
        constraint agent_roles_employees_id_fk
            references employees,
    module_mcp_config jsonb  default '{}'::jsonb                                             not null
);

comment on table agent_roles is 'Bảng lưu thông tin các vai trò (roles) của agent';

comment on column agent_roles.id is 'Khóa chính, tự tăng';

comment on column agent_roles.name is 'Tên vai trò';

comment on column agent_roles.description is 'Mô tả chi tiết vai trò';

comment on column agent_roles.created_by is 'ID người tạo (liên kết đến bảng employees)';

comment on column agent_roles.updated_by is 'ID người cập nhật (liên kết đến bảng employees)';

comment on column agent_roles.created_at is 'Thời gian tạo (UNIX timestamp milliseconds)';

comment on column agent_roles.updated_at is 'Thời gian cập nhật gần nhất (UNIX timestamp milliseconds)';

comment on column agent_roles.deleted_at is 'Thời gian xóa mềm (soft delete), null nếu chưa bị xóa';

alter table agent_roles
    owner to root;

grant delete, insert, select, update on agent_roles to member;

create index idx_agents_system_created_by
    on agents_system (created_by);

create index idx_agents_system_updated_by
    on agents_system (updated_by);

create index idx_agents_system_deleted_by
    on agents_system (deleted_by);

grant delete, insert, select, update on agents_system to member;

create table agents_user
(
    id             uuid                  not null
        primary key
        references agents,
    user_id        integer
        references users,
    type_id        integer               not null
        references type_agents,
    source_id      uuid
        references agents,
    profile        jsonb   default '{}'::jsonb,
    convert_config jsonb   default '{}'::jsonb,
    active         boolean default false not null
);

comment on table agents_user is 'Bảng lưu thông tin agent của người dùng, liên kết với users';

comment on column agents_user.id is 'UUID tham chiếu từ agents.id';

comment on column agents_user.user_id is 'ID người dùng sở hữu agent';

comment on column agents_user.type_id is 'ID loại chức năng agent, tham chiếu type_agents';

comment on column agents_user.source_id is 'UUID tham chiếu đến agent gốc (nếu agent được tạo từ agent khác)';

comment on column agents_user.profile is 'Thông tin hồ sơ dạng JSONB (ví dụ: {"bio": "Assistant for tasks"})';

comment on column agents_user.convert_config is 'Cấu hình chuyển đổi dạng JSONB (ví dụ: {"format": "json"})';

alter table agents_user
    owner to root;

create table user_websites
(
    id           bigserial
        primary key,
    user_id      integer      not null,
    website_name varchar(255) not null,
    host         varchar(255) not null,
    verify       boolean   default false,
    created_at   timestamp default CURRENT_TIMESTAMP,
    agent_id     uuid
        constraint user_websites_agents_user_id_fk
            references agents_user
);

comment on table user_websites is 'Lưu trữ danh sách website của người dùng, bao gồm tên, host và trạng thái xác minh';

comment on column user_websites.id is 'Khóa chính, tự động tăng';

comment on column user_websites.user_id is 'ID của người dùng sở hữu website';

comment on column user_websites.website_name is 'Tên website do người dùng đặt';

comment on column user_websites.host is 'Tên miền hoặc địa chỉ host của website';

comment on column user_websites.verify is 'Trạng thái xác minh của website (TRUE nếu đã xác minh)';

comment on column user_websites.created_at is 'Thời điểm tạo bản ghi';

alter table user_websites
    owner to root;

grant select, update, usage on sequence user_websites_id_seq to member;

grant delete, insert, select, update on user_websites to member;

create table facebook_page
(
    facebook_personal_id integer      not null
        constraint facebook_page_facebook_personal_id_fk
            references facebook_personal,
    page_access_token    varchar(1000),
    page_name            text,
    facebook_page_id     varchar(255) not null
        constraint facebook_page_pk
            primary key,
    is_active            boolean,
    agent_id             uuid
        constraint facebook_page_agents_user_id_fk
            references agents_user,
    avatar_page          varchar(255),
    is_error             boolean
);

comment on table facebook_page is 'Bảng quản lý thông tin trang Facebook được kết nối với agent';

comment on column facebook_page.facebook_personal_id is 'ID tài khoản Facebook cá nhân liên kết';

comment on column facebook_page.page_access_token is 'Access token của trang Facebook';

comment on column facebook_page.page_name is 'Tên trang Facebook';

comment on column facebook_page.facebook_page_id is 'ID duy nhất của trang Facebook';

comment on column facebook_page.is_active is 'Trạng thái hoạt động của trang';

comment on column facebook_page.agent_id is 'ID agent được kết nối với trang Facebook';

comment on column facebook_page.avatar_page is 'URL avatar của trang Facebook';

comment on column facebook_page.is_error is 'Trạng thái lỗi của trang Facebook';

alter table facebook_page
    owner to root;

grant delete, insert, select, update on facebook_page to member;

create table user_convert_customers
(
    id         bigserial
        primary key,
    avatar     varchar(255),
    name       varchar(255),
    email      jsonb,
    phone      varchar(20),
    platform   varchar(50),
    timezone   varchar(50),
    created_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    user_id    integer
        references users,
    agent_id   uuid
        constraint user_convert_customers_agents_user_id_fk
            references agents_user,
    metadata   jsonb  default '[]'::jsonb                                             not null
);

comment on table user_convert_customers is 'Khách hàng được chuyển đổi từ nền tảng khác';

comment on column user_convert_customers.id is 'ID khách hàng';

comment on column user_convert_customers.avatar is 'Ảnh đại diện';

comment on column user_convert_customers.name is 'Tên khách hàng';

comment on column user_convert_customers.email is 'Email khách hàng (dạng JSON)';

comment on column user_convert_customers.phone is 'Số điện thoại khách hàng';

comment on column user_convert_customers.platform is 'Nền tảng nguồn (Facebook, Web,...)';

comment on column user_convert_customers.timezone is 'Múi giờ của khách hàng';

comment on column user_convert_customers.created_at is 'Thời gian tạo';

comment on column user_convert_customers.updated_at is 'Thời gian cập nhật';

comment on column user_convert_customers.user_id is 'Người dùng sở hữu khách hàng';

comment on column user_convert_customers.agent_id is 'ID agent hỗ trợ khách hàng';

comment on column user_convert_customers.metadata is 'Trường tùy chỉnh';

alter table user_convert_customers
    owner to root;

grant select, update, usage on sequence user_convert_customers_id_seq to member;

grant delete, insert, select, update on user_convert_customers to member;

create table customer_facebook
(
    id                       bigserial
        primary key,
    page_scoped_id           varchar(20) not null
        unique,
    page_id                  varchar(255),
    name                     text,
    avatar                   varchar(255),
    gender                   varchar(45),
    user_convert_customer_id bigint
        references user_convert_customers
);

comment on table customer_facebook is 'Thông tin khách hàng đến từ Facebook';

comment on column customer_facebook.id is 'ID khách hàng Facebook';

comment on column customer_facebook.page_scoped_id is 'ID riêng của khách hàng trên page';

comment on column customer_facebook.page_id is 'ID trang Facebook';

comment on column customer_facebook.name is 'Tên người dùng';

comment on column customer_facebook.avatar is 'Ảnh đại diện';

comment on column customer_facebook.gender is 'Giới tính';

comment on column customer_facebook.user_convert_customer_id is 'Tham chiếu đến khách hàng chuyển đổi';

alter table customer_facebook
    owner to root;

grant select, update, usage on sequence customer_facebook_id_seq to member;

grant delete, insert, select, update on customer_facebook to member;

create table customer_web
(
    id                       bigserial
        primary key,
    domain                   text,
    path                     text,
    device                   varchar(200),
    os                       varchar(200),
    ip                       varchar(100),
    browser                  varchar(400),
    end_session_unix         bigint,
    start_session_unix       bigint default (EXTRACT(epoch FROM now()) * (1000)::numeric),
    favicon                  varchar(500),
    user_convert_customer_id integer
        references user_convert_customers
);

comment on table customer_web is 'Thông tin khách truy cập từ website';

comment on column customer_web.id is 'ID khách truy cập web';

comment on column customer_web.domain is 'Tên miền truy cập';

comment on column customer_web.path is 'Đường dẫn trang';

comment on column customer_web.device is 'Thiết bị sử dụng';

comment on column customer_web.os is 'Hệ điều hành';

comment on column customer_web.ip is 'Địa chỉ IP';

comment on column customer_web.browser is 'Trình duyệt';

comment on column customer_web.end_session_unix is 'Thời điểm kết thúc phiên truy cập';

comment on column customer_web.start_session_unix is 'Thời điểm bắt đầu phiên truy cập';

comment on column customer_web.favicon is 'Favicon của trang web';

comment on column customer_web.user_convert_customer_id is 'ID khách hàng chuyển đổi';

alter table customer_web
    owner to root;

grant select, update, usage on sequence customer_web_id_seq to member;

grant delete, insert, select, update on customer_web to member;

create table platform_customer_threads
(
    id                       bigserial
        primary key,
    platform                 varchar(50)  not null,
    user_convert_customer_id bigint
        references user_convert_customers,
    thread_id                varchar(100) not null,
    agent_id                 uuid
        constraint platform_customer_threads_agents_user_id_fk
            references agents_user,
    platform_id              varchar(200)
);

comment on table platform_customer_threads is 'Quản lý luồng hội thoại của khách hàng trên các nền tảng';

comment on column platform_customer_threads.id is 'ID bản ghi luồng hội thoại';

comment on column platform_customer_threads.platform is 'Tên nền tảng (Facebook, Web...)';

comment on column platform_customer_threads.user_convert_customer_id is 'ID khách hàng chuyển đổi';

comment on column platform_customer_threads.thread_id is 'ID hội thoại trên nền tảng';

comment on column platform_customer_threads.agent_id is 'ID nhân viên phụ trách hội thoại';

comment on column platform_customer_threads.platform_id is 'ID cụ thể của nền tảng (ex: Facebook page ID)';

alter table platform_customer_threads
    owner to root;

grant select, update, usage on sequence platform_customer_threads_id_seq to member;

grant delete, insert, select, update on platform_customer_threads to member;

create table user_strategy_version_contents
(
    id                       serial
        primary key,
    strategy_version_id      integer               not null
        references strategy_agent_versions
            on delete cascade,
    strategy_content_step_id integer               not null
        references strategy_content_steps
            on delete cascade,
    agent_id                 uuid
        constraint user_strategy_version_contents_agents_user_id_fk
            references agents_user,
    editable_example         text                  not null,
    step_order               integer               not null,
    edited                   boolean default false not null,
    constraint user_strategy_version_content_user_strategy_version_id_step_key
        unique (strategy_version_id, step_order)
);

comment on table user_strategy_version_contents is 'Bảng lưu trữ nội dung của các bước trong phiên bản chiến lược của người dùng, bao gồm cả nội dung ẩn (kế thừa) và nội dung có thể chỉnh sửa';

comment on column user_strategy_version_contents.id is 'ID định danh duy nhất cho mỗi bước nội dung, tự động tăng';

comment on column user_strategy_version_contents.strategy_version_id is 'ID của phiên bản chiến lược do người dùng tạo, tham chiếu đến bảng user_strategy_versions';

comment on column user_strategy_version_contents.strategy_content_step_id is 'ID của bước nội dung gốc từ phiên bản chính thức, tham chiếu đến bảng strategy_content_steps';

comment on column user_strategy_version_contents.step_order is 'Thứ tự của bước trong chuỗi xử lý, kế thừa từ bước gốc';

alter table user_strategy_version_contents
    owner to root;

grant select, update, usage on sequence user_strategy_version_contents_id_seq to member;

grant delete, insert, select, update on user_strategy_version_contents to member;

create table user_orders
(
    id                       bigserial
        primary key,
    user_convert_customer_id bigint
        references user_convert_customers,
    user_id                  integer
        references users,
    product_info             jsonb,
    bill_info                jsonb,
    has_shipping             boolean default true                                                    not null,
    shipping_status          varchar(50),
    logistic_info            jsonb,
    created_at               bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at               bigint  default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    source                   varchar(45)
);

comment on table user_orders is 'Thông tin đơn hàng của khách hàng';

comment on column user_orders.id is 'ID đơn hàng';

comment on column user_orders.user_convert_customer_id is 'Khách hàng đặt đơn';

comment on column user_orders.user_id is 'Người dùng sở hữu đơn hàng';

comment on column user_orders.product_info is 'Thông tin sản phẩm (JSON)';

comment on column user_orders.bill_info is 'Thông tin hóa đơn (JSON)';

comment on column user_orders.has_shipping is 'Đơn hàng có yêu cầu vận chuyển hay không';

comment on column user_orders.shipping_status is 'Trạng thái vận chuyển (ví dụ: pending, shipped, delivered)';

comment on column user_orders.logistic_info is 'Thông tin vận chuyển chi tiết (JSON)';

comment on column user_orders.created_at is 'Thời gian tạo đơn hàng';

comment on column user_orders.updated_at is 'Thời gian cập nhật đơn hàng';

alter table user_orders
    owner to root;

grant select, update, usage on sequence user_orders_id_seq to member;

grant delete, insert, select, update on user_orders to member;

create table user_converts
(
    id                  bigserial
        primary key,
    convert_customer_id bigint
        references user_convert_customers,
    user_id             integer
        references users,
    conversion_type     varchar(50),
    source              varchar(100),
    notes               text,
    content             jsonb,
    created_at          bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at          bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table user_converts is 'Lưu lại nội dung chuyển đổi khách hàng';

comment on column user_converts.id is 'ID bản ghi chuyển đổi';

comment on column user_converts.convert_customer_id is 'Khách hàng được chuyển đổi';

comment on column user_converts.user_id is 'Người dùng thực hiện chuyển đổi';

comment on column user_converts.conversion_type is 'Loại chuyển đổi (ví dụ: online, offline, referral)';

comment on column user_converts.source is 'Nguồn gốc chuyển đổi (ví dụ: website, social media, event)';

comment on column user_converts.notes is 'Ghi chú thêm về chuyển đổi';

comment on column user_converts.content is 'Thông tin bổ sung (JSON)';

comment on column user_converts.created_at is 'Thời gian tạo';

comment on column user_converts.updated_at is 'Thời gian cập nhật';

alter table user_converts
    owner to root;

grant select, update, usage on sequence user_converts_id_seq to member;

grant delete, insert, select, update on user_converts to member;

create table transactions
(
    id               bigserial
        primary key,
    order_id         bigint
        references user_orders,
    supplier_id      bigint
        references suppliers,
    transaction_type transaction_type default 'INBOUND'::transaction_type                             not null,
    quantity         integer                                                                          not null
        constraint transactions_quantity_check
            check (quantity > 0),
    product_id       integer                                                                          not null
        references user_products,
    warehouse_id     integer
        references warehouse,
    transaction_date bigint           default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    notes            text,
    created_at       bigint           default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at       bigint           default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table transactions is 'Lưu trữ thông tin các giao dịch liên quan đến kho';

comment on column transactions.id is 'ID giao dịch';

comment on column transactions.order_id is 'ID đơn hàng liên quan (nếu có)';

comment on column transactions.supplier_id is 'ID nhà cung cấp liên quan (nếu có)';

comment on column transactions.transaction_type is 'Loại giao dịch (inbound: nhập kho, outbound: xuất kho, adjustment: điều chỉnh)';

comment on column transactions.quantity is 'Số lượng sản phẩm trong giao dịch';

comment on column transactions.product_id is 'ID sản phẩm';

comment on column transactions.warehouse_id is 'ID kho liên quan';

comment on column transactions.transaction_date is 'Thời gian thực hiện giao dịch';

comment on column transactions.notes is 'Ghi chú thêm về giao dịch';

comment on column transactions.created_at is 'Thời gian tạo bản ghi';

comment on column transactions.updated_at is 'Thời gian cập nhật bản ghi';

alter table transactions
    owner to root;

grant select, update, usage on sequence transactions_id_seq to member;

grant delete, insert, select, update on transactions to member;

create table telegram_bots
(
    id           serial
        primary key,
    user_id      integer      not null
        references users,
    bot_name     varchar(255) not null,
    bot_username varchar(255) not null,
    bot_token    varchar(255) not null,
    agent_id     uuid
        constraint telegram_bots_agents_user_id_fk
            references agents_user,
    is_active    boolean default true,
    webhook_url  varchar(255),
    created_at   bigint  default (EXTRACT(epoch FROM now()))::bigint,
    updated_at   bigint  default (EXTRACT(epoch FROM now()))::bigint
);

comment on table telegram_bots is 'Lưu trữ thông tin về các bot Telegram được tạo bởi người dùng';

comment on column telegram_bots.user_id is 'ID người dùng sở hữu bot';

comment on column telegram_bots.bot_name is 'Tên bot do người dùng đặt';

comment on column telegram_bots.bot_username is 'Username của bot trên Telegram (không bao gồm @)';

comment on column telegram_bots.bot_token is 'Token API của bot từ BotFather';

comment on column telegram_bots.agent_id is 'ID agent được kết nối với bot Telegram';

comment on column telegram_bots.is_active is 'Trạng thái hoạt động của bot';

comment on column telegram_bots.webhook_url is 'Webhook URL đã đăng ký với Telegram';

comment on column telegram_bots.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column telegram_bots.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table telegram_bots
    owner to root;

grant select, update, usage on sequence telegram_bots_id_seq to member;

create index idx_telegram_bots_user_id
    on telegram_bots (user_id);

create index idx_telegram_bots_agent_id
    on telegram_bots (agent_id);

grant delete, insert, select, update on telegram_bots to member;

create table telegram_chats
(
    id              serial
        primary key,
    telegram_bot_id integer     not null
        references telegram_bots,
    chat_id         bigint      not null,
    chat_type       varchar(20) not null,
    chat_title      varchar(255),
    username        varchar(255),
    full_name       varchar(255),
    is_active       boolean default true,
    created_at      bigint  default (EXTRACT(epoch FROM now()))::bigint,
    updated_at      bigint  default (EXTRACT(epoch FROM now()))::bigint,
    unique (telegram_bot_id, chat_id)
);

comment on table telegram_chats is 'Lưu trữ thông tin về các cuộc trò chuyện Telegram mà bot tham gia';

comment on column telegram_chats.telegram_bot_id is 'ID của bot Telegram';

comment on column telegram_chats.chat_id is 'ID của cuộc trò chuyện trên Telegram';

comment on column telegram_chats.chat_type is 'Loại cuộc trò chuyện (private, group, supergroup, channel)';

comment on column telegram_chats.chat_title is 'Tên của cuộc trò chuyện';

comment on column telegram_chats.username is 'Username của người dùng hoặc nhóm (nếu có)';

comment on column telegram_chats.full_name is 'Tên đầy đủ của người dùng (nếu là chat private)';

comment on column telegram_chats.is_active is 'Trạng thái hoạt động của cuộc trò chuyện';

comment on column telegram_chats.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column telegram_chats.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table telegram_chats
    owner to root;

grant select, update, usage on sequence telegram_chats_id_seq to member;

create index idx_telegram_chats_bot_id
    on telegram_chats (telegram_bot_id);

create index idx_telegram_chats_chat_id
    on telegram_chats (chat_id);

grant delete, insert, select, update on telegram_chats to member;

create table telegram_messages
(
    id               serial
        primary key,
    telegram_chat_id integer     not null
        references telegram_chats,
    message_id       bigint      not null,
    sender_id        bigint,
    sender_name      varchar(255),
    text             text,
    message_type     varchar(20) default 'text'::character varying,
    metadata         jsonb       default '{}'::jsonb,
    direction        varchar(10) not null,
    sent_at          bigint      not null,
    unique (telegram_chat_id, message_id)
);

comment on table telegram_messages is 'Lưu trữ lịch sử tin nhắn trao đổi qua Telegram';

comment on column telegram_messages.telegram_chat_id is 'ID của cuộc trò chuyện Telegram';

comment on column telegram_messages.message_id is 'ID tin nhắn trên Telegram';

comment on column telegram_messages.sender_id is 'ID người dùng gửi tin nhắn trên Telegram';

comment on column telegram_messages.sender_name is 'Tên người gửi';

comment on column telegram_messages.text is 'Nội dung tin nhắn';

comment on column telegram_messages.message_type is 'Loại tin nhắn (text, photo, video, document, etc.)';

comment on column telegram_messages.metadata is 'Metadata của tin nhắn (JSON)';

comment on column telegram_messages.direction is 'Hướng tin nhắn (incoming/outgoing)';

comment on column telegram_messages.sent_at is 'Thời điểm gửi tin nhắn (Unix timestamp)';

alter table telegram_messages
    owner to root;

grant select, update, usage on sequence telegram_messages_id_seq to member;

create index idx_telegram_messages_chat_id
    on telegram_messages (telegram_chat_id);

create index idx_telegram_messages_sent_at
    on telegram_messages (sent_at);

grant delete, insert, select, update on telegram_messages to member;

create table telegram_webhook_logs
(
    id              serial
        primary key,
    telegram_bot_id integer
        references telegram_bots,
    payload         jsonb       not null,
    status          varchar(20) not null,
    error_message   text,
    created_at      bigint default (EXTRACT(epoch FROM now()))::bigint
);

comment on table telegram_webhook_logs is 'Lưu trữ log các webhook từ Telegram để debug và theo dõi';

comment on column telegram_webhook_logs.telegram_bot_id is 'ID của bot Telegram';

comment on column telegram_webhook_logs.payload is 'Dữ liệu webhook nhận được (JSON)';

comment on column telegram_webhook_logs.status is 'Trạng thái xử lý (success/error)';

comment on column telegram_webhook_logs.error_message is 'Thông báo lỗi (nếu có)';

comment on column telegram_webhook_logs.created_at is 'Thời điểm nhận webhook (Unix timestamp)';

alter table telegram_webhook_logs
    owner to root;

grant select, update, usage on sequence telegram_webhook_logs_id_seq to member;

create index idx_telegram_webhook_logs_bot_id
    on telegram_webhook_logs (telegram_bot_id);

create index idx_telegram_webhook_logs_created_at
    on telegram_webhook_logs (created_at);

grant delete, insert, select, update on telegram_webhook_logs to member;

create table telegram_commands
(
    id              serial
        primary key,
    telegram_bot_id integer      not null
        references telegram_bots,
    command         varchar(50)  not null,
    description     varchar(255) not null,
    response        text,
    action          jsonb,
    "order"         integer default 0,
    is_active       boolean default true,
    created_at      bigint  default (EXTRACT(epoch FROM now()))::bigint,
    updated_at      bigint  default (EXTRACT(epoch FROM now()))::bigint,
    unique (telegram_bot_id, command)
);

comment on table telegram_commands is 'Lưu trữ các lệnh tùy chỉnh cho bot Telegram';

comment on column telegram_commands.telegram_bot_id is 'ID của bot Telegram';

comment on column telegram_commands.command is 'Tên lệnh (không bao gồm /)';

comment on column telegram_commands.description is 'Mô tả lệnh';

comment on column telegram_commands.response is 'Phản hồi khi lệnh được gọi';

comment on column telegram_commands.action is 'Hành động tùy chỉnh (JSON)';

comment on column telegram_commands."order" is 'Thứ tự hiển thị';

comment on column telegram_commands.is_active is 'Trạng thái hoạt động';

comment on column telegram_commands.created_at is 'Thời điểm tạo (Unix timestamp)';

comment on column telegram_commands.updated_at is 'Thời điểm cập nhật (Unix timestamp)';

alter table telegram_commands
    owner to root;

grant select, update, usage on sequence telegram_commands_id_seq to member;

create index idx_telegram_commands_bot_id
    on telegram_commands (telegram_bot_id);

grant delete, insert, select, update on telegram_commands to member;

create index idx_agents_user_user_id
    on agents_user (user_id);

create index idx_agents_user_type_id
    on agents_user (type_id);

create index idx_agents_user_source_id
    on agents_user (source_id);

grant delete, insert, select, update on agents_user to member;

create table agents_template
(
    id             uuid                                                         not null
        primary key
        references agents,
    created_by     integer
        references employees,
    updated_by     integer
        references employees,
    deleted_by     integer
        references employees,
    type_id        integer                                                      not null
        references type_agents,
    profile        jsonb                 default '{}'::jsonb,
    convert_config jsonb                 default '{}'::jsonb,
    status         agent_template_status default 'DRAFT'::agent_template_status not null
);

comment on table agents_template is 'Bảng lưu mẫu agent để tạo agent mới, dùng bởi admin';

comment on column agents_template.id is 'UUID tham chiếu từ agents.id';

comment on column agents_template.created_by is 'ID nhân viên tạo';

comment on column agents_template.updated_by is 'ID nhân viên cập nhật';

comment on column agents_template.deleted_by is 'ID nhân viên xóa';

comment on column agents_template.type_id is 'ID loại chức năng agent, tham chiếu type_agents';

comment on column agents_template.profile is 'Thông tin hồ sơ mẫu dạng JSONB';

comment on column agents_template.convert_config is 'Cấu hình chuyển đổi mẫu dạng JSONB';

comment on column agents_template.status is 'Trạng thái của mẫu agent (DRAFT: nháp, PUBLISHED: đã xuất bản, ARCHIVED: lưu trữ)';

alter table agents_template
    owner to root;

create index idx_agents_template_created_by
    on agents_template (created_by);

create index idx_agents_template_updated_by
    on agents_template (updated_by);

create index idx_agents_template_deleted_by
    on agents_template (deleted_by);

create index idx_agents_template_type_id
    on agents_template (type_id);

grant delete, insert, select, update on agents_template to member;

create table agents_base
(
    id         uuid                  not null
        primary key
        references agents,
    created_by integer
        references employees,
    updated_by integer
        references employees,
    deleted_by integer
        references employees,
    active     boolean default false not null
);

comment on table agents_base is 'Bảng lưu trạng thái đặc biệt của agent, đánh dấu agent đang hoạt động hoặc thuộc nhóm cơ bản';

comment on column agents_base.id is 'UUID tham chiếu từ agents.id';

comment on column agents_base.created_by is 'ID nhân viên tạo';

comment on column agents_base.updated_by is 'ID nhân viên cập nhật';

comment on column agents_base.deleted_by is 'ID nhân viên xóa';

comment on column agents_base.active is 'Trạng thái hoạt động (chỉ một agent có active = TRUE tại một thời điểm)';

alter table agents_base
    owner to root;

create unique index idx_agents_base_active
    on agents_base (active)
    where (active = true);

create index idx_agents_base_created_by
    on agents_base (created_by);

create index idx_agents_base_updated_by
    on agents_base (updated_by);

create index idx_agents_base_deleted_by
    on agents_base (deleted_by);

grant delete, insert, select, update on agents_base to member;

create table agents_url
(
    url_id   uuid not null
        references url_data,
    agent_id uuid not null
        references agents,
    primary key (url_id, agent_id)
);

alter table agents_url
    owner to root;

grant delete, insert, select, update on agents_url to member;

create table agents_media
(
    media_id uuid not null
        references media_data,
    agent_id uuid not null
        references agents,
    primary key (media_id, agent_id)
);

alter table agents_media
    owner to root;

grant delete, insert, select, update on agents_media to member;

create table agents_product
(
    product_id bigint not null
        references user_products,
    agent_id   uuid   not null
        references agents,
    primary key (product_id, agent_id)
);

alter table agents_product
    owner to root;

grant delete, insert, select, update on agents_product to member;

create table admin_email_server_configurations
(
    id                  serial
        primary key,
    server_name         varchar(100),
    host                varchar(255),
    port                integer,
    username            varchar(255),
    password            varchar(255),
    use_ssl             boolean,
    additional_settings json,
    created_by          integer
        references employees,
    created_at          bigint,
    updated_at          bigint
);

comment on table admin_email_server_configurations is 'Lưu trữ thông tin cấu hình cụ thể cho máy chủ Email (SMTP server).';

comment on column admin_email_server_configurations.server_name is 'Tên hiển thị của cấu hình, ví dụ: “Mailgun Server #1” hoặc “AWS SES”';

comment on column admin_email_server_configurations.host is 'Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…';

comment on column admin_email_server_configurations.port is 'Cổng SMTP, ví dụ: 465, 587, …';

comment on column admin_email_server_configurations.username is 'Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng).';

comment on column admin_email_server_configurations.password is 'Mật khẩu hoặc token xác thực cho SMTP.';

comment on column admin_email_server_configurations.use_ssl is 'Xác định có sử dụng SSL/TLS hay không';

comment on column admin_email_server_configurations.additional_settings is 'Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.';

comment on column admin_email_server_configurations.created_by is 'Nhân viên tạo';

alter table admin_email_server_configurations
    owner to root;

grant select, update, usage on sequence admin_email_server_configurations_id_seq to member;

grant delete, insert, select, update on admin_email_server_configurations to member;

create table user_multi_agents
(
    parent_agent_id uuid not null
        references agents_user,
    child_agent_id  uuid not null
        references agents_user,
    prompt          text,
    primary key (child_agent_id, parent_agent_id),
    constraint chk_parent_not_equal_child
        check (parent_agent_id <> child_agent_id)
);

comment on table user_multi_agents is 'Bảng trung gian lưu quan hệ đa cấp giữa các agents, cho phép một agent có nhiều cấp trên hoặc cấp dưới.';

comment on column user_multi_agents.parent_agent_id is 'ID của agent đóng vai trò là người đỡ đầu (hoặc cấp trên) trong mối quan hệ đa cấp';

comment on column user_multi_agents.child_agent_id is 'ID của agent đóng vai trò là người được đỡ đầu (hoặc cấp dưới) trong mối quan hệ đa cấp';

alter table user_multi_agents
    owner to root;

grant delete, insert, select, update on user_multi_agents to member;

create table api_key
(
    id               uuid   default uuid_generate_v4()                                      not null
        primary key,
    scheme_name      varchar(50)                                                            not null,
    api_key          text                                                                   not null,
    api_key_location varchar(20)                                                            not null
        constraint api_key_api_key_location_check
            check ((api_key_location)::text = ANY
                   ((ARRAY ['header'::character varying, 'query'::character varying])::text[])),
    created_at       bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at       bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table api_key is 'Bảng lưu trữ cấu hình xác thực API Key cho các công cụ tùy chỉnh';

comment on column api_key.id is 'Mã định danh duy nhất, sử dụng UUID';

comment on column api_key.scheme_name is 'Tên security scheme từ Swagger (ví dụ: apiKey)';

comment on column api_key.api_key is 'Giá trị API Key';

comment on column api_key.api_key_location is 'Vị trí gửi API Key (header, query, cookie)';

comment on column api_key.created_at is 'Thời điểm tạo, tính bằng mili giây';

comment on column api_key.updated_at is 'Thời điểm cập nhật, tính bằng mili giây';

alter table api_key
    owner to root;

grant delete, insert, select, update on api_key to member;

create table oauth
(
    id                 uuid   default uuid_generate_v4()                                      not null
        primary key,
    scheme_name        varchar(50)                                                            not null,
    token              text                                                                   not null,
    token_expires_at   bigint,
    refresh_token      text,
    token_source       varchar(20)                                                            not null
        constraint oauth_token_source_check
            check ((token_source)::text = ANY
                   ((ARRAY ['jwt'::character varying, 'oauth'::character varying, 'openid'::character varying])::text[])),
    client_id          varchar(255),
    client_secret      varchar(255),
    auth_url           varchar(255),
    token_url          varchar(255),
    refresh_url        varchar(255),
    openid_connect_url varchar(255),
    scopes             text,
    flow_type          varchar(50),
    created_at         bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at         bigint default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null
);

comment on table oauth is 'Bảng lưu trữ cấu hình và chi tiết xác thực OAuth 2.0, OpenID Connect, và JWT cho các công cụ tùy chỉnh';

comment on column oauth.id is 'Mã định danh duy nhất, sử dụng UUID';

comment on column oauth.scheme_name is 'Tên security scheme từ Swagger (ví dụ: oauth2, bearerAuth)';

comment on column oauth.token is 'Token (JWT, OAuth access token, hoặc OpenID token)';

comment on column oauth.token_expires_at is 'Thời gian hết hạn của token, tính bằng mili giây';

comment on column oauth.refresh_token is 'Refresh token cho OAuth hoặc OpenID';

comment on column oauth.token_source is 'Nguồn token (jwt, oauth, openid)';

comment on column oauth.client_id is 'ID của ứng dụng client';

comment on column oauth.client_secret is 'Mật khẩu của ứng dụng client';

comment on column oauth.auth_url is 'URL để lấy mã ủy quyền';

comment on column oauth.token_url is 'URL để lấy access token';

comment on column oauth.refresh_url is 'URL để làm mới token';

comment on column oauth.openid_connect_url is 'URL discovery cho OpenID Connect';

comment on column oauth.scopes is 'Danh sách scopes (JSON hoặc chuỗi)';

comment on column oauth.flow_type is 'Loại flow (authorization_code, client_credentials, openid)';

comment on column oauth.created_at is 'Thời điểm tạo, tính bằng mili giây';

comment on column oauth.updated_at is 'Thời điểm cập nhật, tính bằng mili giây';

alter table oauth
    owner to root;

grant delete, insert, select, update on oauth to member;

create table user_tools_custom
(
    id               uuid         default uuid_generate_v4()                                      not null
        primary key,
    user_id          integer                                                                      not null
        references users,
    created_at       bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    updated_at       bigint       default ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint not null,
    status           tools_status default 'APPROVED'::tools_status                                not null,
    tool_name        varchar(64)                                                                  not null,
    tool_description text,
    parameters       jsonb        default '{}'::jsonb                                             not null,
    endpoint         varchar(255)                                                                 not null,
    method           varchar(10)                                                                  not null
        constraint user_tools_custom_method_check
            check ((method)::text = ANY
                   ((ARRAY ['GET'::character varying, 'POST'::character varying, 'PUT'::character varying, 'DELETE'::character varying, 'PATCH'::character varying])::text[])),
    response         jsonb        default '{}'::jsonb,
    oauth_id         uuid
        references oauth,
    api_key_id       uuid
        references api_key,
    base_url         varchar(255)                                                                 not null
);

comment on table user_tools_custom is 'Bảng lưu trữ các công cụ tùy chỉnh và phiên bản do người dùng tạo hoặc chỉnh sửa';

comment on column user_tools_custom.id is 'Mã định danh duy nhất của công cụ tùy chỉnh, sử dụng UUID';

comment on column user_tools_custom.user_id is 'Mã người dùng tạo hoặc chỉnh sửa, tham chiếu đến users';

comment on column user_tools_custom.created_at is 'Thời điểm tạo, tính bằng mili giây';

comment on column user_tools_custom.updated_at is 'Thời điểm cập nhật gần nhất, tính bằng mili giây';

comment on column user_tools_custom.status is 'Trạng thái của công cụ (APPROVED, DRAFT, DEPRECATED)';

comment on column user_tools_custom.tool_name is 'Tên duy nhất của công cụ tùy chỉnh';

comment on column user_tools_custom.tool_description is 'Mô tả chi tiết về chức năng của công cụ';

comment on column user_tools_custom.parameters is 'Đối tượng JSONB lưu trữ path_params, query_params, và request_body theo chuẩn Swagger';

comment on column user_tools_custom.endpoint is 'Đường dẫn endpoint của công cụ (ví dụ: /users)';

comment on column user_tools_custom.method is 'Phương thức HTTP (GET, POST, PUT, DELETE, PATCH)';

comment on column user_tools_custom.response is 'Đối tượng JSONB lưu trữ schema phản hồi theo chuẩn Swagger';

alter table user_tools_custom
    owner to root;

grant delete, insert, select, update on user_tools_custom to member;

create table user_group_tool_custom_mappings
(
    group_id integer not null
        references user_group_tools
            on delete cascade,
    tool_id  uuid    not null
        references user_tools_custom
            on delete cascade,
    primary key (group_id, tool_id)
);

alter table user_group_tool_custom_mappings
    owner to root;

grant delete, insert, select, update on user_group_tool_custom_mappings to member;

create table audience_user_custom_fields
(
    field_key    varchar(100) not null,
    user_id      bigint       not null
        constraint fk_user
            references users,
    display_name varchar(255) not null,
    data_type    varchar(50)  not null,
    description  text,
    id           bigserial
        constraint audience_user_custom_fields_pk
            primary key,
    constraint audience_user_custom_fields_pk_2
        unique (user_id, field_key)
);

comment on table audience_user_custom_fields is 'Lưu thông tin các trường tùy chỉnh mà người dùng có thể định nghĩa động';

comment on column audience_user_custom_fields.field_key is 'Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)';

comment on column audience_user_custom_fields.user_id is 'ID của người dùng mà trường tùy chỉnh này thuộc về';

comment on column audience_user_custom_fields.display_name is 'Tên hiển thị thân thiện với người dùng';

comment on column audience_user_custom_fields.data_type is 'Kiểu dữ liệu: string, integer, date, boolean, v.v.';

comment on column audience_user_custom_fields.description is 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh';

alter table audience_user_custom_fields
    owner to root;

grant select, update, usage on sequence audience_user_custom_fields_id_seq to member;

grant delete, insert, select, update on audience_user_custom_fields to member;

create table audience_admin_custom_fields
(
    field_key    varchar(100) not null
        primary key,
    created_by   integer      not null
        constraint fk_user
            references employees,
    display_name varchar(255) not null,
    data_type    varchar(50)  not null,
    description  text
);

comment on table audience_admin_custom_fields is 'Lưu thông tin các trường tùy chỉnh mà admin có thể định nghĩa động';

comment on column audience_admin_custom_fields.field_key is 'Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)';

comment on column audience_admin_custom_fields.created_by is 'ID của admin mà trường tùy chỉnh này thuộc về';

comment on column audience_admin_custom_fields.display_name is 'Tên hiển thị thân thiện với admin';

comment on column audience_admin_custom_fields.data_type is 'Kiểu dữ liệu: string, integer, date, boolean, v.v.';

comment on column audience_admin_custom_fields.description is 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh';

alter table audience_admin_custom_fields
    owner to root;

grant delete, insert, select, update on audience_admin_custom_fields to member;

create table generic_pages
(
    id           varchar(36)                              not null
        primary key,
    name         varchar(255)                             not null,
    description  text,
    path         varchar(255)                             not null
        constraint unique_path
            unique,
    config       jsonb                                    not null,
    status       page_status default 'draft'::page_status not null,
    created_at   bigint                                   not null,
    updated_at   bigint                                   not null,
    published_at bigint,
    created_by   varchar(36),
    updated_by   varchar(36)
);

comment on table generic_pages is 'Lưu trữ thông tin các trang tùy chỉnh được tạo bởi Generic Page Builder';

comment on column generic_pages.id is 'ID duy nhất của trang, dạng UUID';

comment on column generic_pages.name is 'Tên của trang, hiển thị trong giao diện quản trị';

comment on column generic_pages.description is 'Mô tả chi tiết về trang, hỗ trợ cho việc quản lý';

comment on column generic_pages.path is 'Đường dẫn URL của trang, dùng để truy cập trang từ frontend';

comment on column generic_pages.config is 'Cấu hình trang dạng JSON, bao gồm layout, components, và các thiết lập khác';

comment on column generic_pages.status is 'Trạng thái của trang: draft (nháp), published (đã xuất bản), archived (đã lưu trữ)';

comment on column generic_pages.created_at is 'Thời điểm tạo trang, dạng Unix timestamp (milliseconds)';

comment on column generic_pages.updated_at is 'Thời điểm cập nhật trang gần nhất, dạng Unix timestamp (milliseconds)';

comment on column generic_pages.published_at is 'Thời điểm xuất bản trang, dạng Unix timestamp (milliseconds)';

comment on column generic_pages.created_by is 'ID của người dùng tạo trang';

comment on column generic_pages.updated_by is 'ID của người dùng cập nhật trang gần nhất';

alter table generic_pages
    owner to root;

create index idx_generic_pages_status
    on generic_pages (status);

create index idx_generic_pages_created_at
    on generic_pages (created_at);

grant delete, insert, select, update on generic_pages to member;

create table generic_page_templates
(
    id          varchar(36)  not null
        primary key,
    name        varchar(255) not null,
    description text,
    category    varchar(100),
    thumbnail   varchar(255),
    config      jsonb        not null,
    created_at  bigint       not null,
    updated_at  bigint       not null,
    created_by  varchar(36),
    updated_by  varchar(36)
);

comment on table generic_page_templates is 'Lưu trữ các mẫu trang có thể tái sử dụng để tạo trang mới nhanh chóng';

comment on column generic_page_templates.id is 'ID duy nhất của mẫu trang, dạng UUID';

comment on column generic_page_templates.name is 'Tên của mẫu trang, hiển thị trong giao diện quản trị';

comment on column generic_page_templates.description is 'Mô tả chi tiết về mẫu trang và mục đích sử dụng';

comment on column generic_page_templates.category is 'Danh mục của mẫu trang, dùng để phân loại và lọc';

comment on column generic_page_templates.thumbnail is 'URL hình thu nhỏ minh họa cho mẫu trang';

comment on column generic_page_templates.config is 'Cấu hình mẫu trang dạng JSON, bao gồm layout, components, và các thiết lập khác';

comment on column generic_page_templates.created_at is 'Thời điểm tạo mẫu trang, dạng Unix timestamp (milliseconds)';

comment on column generic_page_templates.updated_at is 'Thời điểm cập nhật mẫu trang gần nhất, dạng Unix timestamp (milliseconds)';

comment on column generic_page_templates.created_by is 'ID của người dùng tạo mẫu trang';

comment on column generic_page_templates.updated_by is 'ID của người dùng cập nhật mẫu trang gần nhất';

alter table generic_page_templates
    owner to root;

create index idx_generic_page_templates_category
    on generic_page_templates (category);

grant delete, insert, select, update on generic_page_templates to member;

create table generic_page_template_tags
(
    template_id varchar(36) not null
        references generic_page_templates
            on delete cascade,
    tag         varchar(50) not null,
    primary key (template_id, tag)
);

comment on table generic_page_template_tags is 'Lưu trữ các tag cho mẫu trang, hỗ trợ tìm kiếm và phân loại';

comment on column generic_page_template_tags.template_id is 'ID của mẫu trang, tham chiếu đến bảng generic_page_templates';

comment on column generic_page_template_tags.tag is 'Tên tag, dùng để phân loại và tìm kiếm mẫu trang';

alter table generic_page_template_tags
    owner to root;

grant delete, insert, select, update on generic_page_template_tags to member;

create table generic_page_submissions
(
    id         varchar(36)                                            not null
        primary key,
    page_id    varchar(36)                                            not null
        references generic_pages
            on delete cascade,
    data       jsonb                                                  not null,
    status     submission_status default 'pending'::submission_status not null,
    created_at bigint                                                 not null,
    updated_at bigint                                                 not null,
    ip_address varchar(45),
    user_agent text,
    user_id    varchar(36)
);

alter table generic_page_submissions
    owner to root;

create index idx_generic_page_submissions_status
    on generic_page_submissions (status);

create index idx_generic_page_submissions_page_id
    on generic_page_submissions (page_id);

grant delete, insert, select, update on generic_page_submissions to member;

create type user_type as enum ('INDIVIDUAL', 'BUSINESS');

alter type user_type owner to root;

create type gender as enum ('MALE', 'FEMALE', 'OTHER');

alter type gender owner to root;

create type auth_method_enum as enum ('PASSWORD', 'SMS', 'EMAIL', 'ZALO', 'TOTP', 'RECOVERY', 'FACEBOOK');

alter type auth_method_enum owner to root;

create type auth_status_enum as enum ('SUCCESS', 'FAILED', 'EXPIRED');

alter type auth_status_enum owner to root;

create type discount_type_enum as enum ('PERCENTAGE', 'FIXED_AMOUNT');

alter type discount_type_enum owner to root;

create type coupon_status_enum as enum ('ACTIVE', 'INACTIVE', 'EXPIRED');

alter type coupon_status_enum owner to root;

create type transaction_status as enum ('PENDING', 'CONFIRMED', 'FAILED', 'REFUNDED');

alter type transaction_status owner to root;

create type transaction_type_enum as enum ('POINT_PURCHASE', 'POINT_WITHDRAWAL');

alter type transaction_type_enum owner to root;

create type webhook_status_enum as enum ('SUCCESS', 'FAILED', 'PROCESSING');

alter type webhook_status_enum owner to root;

create type affiliate_account_status as enum ('ACTIVE', 'REJECT', 'PENDING', 'RE_REGISTER', 'STEP0', 'STEP1', 'STEP2', 'STEP3', 'STEP4');

alter type affiliate_account_status owner to root;

create type contract_type_enum as enum ('INDIVIDUAL', 'BUSINESS');

alter type contract_type_enum owner to root;

create type contract_status_enum as enum ('DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED');

alter type contract_status_enum owner to root;

create type sign_method_enum as enum ('OTP', 'UPLOAD');

alter type sign_method_enum owner to root;

create type ticket_status as enum ('OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED');

alter type ticket_status owner to root;

create type ticket_priority as enum ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

alter type ticket_priority owner to root;

create type sender_type_enum as enum ('USER', 'STAFF');

alter type sender_type_enum owner to root;

create type subscription_status as enum ('ACTIVE', 'CANCELLED', 'EXPIRED', 'PENDING');

alter type subscription_status owner to root;

create type invoice_status as enum ('PENDING', 'PAID', 'FAILED', 'CANCELLED', 'REFUNDED');

alter type invoice_status owner to root;

create type payment_status as enum ('PENDING', 'SUCCESS', 'FAILED');

alter type payment_status owner to root;

create type package_type as enum ('TIME_ONLY', 'HYBRID');

alter type package_type owner to root;

create type point_conversion_history_status as enum ('PENDING', 'SUCCESS', 'FAILED');

alter type point_conversion_history_status owner to root;

create type knowledge_file_status as enum ('PENDING', 'APPROVED', 'REJECTED', 'DRAFT', 'DELETED');

alter type knowledge_file_status owner to root;

create type owner_type as enum ('USER', 'ADMIN');

alter type owner_type owner to root;

create type media_status as enum ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'DELETED');

alter type media_status owner to root;

create type strategy_status as enum ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'DELETED');

alter type strategy_status owner to root;

create type rank_strategy_status as enum ('APPROVED', 'DELETED');

alter type rank_strategy_status owner to root;

create type providers_status as enum ('PENDING', 'APPROVED', 'REJECTED', 'DRAFT', 'DELETED');

alter type providers_status owner to root;

create type product_status as enum ('PENDING', 'APPROVED', 'REJECTED', 'DRAFT', 'DELETED');

alter type product_status owner to root;

create type product_category as enum ('AGENT', 'KNOWLEDGE_FILE', 'FUNCTION', 'FINETUNE', 'STRATEGY');

alter type product_category owner to root;

create type price_type as enum ('NO_PRICE', 'HAS_PRICE', 'STRING_PRICE');

alter type price_type owner to root;

create type model_status as enum ('DRAFT', 'APPROVED', 'DELETED');

alter type model_status owner to root;

create type warehouse_type as enum ('PHYSICAL', 'VIRTUAL');

alter type warehouse_type owner to root;

create type transaction_type as enum ('INBOUND', 'OUTBOUND', 'ADJUSTMENT');

alter type transaction_type owner to root;

create type data_finetuning_status as enum ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'DELETED');

alter type data_finetuning_status owner to root;

create type tools_status as enum ('DRAFT', 'APPROVED', 'DEPRECATED');

alter type tools_status owner to root;

create type access_type as enum ('PUBLIC', 'PRIVATE', 'RESTRICTED');

alter type access_type owner to root;

create type agent_status_enum as enum ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED');

alter type agent_status_enum owner to root;

create type agent_template_status as enum ('DRAFT', 'ACTIVE', 'INACTIVE');

alter type agent_template_status owner to root;

create type type_agent_status as enum ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED');

alter type type_agent_status owner to root;

create type user_product_status as enum ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'DELETED');

alter type user_product_status owner to root;

create type custom_field_status as enum ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'DELETED');

alter type custom_field_status owner to root;

create type page_status as enum ('draft', 'published', 'archived');

alter type page_status owner to root;

create type submission_status as enum ('pending', 'processed', 'rejected');

alter type submission_status owner to root;