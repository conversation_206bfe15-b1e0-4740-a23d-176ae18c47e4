// No need for NestJS testing imports

// Mock SortDirection enum
enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

// Mock các DTO
class CreateUrlDto {
  url: string;
  title: string;
  content: string;
  type?: string;
  tags?: string[];
}

class UpdateUrlDto {
  url?: string;
  title?: string;
  content?: string;
  type?: string;
  tags?: string[];
}

class FindAllUrlDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: SortDirection;
  keyword?: string;
  type?: string;
  tags?: string[];
}

// Mock các module cần thiết

const ApiResponseMock = {
  success: jest.fn().mockImplementation((data, message) => ({
    success: true,
    data,
    message
  })),
  created: jest.fn().mockImplementation((data, message) => ({
    success: true,
    data,
    message
  }))
};

// Mock UrlUserController
class UrlUserController {
  constructor(private readonly urlUserService: any) {}

  async findAll(user: any, queryParams: FindAllUrlDto) {
    const result = await this.urlUserService.findUrlsByOwner(
      user.id,
      queryParams.page || 1,
      queryParams.limit || 10,
      queryParams.sortBy || 'createdAt',
      queryParams.sortDirection || SortDirection.DESC,
      queryParams.keyword,
      queryParams.type,
      queryParams.tags || []
    );
    return ApiResponseMock.success(result);
  }

  async findOne(user: any, id: string) {
    const result = await this.urlUserService.findUrlById(user.id, id);
    return ApiResponseMock.success(result);
  }

  async create(user: any, createUrlDto: CreateUrlDto) {
    const result = await this.urlUserService.createUrl(user.id, createUrlDto);
    return ApiResponseMock.created(result, 'URL đã được tạo thành công');
  }

  async update(user: any, id: string, updateUrlDto: UpdateUrlDto) {
    const result = await this.urlUserService.updateUrl(id, user.id, updateUrlDto);
    return ApiResponseMock.success(result, 'URL đã được cập nhật thành công');
  }

  async remove(user: any, id: string) {
    await this.urlUserService.deleteUrl(id, user.id);
    return ApiResponseMock.success(null, 'URL đã được xóa thành công');
  }
}

// Mock UrlUserService
class UrlUserService {
  findUrlById = jest.fn();
  findUrlsByOwner = jest.fn();
  searchUrls = jest.fn();
  createUrl = jest.fn();
  updateUrl = jest.fn();
  deleteUrl = jest.fn();
}

// Rename ApiResponseMock to ApiResponse for tests
const ApiResponse = ApiResponseMock;

describe('Kiểm thử Controller URL của người dùng', () => {
  let controller: UrlUserController;
  let service: UrlUserService;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
    isActive: true,
  };

  const mockUrls = [
    { ...mockUrl },
    {
      ...mockUrl,
      id: 'test-id-2',
      url: 'https://example.com/2',
      title: 'Test URL 2',
    },
  ];

  const mockPaginatedResult = {
    items: mockUrls,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  const mockUser = {
    id: 1,
    email: '<EMAIL>',
  };

  beforeEach(async () => {
    // Tạo service và controller
    service = new UrlUserService();

    // Setup mock methods
    service.findUrlById.mockResolvedValue(mockUrl);
    service.findUrlsByOwner.mockResolvedValue(mockPaginatedResult);
    service.searchUrls.mockResolvedValue(mockUrls);
    service.createUrl.mockResolvedValue(mockUrl);
    service.updateUrl.mockResolvedValue(mockUrl);
    service.deleteUrl.mockResolvedValue(undefined);

    // Tạo controller với service đã mock
    controller = new UrlUserController(service);
  });

  it('Controller phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll - Lấy danh sách URL', () => {
    it('Phải trả về danh sách URL có phân trang của người dùng', async () => {
      const queryParams: FindAllUrlDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      };

      const result = await controller.findAll(mockUser, queryParams);
      expect(result).toEqual(ApiResponse.success(mockPaginatedResult));
      expect(service.findUrlsByOwner).toHaveBeenCalledWith(
        mockUser.id,
        queryParams.page,
        queryParams.limit,
        queryParams.sortBy,
        queryParams.sortDirection,
        undefined,
        undefined,
        []
      );
    });

    it('Phải xử lý được các tham số tìm kiếm', async () => {
      const queryParams: FindAllUrlDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        keyword: 'test',
        type: 'web',
        tags: ['tag1', 'tag2'],
      };

      const result = await controller.findAll(mockUser, queryParams);
      expect(result).toEqual(ApiResponse.success(mockPaginatedResult));
      expect(service.findUrlsByOwner).toHaveBeenCalledWith(
        mockUser.id,
        queryParams.page,
        queryParams.limit,
        queryParams.sortBy,
        queryParams.sortDirection,
        queryParams.keyword,
        queryParams.type,
        queryParams.tags
      );
    });
  });

  describe('findOne - Lấy thông tin chi tiết URL', () => {
    it('Phải trả về thông tin URL theo ID', async () => {
      const result = await controller.findOne(mockUser, 'test-id');
      expect(result).toEqual(ApiResponse.success(mockUrl));
      expect(service.findUrlById).toHaveBeenCalledWith(mockUser.id, 'test-id');
    });
  });

  describe('create - Tạo URL mới', () => {
    it('Phải tạo được URL mới', async () => {
      const createUrlDto: CreateUrlDto = {
        url: 'https://example.com',
        title: 'Test URL',
        content: 'Test content',
        type: 'web',
        tags: ['test'],
      };

      const result = await controller.create(mockUser, createUrlDto);
      expect(result).toEqual(ApiResponse.created(mockUrl, 'URL đã được tạo thành công'));
      expect(service.createUrl).toHaveBeenCalledWith(mockUser.id, createUrlDto);
    });
  });

  describe('update - Cập nhật URL', () => {
    it('Phải cập nhật được URL đã tồn tại', async () => {
      const updateUrlDto: UpdateUrlDto = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      const result = await controller.update(mockUser, 'test-id', updateUrlDto);
      expect(result).toEqual(ApiResponse.success(mockUrl, 'URL đã được cập nhật thành công'));
      expect(service.updateUrl).toHaveBeenCalledWith('test-id', mockUser.id, updateUrlDto);
    });
  });

  describe('remove - Xóa URL', () => {
    it('Phải xóa được URL đã tồn tại', async () => {
      const result = await controller.remove(mockUser, 'test-id');
      expect(result).toEqual(ApiResponse.success(null, 'URL đã được xóa thành công'));
      expect(service.deleteUrl).toHaveBeenCalledWith('test-id', mockUser.id);
    });
  });
});
