import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { MediaAdminService } from '../services/media-admin.service';
import { MediaQueryDto } from '@/modules/data/media/dto/media-query.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { MediaResponseDto } from '@/modules/data/media/dto/media-user.dto';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { MEDIA_ERROR_CODES } from '../../exception';
import { AdminMediaResponseDto } from '../../dto/media-admin.dto';
import { DeleteMediaDto } from '../../dto/delete-media.dto';
import { DeleteAgentMediaDto } from '../../dto/delete-agent-media.dto';
import { DeleteAllAgentMediaDto } from '../../dto/delete-all-agent-media.dto';
/**
 * Controller handling APIs related to media management for admin
 */
@ApiTags('Admin - Media')
@ApiExtraModels(
  ApiResponseDto,
  MediaResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
  AdminMediaResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/media')
export class MediaAdminController {
  constructor(private readonly mediaAdminService: MediaAdminService) {}

  /**
   * Get paginated list of media for admin
   */
  @Get()
  @ApiOperation({ summary: 'Get list of media for admin' })
  // @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  // @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({
    status: 200,
    description: 'List of media retrieved successfully.',
    schema: ApiResponseDto.getPaginatedSchema(AdminMediaResponseDto),
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.NOT_FOUND,
    MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
  )
  async getAllMediaForAdmin(
    @Query() query: MediaQueryDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<AdminMediaResponseDto>>> {
    const result = await this.mediaAdminService.findAllForAdmin(
      query,
      employee.isAdmin,
    );
    return ApiResponseDto.paginated(result);
  }

  /**
   * Get media details by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get media details by ID' })
  @ApiParam({ name: 'id', description: 'ID of the media' })
  @ApiResponse({
    status: 200,
    description: 'Media details retrieved successfully.',
    schema: ApiResponseDto.getSchema(MediaResponseDto),
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.NOT_FOUND,
    MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
  )
  async findOne(
    @Param('id') id: string,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<AdminMediaResponseDto>> {
    const result = await this.mediaAdminService.findByIdForAdmin(
      id,
      employee.isAdmin,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa liên kết nhiều agent với một media bởi admin
   */
  @Delete('agent-media')
  @ApiOperation({
    summary: 'Xóa liên kết nhiều agent với một media bởi admin',
    description:
      'Chỉ admin mới có thể thực hiện hành động này. Xóa liên kết nhiều agent khỏi một media trong bảng agent_media (truyền mediaId và danh sách agentIds).',
  })
  @ApiResponse({
    status: 200,
    description:
      'Trả về kết quả xóa với ba danh sách: deletedIds, skippedIds, và failedIds.',
    schema: {
      example: {
        deletedIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'],
        skippedIds: ['123e4567-e89b-12d3-a456-426614174002'],
        failedIds: [{ id: '123e4567-e89b-12d3-a456-426614174003', reason: 'Error message' }],
      },
    },
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.BAD_REQUEST,
    MEDIA_ERROR_CODES.FORBIDDEN,
  )
  async deleteAgentMediaByAdmin(
    @Body() dto: DeleteAgentMediaDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<{
    deletedIds: string[];
    skippedIds: string[];
    failedIds: { id: string; reason: string }[];
  }>> {
    const result = await this.mediaAdminService.deleteAgentMediaByAdmin(
      dto,
      employee.isAdmin,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa tất cả liên kết của một media với các agent bởi admin
   */
  @Delete('agent-media/all')
  @ApiOperation({
    summary: 'Xóa tất cả liên kết của một media với các agent bởi admin',
    description:
      'Chỉ admin mới có thể thực hiện hành động này. Xóa tất cả liên kết của một media với các agent trong bảng agent_media (chỉ truyền mediaId).',
  })
  @ApiResponse({
    status: 200,
    description:
      'Trả về kết quả xóa với số lượng liên kết đã xóa.',
    schema: {
      example: {
        deletedCount: 5,
        mediaId: '123e4567-e89b-12d3-a456-426614174000'
      },
    },
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.BAD_REQUEST,
    MEDIA_ERROR_CODES.FORBIDDEN,
  )
  async deleteAllAgentMediaByAdmin(
    @Body() dto: DeleteAllAgentMediaDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<{
    deletedCount: number;
    mediaId: string;
  }>> {
    const result = await this.mediaAdminService.deleteAllAgentMediaByAdmin(
      dto.mediaId,
      employee.isAdmin,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa mềm media bởi admin (cập nhật trạng thái thành DELETED)
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa mềm media bởi admin',
    description:
      'Chỉ admin mới có thể thực hiện hành động này. Cập nhật trạng thái media thành DELETED.',
  })
  @ApiResponse({
    status: 200,
    description:
      'Trả về kết quả xóa với ba danh sách: deletedIds, skippedIds, và failedIds.',
    schema: {
      example: {
        deletedIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'],
        skippedIds: ['123e4567-e89b-12d3-a456-426614174002'],
        failedIds: [{ id: '123e4567-e89b-12d3-a456-426614174003', reason: 'Error message' }],
      },
    },
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.BAD_REQUEST,
    MEDIA_ERROR_CODES.FORBIDDEN,
  )
  async deleteMediaByAdmin(
    @Body() dto: DeleteMediaDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<{
    deletedIds: string[];
    skippedIds: string[];
    failedIds: { id: string; reason: string }[];
  }>> {
    const result = await this.mediaAdminService.deleteManyMediaByAdmin(
      dto.mediaIds,
      employee.isAdmin,
    );
    return ApiResponseDto.deleted(result);
  }
}