import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsInt, IsOptional, IsPhoneNumber, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp
 */
export enum AudienceSortField {
  ID = 'id',
  EMAIL = 'email',
  PHONE = 'phone',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho thứ tự sắp xếp
 */
export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho query parameters khi lấy danh sách audience
 */
export class AudienceQueryDto {
  /**
   * Trang hiện tại (bắt đầu từ 1)
   * @example 1
   */
  @ApiProperty({
    description: 'Trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Trang phải là số nguyên' })
  @Min(1, { message: 'Trang phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  page?: number = 1;

  /**
   * Số lượng item trên mỗi trang
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Số lượng item phải là số nguyên' })
  @Min(1, { message: 'Số lượng item phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Tìm kiếm theo email
   * @example "example.com"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo email',
    example: 'example.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Email phải là chuỗi' })
  email?: string;

  /**
   * Tìm kiếm theo số điện thoại
   * @example "+84"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo số điện thoại',
    example: '+84',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  phone?: string;

  /**
   * Tìm kiếm theo tag ID
   * @example 1
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tag ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Tag ID phải là số nguyên' })
  @Type(() => Number)
  tagId?: number;

  /**
   * Tìm kiếm theo tên trường tùy chỉnh
   * @example "address"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên trường tùy chỉnh',
    example: 'address',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên trường tùy chỉnh phải là chuỗi' })
  customFieldName?: string;

  /**
   * Tìm kiếm theo giá trị trường tùy chỉnh
   * @example "Hanoi"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo giá trị trường tùy chỉnh',
    example: 'Hanoi',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giá trị trường tùy chỉnh phải là chuỗi' })
  customFieldValue?: string;

  /**
   * Sắp xếp theo trường
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: AudienceSortField,
    example: AudienceSortField.CREATED_AT,
    default: AudienceSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(AudienceSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(AudienceSortField).join(', ')}`,
  })
  sortBy?: AudienceSortField = AudienceSortField.CREATED_AT;

  /**
   * Thứ tự sắp xếp
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    enum: SortOrder,
    example: SortOrder.DESC,
    default: SortOrder.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortOrder, {
    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortOrder).join(', ')}`,
  })
  sortOrder?: SortOrder = SortOrder.DESC;
}
