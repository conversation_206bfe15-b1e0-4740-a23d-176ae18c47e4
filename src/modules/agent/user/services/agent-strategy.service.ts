import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';

import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { STRATEGY_ERROR_CODES } from '@modules/agent/exceptions/strategy-error.code';

import { AgentUserRepository } from '@modules/agent/repositories';
import {
  StrategyAgentRepository,
  StrategyAgentVersionRepository,
  StrategyContentStepRepository,
  UserStrategyAgentRepository,
  UserStrategyVersionContentRepository
} from '@modules/strategy/repositories';
import { StrategyAgent, StrategyAgentVersion } from '@modules/strategy/entities';

import { AgentStrategyDto, UpdateAgentStrategyDto } from '../dto';
import { AssignStrategyToAgentDto } from '../dto/agent/assign-strategy.dto';

/**
 * Service xử lý các thao tác liên quan đến strategy của agent
 */
@Injectable()
export class AgentStrategyService {
  private readonly logger = new Logger(AgentStrategyService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly userStrategyAgentRepository: UserStrategyAgentRepository,
    private readonly strategyAgentRepository: StrategyAgentRepository,
    private readonly strategyAgentVersionRepository: StrategyAgentVersionRepository,
    private readonly strategyContentStepRepository: StrategyContentStepRepository,
    private readonly userStrategyVersionContentRepository: UserStrategyVersionContentRepository,
  ) {}

  /**
   * Xóa các bản ghi liên quan đến agent trong user_strategy_version_content
   * @param agentId ID của agent
   */
  async deleteAgentStrategyContent(agentId: string): Promise<void> {
    try {
      await this.userStrategyVersionContentRepository.deleteByAgentId(agentId);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nội dung strategy của agent: ${error.message}`);
      // Không ném lỗi để không ảnh hưởng đến quá trình xóa agent
    }
  }

  /**
   * Gán strategy cho agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param dto Thông tin strategy
   */
  @Transactional()
  async assignStrategyToAgent(
    agentId: string,
    userId: number,
    dto: AssignStrategyToAgentDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent tồn tại và thuộc về user
      const agentUser = await this.agentUserRepository.createBaseQuery()
        .select(['agentUser.id', 'agentUser.userId'])
        .where('agentUser.id = :id', { id: agentId })
        .andWhere('agentUser.userId = :userId', { userId })
        .getOne();

      if (!agentUser) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Nếu strategyId là null hoặc undefined, gỡ strategy khỏi agent
      if (!dto.strategyId) {
        await this.removeStrategyFromAgent(agentId, userId);
        return;
      }

      // Kiểm tra strategy tồn tại
      let strategy: StrategyAgent | null;
      try {
        strategy = await this.strategyAgentRepository.findById(dto.strategyId);
      } catch (error) {
        this.logger.error(`Lỗi khi truy vấn strategy: ${error.message}`);
        // Xử lý lỗi liên quan đến rankStrategy
        if (error.message && error.message.includes('rankStrategy')) {
          throw new AppException(
            STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED,
            'Bạn chưa sở hữu strategy này. Vui lòng mua strategy này để sử dụng.'
          );
        }
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED, error.message);
      }

      if (!strategy) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Kiểm tra user có quyền sử dụng strategy không
      const userStrategy = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(
        userId,
        dto.strategyId,
      );
      if (!userStrategy) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ACCESS_DENIED);
      }

      // Kiểm tra strategy có phiên bản nào không
      const versions = await this.strategyAgentVersionRepository.findByStrategyId(dto.strategyId);
      if (!versions || versions.length === 0) {
        throw new AppException(
          STRATEGY_ERROR_CODES.STRATEGY_NO_VERSIONS,
          `Chiến lược với ID ${dto.strategyId} không có phiên bản nào.`
        );
      }

      // Nếu có versionId, kiểm tra phiên bản tồn tại
      if (dto.versionId) {
        const version = await this.strategyAgentVersionRepository.findById(dto.versionId);
        if (!version || version.strategyAgentId !== dto.strategyId) {
          throw new AppException(
            STRATEGY_ERROR_CODES.STRATEGY_VERSION_NOT_FOUND,
            `Không tìm thấy phiên bản với ID ${dto.versionId} cho strategy ${dto.strategyId}`
          );
        }
      }

      // Kiểm tra agent đã có cấu hình với strategy chưa
      const existingConfig = await this.checkAgentStrategyConfig(agentId, dto.strategyId);

      if (!existingConfig) {
        // Nếu chưa có, tạo mới các bản ghi
        await this.createAgentStrategyConfig(agentId, userId, dto.strategyId, dto.versionId);
      } else if (dto.versionId) {
        // Nếu đã có cấu hình nhưng muốn cập nhật phiên bản
        await this.createAgentStrategyConfig(agentId, userId, dto.strategyId, dto.versionId);
      }

      // Cập nhật strategyId cho agent
      await this.agentUserRepository.update(agentId, {
        strategyId: parseInt(dto.strategyId),
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gán strategy cho agent: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ASSIGN_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin strategy của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param versionId ID của phiên bản strategy (nếu không cung cấp, sẽ sử dụng phiên bản hiện tại)
   * @returns Thông tin strategy của agent
   */
  async getAgentStrategy(agentId: string, userId: number, versionId?: number): Promise<AgentStrategyDto | null> {
    try {
      // Kiểm tra agent có tồn tại không và người dùng có quyền truy cập không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agentUser } = result;

      // Nếu agent không có strategy, trả về lỗi
      if (!agentUser.strategyId) {
        throw new AppException(
          STRATEGY_ERROR_CODES.STRATEGY_NOT_ASSIGNED,
          `Agent với ID ${agentId} chưa được gán strategy nào.`
        );
      }

      // Tìm strategy
      let strategy: StrategyAgent | null;
      try {
        strategy = await this.strategyAgentRepository.findById(agentUser.strategyId.toString());
      } catch (error) {
        this.logger.error(`Lỗi khi truy vấn strategy: ${error.message}`);
        // Xử lý lỗi liên quan đến rankStrategy
        if (error.message && error.message.includes('rankStrategy')) {
          throw new AppException(
            STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED,
            'Không thể truy vấn thông tin strategy do lỗi cấu hình. Vui lòng liên hệ quản trị viên.'
          );
        }
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED, error.message);
      }

      if (!strategy) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Nếu có versionId, kiểm tra phiên bản tồn tại
      if (versionId) {
        const version = await this.strategyAgentVersionRepository.findById(versionId);
        if (!version || version.strategyAgentId !== agentUser.strategyId.toString()) {
          throw new AppException(
            STRATEGY_ERROR_CODES.STRATEGY_VERSION_NOT_FOUND,
            `Không tìm thấy phiên bản với ID ${versionId} cho strategy ${agentUser.strategyId}`
          );
        }

        // Tìm user_strategy_version cho phiên bản này
        const userStrategyAgent = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(
          userId,
          agentUser.strategyId.toString()
        );

        if (!userStrategyAgent) {
          throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ACCESS_DENIED);
        }

        // Lấy các step từ strategy_content_step
        const contentSteps = await this.strategyContentStepRepository.findByStrategyVersionId(versionId);

        // Kiểm tra xem đã có nội dung cho agent và phiên bản này chưa
        const existingContent = await this.userStrategyVersionContentRepository.findByAgentIdAndStrategyId(
          agentId,
          agentUser.strategyId.toString()
        );

        // Nếu chưa có nội dung, tạo mới
        if (!existingContent || existingContent.length === 0) {
          // Tạo user_strategy_version_content cho từng bước
          if (contentSteps.length > 0) {
            await this.userStrategyVersionContentRepository.createBulkContent(
              versionId, // Sử dụng versionId trực tiếp
              agentId,
              contentSteps.map(step => ({
                id: step.id,
                example: step.example,
                stepOrder: step.stepOrder,
              }))
            );
          }
        }

        // Lấy các bước nội dung của phiên bản cụ thể
        const versionContentSteps = await this.userStrategyVersionContentRepository.findByAgentIdAndStrategyId(
          agentId,
          agentUser.strategyId.toString()
        );

        return {
          id: strategy.id,
          name: strategy.name,
          description: strategy.description || '',
          versionId: versionId,
          versionName: version.versionName,
          steps: versionContentSteps.map(step => ({
            id: step.id,
            stepOrder: step.stepOrder,
            editableExample: step.editableExample,
            edited: step.edited,
          })),
        };
      } else {
        // Nếu không có versionId, kiểm tra xem strategy có phiên bản nào không
        const versions = await this.strategyAgentVersionRepository.findByStrategyId(agentUser.strategyId.toString());
        if (!versions || versions.length === 0) {
          throw new AppException(
            STRATEGY_ERROR_CODES.STRATEGY_NO_VERSIONS,
            `Chiến lược với ID ${agentUser.strategyId} không có phiên bản nào.`
          );
        }

        // Lấy phiên bản hiện tại
        const contentSteps = await this.userStrategyVersionContentRepository.findByAgentIdAndStrategyId(
          agentId,
          agentUser.strategyId.toString()
        );

        // Tìm thông tin phiên bản hiện tại
        const userStrategyAgent = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(
          userId,
          agentUser.strategyId.toString()
        );

        let versionId: number | undefined = undefined;
        let versionName: string | undefined = undefined;

        if (userStrategyAgent) {
          // Sử dụng phiên bản mới nhất
          const latestVersion = versions[0]; // Đã sắp xếp theo version_number DESC
          versionId = latestVersion.id;
          versionName = latestVersion.versionName;
        }

        return {
          id: strategy.id,
          name: strategy.name,
          description: strategy.description || '',
          versionId,
          versionName,
          steps: contentSteps.map(step => ({
            id: step.id,
            stepOrder: step.stepOrder,
            editableExample: step.editableExample,
            edited: step.edited,
          })),
        };
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin strategy của agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED, error.message);
    }
  }

  /**
   * Cập nhật strategy cho agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin cập nhật strategy
   */
  @Transactional()
  async updateAgentStrategy(
    agentId: string,
    userId: number,
    updateDto: UpdateAgentStrategyDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không và người dùng có quyền truy cập không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Kiểm tra strategy tồn tại
      const strategy = await this.strategyAgentRepository.findById(updateDto.strategyId);
      if (!strategy) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Kiểm tra user có quyền sử dụng strategy không
      const userStrategy = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(
        userId,
        updateDto.strategyId,
      );
      if (!userStrategy) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ACCESS_DENIED);
      }

      // Cập nhật strategyId cho agent
      await this.agentUserRepository.update(agentId, {
        strategyId: parseInt(updateDto.strategyId),
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật strategy cho agent: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ASSIGN_FAILED, error.message);
    }
  }

  /**
   * Gỡ strategy khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async removeStrategyFromAgent(agentId: string, userId: number): Promise<void> {
    try {
      // Kiểm tra agent tồn tại và thuộc về user
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Cập nhật strategyId cho agent thành null
      await this.agentUserRepository
        .createQueryBuilder()
        .update('agents_user')
        .set({ strategy_id: null })
        .where('id = :id', { id: agentId })
        .execute();
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ strategy khỏi agent: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_REMOVE_FAILED, error.message);
    }
  }

  /**
   * Kiểm tra agent đã có cấu hình với strategy chưa
   * @param agentId ID của agent
   * @param strategyId ID của strategy
   */
  private async checkAgentStrategyConfig(agentId: string, strategyId: string): Promise<boolean> {
    try {
      return await this.userStrategyVersionContentRepository.existsByAgentIdAndStrategyId(agentId, strategyId);
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra cấu hình strategy cho agent: ${error.message}`);
      return false;
    }
  }

  /**
   * Tạo cấu hình strategy cho agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param strategyId ID của strategy
   * @returns Danh sách các step đã tạo
   */
  @Transactional()
  private async createAgentStrategyConfig(
    agentId: string,
    userId: number,
    strategyId: string,
    versionId?: number,
  ): Promise<void> {
    try {
      // Tìm user_strategy_agent
      const userStrategyAgent = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(
        userId,
        strategyId,
      );
      if (!userStrategyAgent) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ACCESS_DENIED);
      }

      // Kiểm tra xem đã có nội dung cho agent và strategy này chưa
      const existingContent = await this.userStrategyVersionContentRepository.findByAgentIdAndStrategyId(
        agentId,
        strategyId
      );

      // Tìm phiên bản của strategy (theo versionId hoặc phiên bản mới nhất)
      let strategyVersion: StrategyAgentVersion | null;
      if (versionId) {
        // Nếu có versionId, tìm phiên bản theo ID
        strategyVersion = await this.strategyAgentVersionRepository.findById(versionId);
        if (!strategyVersion || strategyVersion.strategyAgentId !== strategyId) {
          throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_VERSION_NOT_FOUND,
            `Không tìm thấy phiên bản với ID ${versionId} cho strategy ${strategyId}`);
        }
      } else {
        // Nếu không có versionId, tìm phiên bản mới nhất
        strategyVersion = await this.strategyAgentVersionRepository.findLatestVersion(strategyId);
      }

      if (!strategyVersion) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_VERSION_NOT_FOUND);
      }

      // Nếu chưa có nội dung hoặc cần cập nhật phiên bản
      if (!existingContent || existingContent.length === 0 || versionId) {
        // Nếu đã có nội dung và cần cập nhật, xóa nội dung cũ
        if (existingContent && existingContent.length > 0) {
          await this.userStrategyVersionContentRepository.deleteByAgentId(agentId);
        }

        // Lấy các step từ strategy_content_step
        const contentSteps = await this.strategyContentStepRepository.findByStrategyVersionId(strategyVersion.id);

        this.logger.log(`Tìm thấy ${contentSteps.length} step từ strategy gốc, tiến hành tạo nội dung`);

        // Tạo user_strategy_version_content cho từng bước
        if (contentSteps.length > 0) {
          await this.userStrategyVersionContentRepository.createBulkContent(
            strategyVersion.id, // Sử dụng strategyVersion.id trực tiếp
            agentId,
            contentSteps.map(step => ({
              id: step.id,
              example: step.example,
              stepOrder: step.stepOrder,
            }))
          );
          this.logger.log(`Đã tạo thành công ${contentSteps.length} step cho agent ${agentId}`);
        }
      } else {
        this.logger.log(`Đã tìm thấy ${existingContent.length} step cho agent ${agentId}, không cần tạo mới`);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo cấu hình strategy cho agent: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_ASSIGN_FAILED, error.message);
    }
  }
}
