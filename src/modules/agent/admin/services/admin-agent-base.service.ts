import { PaginatedResult } from '@/common/response';
import { AppException, ErrorCode } from '@common/exceptions';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { AgentBaseRepository } from '@modules/agent/repositories/agent-base.repository';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { MultiAgentsSystemRepository } from '@modules/agent/repositories/multi-agents-system.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { TypeProviderEnum } from '@modules/model-training/constants/type-provider.enum';
import { BaseModelAdminService } from '@modules/model-training/admin/services';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { Transactional } from 'typeorm-transactional';
import {
  AgentBaseListItemDto,
  AgentBaseQueryDto,
  AgentBaseResponseDto,
  CreateAgentBaseDto, MultiAgentRelationDto, MultiAgentRelationResponseDto, UpdateAgentBaseDto
} from '../dto/agent-base';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { AvatarUrlHelper } from '../helpers/avatar-url.helper';
import { AgentBaseMapper } from '../mappers/agent-base.mapper';
import { MultiAgentRelationMapper } from '../mappers/multi-agent-relation.mapper';

/**
 * Service xử lý logic nghiệp vụ liên quan đến agent base
 */
@Injectable()
export class AdminAgentBaseService {
  private readonly logger = new Logger(AdminAgentBaseService.name);

  constructor(
    private readonly agentBaseRepository: AgentBaseRepository,
    private readonly agentRepository: AgentRepository,
    private readonly agentSystemRepository: AgentSystemRepository,
    private readonly multiAgentsSystemRepository: MultiAgentsSystemRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly baseModelAdminService: BaseModelAdminService,
  ) { }

  /**
   * Lấy danh sách agent base với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent base với phân trang
   */
  async findAll(
    queryDto: AgentBaseQueryDto,
  ): Promise<PaginatedResult<AgentBaseListItemDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        active,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;

      const result = await this.agentBaseRepository.findPaginated(
        page,
        limit,
        search,
        active,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi từ entity sang DTO sử dụng mapper
      const items = result.items.map((agentBase) => {
        // Tìm agent tương ứng với agentBase
        const agent = result.agents?.find((a) => a.id === agentBase.id);

        if (!agent) {
         throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
        }

        // Sử dụng mapper để chuyển đổi
        return AgentBaseMapper.toListItemDto(agentBase, agent, this.cdnService);
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error finding agent bases: ${error.message}`,
        error.stack,
      );
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Lấy thông tin chi tiết agent base theo ID
   * @param id ID của agent base
   * @returns Thông tin chi tiết agent base
   */
  async findById(id: string): Promise<AgentBaseResponseDto> {

    const agentBase = await this.agentBaseRepository.findById(id);
    if (!agentBase) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
    }

    // Kiểm tra xem agent base đã bị xóa chưa (sử dụng deletedBy thay vì deletedAt)
    if (agentBase.deletedBy) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND, 'Agent base đã bị xóa');
    }

    try {
      // Lấy thông tin agent tương ứng
      const agent = await this.agentRepository.findById(id);
      if (!agent) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Lấy thông tin quan hệ multi-agent
      const multiAgentRelations: MultiAgentRelationResponseDto[] = [];
      try {
        const relations = await this.multiAgentsSystemRepository.findByParentAgentId(id);
        if (relations && relations.length > 0) {
          for (const relation of relations) {
            // Lấy thông tin agent để lấy tên
            const childAgentInfo = await this.agentRepository.findById(relation.childAgentId);

            // Chỉ thêm vào nếu childAgentId khác với id của agent hiện tại
            if (relation.childAgentId !== id) {
              multiAgentRelations.push(
                MultiAgentRelationResponseDto.fromEntity(
                  relation,
                  childAgentInfo?.name
                )
              );
            }
          }
        }
      } catch (error) {
        this.logger.warn(`Lỗi khi lấy thông tin quan hệ multi-agent: ${error.message}`);
      }

      // Sử dụng mapper để chuyển đổi sang DTO
      return await AgentBaseMapper.toResponseDto(
        agentBase,
        agent,
        undefined,
        this.cdnService,
        this.employeeInfoService,
        multiAgentRelations,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding agent base: ${error.message}`,
        error.stack,
      );
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Tạo agent base mới
   * @param createDto Dữ liệu tạo agent base
   * @param employeeId ID của nhân viên tạo
   * @returns Thông tin agent base đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateAgentBaseDto,
    employeeId: number,
  ): Promise<string | null> {
    try {
      // Kiểm tra model có tồn tại không
      await this.validateModel(
        createDto.modelConfig.modelId,
      );

      // Tạo agent mới
      const agent = this.agentRepository.create({
        name: createDto.name,
        modelConfig: createDto.modelConfig,
        instruction: createDto.instruction,
        status: createDto.status || AgentStatusEnum.DRAFT,
      });

      // Tạo URL tải lên avatar (nếu có)
      let avatarUrlUpload: { url: string; key: string | null } | undefined;
      if (createDto.avatarMimeType) {
        // Tạo URL tải lên avatar
        avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
          this.s3Service,
          employeeId.toString(),
          createDto.avatarMimeType,
          agent.id,
        );

        // Cập nhật avatar key cho agent nếu có key mới
        if (avatarUrlUpload.key) {
          agent.avatar = avatarUrlUpload.key;
        }
      }

      // Lưu agent
      await this.agentRepository.save(agent);

      // Tạo agent base mới
      const agentBase = this.agentBaseRepository.create({
        id: agent.id,
        createdBy: employeeId,
        updatedBy: employeeId,
        active: false, // Mặc định không active
      });

      // Nếu active = true, đảm bảo chỉ có một agent base active
      if (createDto.active) {
        agentBase.active = createDto.active;
      }

      // Lưu agent base trước
      const createdAgentBase = await this.agentBaseRepository.save(agentBase);


      if (!createdAgentBase) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
      }

      // Xử lý quan hệ multi-agent nếu có
      if (createDto.multiAgentRelations && createDto.multiAgentRelations.length > 0) {
        await this.createMultiAgentRelations(
          agent.id,
          createDto.multiAgentRelations
        );
      }

      return avatarUrlUpload ? avatarUrlUpload.url : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating agent base: ${error.message}`,
        error.stack,
      );
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Cập nhật thông tin agent base
   * @param id ID của agent base
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns Thông tin agent base đã cập nhật
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentBaseDto,
    employeeId: number,
  ): Promise<string | null> {
    // Kiểm tra agent base có tồn tại không
    const agentBaseExists = await this.agentBaseRepository.existsById(id);
    if (!agentBaseExists) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
    }

    // Kiểm tra agent có tồn tại không
    const agentExists = await this.agentRepository.existsById(id);
    if (!agentExists) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Sau khi đã kiểm tra tồn tại, lấy thông tin chi tiết
    // Lấy thông tin agent (cần cho avatar)
    const agent = await this.agentRepository.findById(id);
    if (!agent) {
      // Trường hợp này không nên xảy ra vì đã kiểm tra tồn tại ở trên
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Lấy thông tin agent base (cần cho cập nhật)
    const agentBase = await this.agentBaseRepository.findById(id);
    if (!agentBase) {
      // Trường hợp này không nên xảy ra vì đã kiểm tra tồn tại ở trên
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
    }

    try {
      // Cập nhật thông tin agent nếu có
      const agentUpdates: Partial<{
        name: string;
        instruction: string | null;
        modelConfig: ModelConfig;
        vectorStoreId: string | null;
        status: AgentStatusEnum;
        avatar: string;
        updatedAt: number;
        rejectionReason?: string | null;
      }> = {};
      let hasAgentUpdates = false;

      if (updateDto.name !== undefined) {
        agentUpdates.name = updateDto.name;
        hasAgentUpdates = true;
      }

      if (updateDto.instruction !== undefined) {
        agentUpdates.instruction = updateDto.instruction;
        hasAgentUpdates = true;
      }

      if (updateDto.modelConfig !== undefined) {
        // Kiểm tra model có tồn tại không
        await this.validateModel(
          updateDto.modelConfig.modelId,
        );
        agentUpdates.modelConfig = updateDto.modelConfig;
        hasAgentUpdates = true;
      }

      if (updateDto.vectorStoreId !== undefined) {
        agentUpdates.vectorStoreId = updateDto.vectorStoreId;
        hasAgentUpdates = true;
      }

      if (updateDto.status !== undefined) {
        agentUpdates.status = updateDto.status;
        hasAgentUpdates = true;
      }

      // Tạo URL tải lên avatar mới (nếu có)
      let avatarUrlUpload: { url: string; key: string | null } | undefined;
      if (updateDto.avatarMimeType) {
        // Tạo URL tải lên avatar
        avatarUrlUpload = await AvatarUrlHelper.generateUploadUrl(
          this.s3Service,
          employeeId.toString(),
          updateDto.avatarMimeType,
          id,
          agent.avatar || undefined,
        );

        // Cập nhật avatar key cho agent nếu có key mới
        if (avatarUrlUpload.key) {
          agentUpdates.avatar = avatarUrlUpload.key;
          hasAgentUpdates = true;
        }
      }

      // Lưu thông tin agent nếu có cập nhật
      if (hasAgentUpdates) {
        await this.agentRepository.update(id, agentUpdates);
      }

      // Cập nhật thông tin agent base
      agentBase.updatedBy = employeeId;

      // Nếu có cập nhật trạng thái active
      if (updateDto.active !== undefined) {
        if (updateDto.active) {
          // Nếu đặt thành active, sử dụng phương thức setActive
          agentBase.active = true;
        } else {
          // Nếu đặt thành không active, chỉ cập nhật trạng thái
          agentBase.active = false;
        }
      }

      // Lưu thông tin agent base
      await this.agentBaseRepository.save(agentBase);

      return avatarUrlUpload ? avatarUrlUpload.url : null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating agent base: ${error.message}`,
        error.stack,
      );
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Xóa agent base (soft delete)
   * @param id ID của agent base
   * @param employeeId ID của nhân viên xóa
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<void> {
    // Kiểm tra agent base có tồn tại không
    const agentBaseExists = await this.agentBaseRepository.existsById(id);
    if (!agentBaseExists) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
    }

    try {
      // Kiểm tra agent có tồn tại không
      const agentExists = await this.agentRepository.existsById(id);

      // Xóa mềm agent base bằng cách cập nhật trường deletedBy
      const agentBaseDeleted = await this.agentBaseRepository.customSoftDelete(id, employeeId);

      if (!agentBaseDeleted) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_DELETE_FAILED, 'Không thể xóa agent');
      }

      this.logger.debug(`Đã xóa mềm agent base với ID ${id}`);

      // Nếu có agent tương ứng, cũng xóa mềm agent
      if (agentExists) {
        // Xóa mềm agent bằng cách cập nhật trường deletedAt
        const agentDeleted = await this.agentRepository.customSoftDelete(id);
        if (!agentDeleted) {
          throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_DELETE_FAILED, 'Không thể xóa agent');
        }
      } else {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND, 'Không tìm thấy agent');
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing agent base: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_DELETE_FAILED);
    }
  }

  /**
   * Đặt agent base thành active
   * @param id ID của agent base
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async setActive(id: string): Promise<void> {
    // Kiểm tra agent base có tồn tại không
    const agentBaseExists = await this.agentBaseRepository.existsById(id);
    if (!agentBaseExists) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
    }

    // Lấy thông tin agent base để cập nhật
    const agentBase = await this.agentBaseRepository.findById(id);
    if (!agentBase) {
      // Trường hợp này không nên xảy ra vì đã kiểm tra tồn tại ở trên
      throw new AppException(AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND);
    }
    try {

      // Đặt agent base thành active
      await this.agentBaseRepository.setActive(id);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error setting agent base active: ${error.message}`,
        error.stack,
      );
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Tạo các quan hệ multi-agent cho agent base
   * @param parentAgentId ID của agent base (parent)
   * @param relations Danh sách quan hệ cần tạo
   * @returns Danh sách quan hệ đã tạo
   */
  private async createMultiAgentRelations(
    parentAgentId: string,
    relations: MultiAgentRelationDto[]
  ): Promise<MultiAgentRelationResponseDto[]> {
    try {
      const result: MultiAgentRelationResponseDto[] = [];

      // Kiểm tra và tạo từng quan hệ
      for (const relation of relations) {
        // Kiểm tra agent system có tồn tại không
        const childAgent = await this.agentSystemRepository.findById(relation.childAgentId);
        if (!childAgent) {
          this.logger.warn(`Agent system với ID ${relation.childAgentId} không tồn tại, bỏ qua quan hệ này`);
          continue;
        }

        // Lấy thông tin agent để lấy tên
        const childAgentInfo = await this.agentRepository.findById(relation.childAgentId);

        try {
          // Tạo quan hệ mới
          const createdRelation = await this.multiAgentsSystemRepository.createRelation(
            parentAgentId,
            relation.childAgentId,
            relation.prompt
          );

          // Thêm vào kết quả sử dụng mapper
          result.push(MultiAgentRelationMapper.toResponseDto(createdRelation, childAgentInfo?.name));
        } catch (error) {
          // Nếu quan hệ đã tồn tại, bỏ qua và tiếp tục với quan hệ tiếp theo
          if (error instanceof AppException &&
            (error.getResponse() as { code: number }).code === AGENT_ERROR_CODES.RELATION_ALREADY_EXISTS.code) {
            this.logger.warn(`Quan hệ giữa agent ${parentAgentId} và ${relation.childAgentId} đã tồn tại, bỏ qua`);
            continue;
          }
          throw error;
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo quan hệ multi-agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.RELATION_CREATION_FAILED);
    }
  }

  /**
   * Xác thực model có tồn tại và có trạng thái APPROVED không
   * @param modelId ID của model cần xác thực
   * @throws AppException nếu model không tồn tại hoặc không có trạng thái APPROVED
   */
  private async validateModel(
    modelId: string,
  ): Promise<void> {
    try {
      // Kiểm tra model có tồn tại không bằng cách gọi service của module Model (Admin)
      const model = await this.baseModelAdminService.getModelById(modelId);

      if (!model) {
        throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
      }

      // Kiểm tra model có trạng thái APPROVED không
      if (model.status !== 'APPROVED') {
        throw new AppException(
          AGENT_ERROR_CODES.MODEL_NOT_APPROVED,
          `Model với ID ${modelId} chưa được phê duyệt, không thể sử dụng`
        );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xác thực model: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
    }
  }
}
