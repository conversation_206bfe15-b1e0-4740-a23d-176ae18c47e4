import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_websites trong cơ sở dữ liệu
 * Lưu trữ danh sách website của người dùng, bao gồ<PERSON> tên, host và trạng thái xác minh
 */
@Entity('user_websites')
export class UserWebsite {
  /**
   * Kh<PERSON><PERSON> chín<PERSON>, tự động tăng
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID của người dùng sở hữu website
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Tên website do người dùng đặt
   */
  @Column({ name: 'website_name', length: 255 })
  websiteName: string;

  /**
   * Tên miền hoặc địa chỉ host của website
   */
  @Column({ name: 'host', length: 255 })
  host: string;

  /**
   * Trạng thái xác minh của website (TRUE nếu đã xác minh)
   */
  @Column({ name: 'verify', default: false })
  verify: boolean;

  /**
   * Thời điểm tạo bản ghi
   */
  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  /**
   * ID agent được kết nối với website
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string;
}
