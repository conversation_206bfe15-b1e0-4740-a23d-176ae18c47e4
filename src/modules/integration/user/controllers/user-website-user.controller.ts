import { Body, Controller, Get, HttpStatus, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiQuery, ApiResponse, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { UserWebsiteUserService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { CreateWebsiteDto, WebsiteQueryDto, WebsiteResponseDto } from '../dto';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers';

@ApiTags(SWAGGER_API_TAGS.USER_INTEGRATION_WEBSITE)
@Controller('integration/website')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserWebsiteUserController {
  constructor(
    private readonly userWebsiteUserService: UserWebsiteUserService,
  ) {}

  /**
   * Lấy danh sách website của người dùng
   */
  @ApiOperation({ summary: 'Lấy danh sách website của người dùng' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang hiện tại (bắt đầu từ 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng bản ghi trên mỗi trang',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Từ khóa tìm kiếm theo tên website',
  })
  @ApiQuery({
    name: 'verify',
    required: false,
    type: Boolean,
    description: 'Lọc theo trạng thái xác minh',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách website thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(WebsiteResponseDto) }
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      ]
    }
  })
  @Get()
  async findAll(
    @Query() queryDto: WebsiteQueryDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<PaginatedResult<WebsiteResponseDto>>> {
    const result = await this.userWebsiteUserService.findAll(queryDto, user.id);
    return wrapResponse(result, 'Lấy danh sách website thành công');
  }

  /**
   * Tạo mới website
   */
  @ApiOperation({ summary: 'Tạo mới website' })
  @ApiBody({
    description: 'Thông tin website cần tạo',
    type: CreateWebsiteDto
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo website thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { $ref: getSchemaPath(WebsiteResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu không hợp lệ hoặc website đã tồn tại',
  })
  @Post()
  async create(
    @Body() createWebsiteDto: CreateWebsiteDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<WebsiteResponseDto>> {
    const result = await this.userWebsiteUserService.create(createWebsiteDto, user.id);
    return wrapResponse(result, 'Tạo website thành công');
  }
}
