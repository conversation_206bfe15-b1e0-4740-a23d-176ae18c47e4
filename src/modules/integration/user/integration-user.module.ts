import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as controllers from './controllers';
import * as services from './services';
import { SepayHubModule } from '@shared/services/sepay-hub';
import { Agent } from '@modules/agent/entities';
import { GoogleAdsAccount } from '@modules/marketing/user/entities/google-ads-account.entity';
import { GoogleAdsAccountRepository } from '@modules/marketing/user/repositories/google-ads-account.repository';
import { GoogleApiModule } from '@shared/services/google';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';

/**
 * Module quản lý tích hợp cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([...Object.values(entities), Agent, GoogleAdsAccount]),
    SepayHubModule,
    GoogleApiModule,
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
    AgentRepository,
    GoogleAdsAccountRepository,
  ],
  exports: [
    ...Object.values(services),
  ],
})
export class IntegrationUserModule {}
