import { BadRequestException, Injectable, NotFoundException, Logger } from '@nestjs/common';
import { CreateEmailServerDto, TestEmailServerDto, UpdateEmailServerDto } from '../dto';
import { EmailServerConfiguration } from '@modules/integration/entities';
import { AdminEmailServerConfigurationEntity } from '@modules/integration/entities/admin_email_server_configurations.entity';
import * as nodemailer from 'nodemailer';
import { EncryptionService } from '@shared/services/encryption.service';
import { AdminEmailServerConfigurationRepository } from '../../repositories';

@Injectable()
export class EmailServerConfigurationUserService {
  private readonly logger = new Logger(EmailServerConfigurationUserService.name);

  constructor(
    private readonly adminEmailServerRepo: AdminEmailServerConfigurationRepository,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách cấu hình máy chủ email của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách cấu hình máy chủ email
   */
  async findAllByUserId(userId: number): Promise<EmailServerConfiguration[]> {
    // Lưu ý: AdminEmailServerConfigurationEntity không có trường userId
    // Ta sẽ sử dụng createdBy thay thế cho userId với mục đích tạm thời
    const emailServers = await this.adminEmailServerRepo.find({
      where: { createdBy: userId },
      order: { createdAt: 'DESC' },
    });

    // Chuyển đổi từ AdminEmailServerConfigurationEntity sang EmailServerConfiguration
    return emailServers.map(server => {
      const { password, ...serverWithoutPassword } = server;
      return {
        ...serverWithoutPassword,
        userId, // Thêm userId vào kết quả trả về
        password: '********'
      } as EmailServerConfiguration;
    });
  }

  /**
   * Lấy thông tin chi tiết của một cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của cấu hình máy chủ email
   */
  async findOne(id: number, userId: number): Promise<EmailServerConfiguration> {
    const emailServer = await this.adminEmailServerRepo.findOne({
      where: { id, createdBy: userId }, // Sử dụng createdBy thay cho userId
    });

    if (!emailServer) {
      throw new NotFoundException(`Không tìm thấy cấu hình máy chủ email với ID ${id}`);
    }

    // Không giải mã mật khẩu khi trả về chi tiết
    // Chỉ trả về thông tin cơ bản, không bao gồm mật khẩu thật
    const { password, ...serverWithoutPassword } = emailServer;
    return {
      ...serverWithoutPassword,
      userId, // Thêm userId vào kết quả
      password: '********'
    } as EmailServerConfiguration;
  }

  /**
   * Lấy thông tin chi tiết của một cấu hình máy chủ email bao gồm mật khẩu đã giải mã
   * @param id ID của cấu hình máy chủ email
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của cấu hình máy chủ email với mật khẩu đã giải mã
   * @private Phương thức này chỉ được sử dụng nội bộ
   */
  private async findOneWithDecryptedPassword(id: number, userId: number): Promise<AdminEmailServerConfigurationEntity & { userId?: number }> {
    const emailServer = await this.adminEmailServerRepo.findOne({
      where: { id, createdBy: userId }, // Sử dụng createdBy thay cho userId
    });

    if (!emailServer) {
      throw new NotFoundException(`Không tìm thấy cấu hình máy chủ email với ID ${id}`);
    }

    try {
      // Giải mã mật khẩu
      if (emailServer.password) {
        emailServer.password = this.encryptionService.decrypt(emailServer.password);
      }

      // Thêm userId vào đối tượng trả về để sử dụng trong logic tiếp theo
      const result = { ...emailServer, userId };
      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã mật khẩu cho máy chủ email ID ${id}: ${error.message}`);
      // Trả về đối tượng gốc nếu không thể giải mã
      return { ...emailServer, userId };
    }
  }

  /**
   * Tạo mới cấu hình máy chủ email
   * @param createEmailServerDto Thông tin cấu hình máy chủ email cần tạo
   * @param userId ID của người dùng
   * @returns Cấu hình máy chủ email đã được tạo
   */
  async create(createEmailServerDto: CreateEmailServerDto, userId: number): Promise<EmailServerConfiguration> {
    try {
      const now = Date.now();

      // Tạo bản sao của DTO để không thay đổi đối tượng gốc
      const dtoWithEncryptedPassword = { ...createEmailServerDto };

      // Mã hóa mật khẩu trước khi lưu vào database
      if (dtoWithEncryptedPassword.password) {
        dtoWithEncryptedPassword.password = this.encryptionService.encrypt(dtoWithEncryptedPassword.password);
      }

      // Tạo đối tượng cấu hình máy chủ email sử dụng AdminEmailServerConfigurationEntity
      const emailServer = this.adminEmailServerRepo.create({
        ...dtoWithEncryptedPassword,
        createdBy: userId, // Sử dụng createdBy thay cho userId
        createdAt: now,
        updatedAt: now,
      });

      // Lưu vào database
      const savedServer = await this.adminEmailServerRepo.save(emailServer);

      // Trả về đối tượng đã lưu nhưng che mật khẩu và thêm userId
      const { password, ...serverWithoutPassword } = savedServer;
      return {
        ...serverWithoutPassword,
        userId, // Thêm userId vào kết quả
        password: '********'
      } as EmailServerConfiguration;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình máy chủ email: ${error.message}`);
      throw new BadRequestException(`Không thể tạo cấu hình máy chủ email: ${error.message}`);
    }
  }

  /**
   * Cập nhật thông tin cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param updateEmailServerDto Thông tin cần cập nhật
   * @param userId ID của người dùng
   * @returns Cấu hình máy chủ email đã được cập nhật
   */
  async update(id: number, updateEmailServerDto: UpdateEmailServerDto, userId: number): Promise<EmailServerConfiguration> {
    try {
      // Lấy thông tin gốc từ database (không phải phiên bản đã che mật khẩu)
      const originalServer = await this.adminEmailServerRepo.findOne({
        where: { id, createdBy: userId }, // Sử dụng createdBy thay cho userId
      });

      if (!originalServer) {
        throw new NotFoundException(`Không tìm thấy cấu hình máy chủ email với ID ${id}`);
      }

      // Tạo bản sao của DTO để không thay đổi đối tượng gốc
      const dtoWithEncryptedPassword = { ...updateEmailServerDto };

      // Mã hóa mật khẩu mới nếu có
      if (dtoWithEncryptedPassword.password) {
        dtoWithEncryptedPassword.password = this.encryptionService.encrypt(dtoWithEncryptedPassword.password);
      }

      // Cập nhật thông tin
      Object.assign(originalServer, {
        ...dtoWithEncryptedPassword,
        updatedAt: Date.now(),
      });

      // Lưu vào database
      const updatedServer = await this.adminEmailServerRepo.save(originalServer);

      // Trả về đối tượng đã cập nhật nhưng che mật khẩu và thêm userId
      const { password, ...serverWithoutPassword } = updatedServer;
      return {
        ...serverWithoutPassword,
        userId, // Thêm userId vào kết quả
        password: '********'
      } as EmailServerConfiguration;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật cấu hình máy chủ email ID ${id}: ${error.message}`);
      throw new BadRequestException(`Không thể cập nhật cấu hình máy chủ email: ${error.message}`);
    }
  }

  /**
   * Xóa cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param userId ID của người dùng
   * @returns Thông báo kết quả
   */
  async remove(id: number, userId: number): Promise<{ message: string }> {
    // Lấy thông tin từ DB sử dụng createdBy thay cho userId
    const emailServer = await this.adminEmailServerRepo.findOne({
      where: { id, createdBy: userId },
    });

    if (!emailServer) {
      throw new NotFoundException(`Không tìm thấy cấu hình máy chủ email với ID ${id}`);
    }

    await this.adminEmailServerRepo.remove(emailServer);

    return { message: 'Xóa cấu hình máy chủ email thành công' };
  }

  /**
   * Kiểm tra kết nối máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param testEmailServerDto Thông tin kiểm tra
   * @param userId ID của người dùng
   * @returns Kết quả kiểm tra
   */
  async testConnection(id: number, testEmailServerDto: TestEmailServerDto, userId: number): Promise<{ success: boolean; message: string; details?: any }> {
    // Lấy thông tin máy chủ email với mật khẩu đã giải mã
    const emailServer = await this.findOneWithDecryptedPassword(id, userId);

    try {
      // Tạo transporter
      const transporter = nodemailer.createTransport({
        host: emailServer.host,
        port: emailServer.port,
        secure: emailServer.useSsl,
        auth: {
          user: emailServer.username,
          pass: emailServer.password, // Sử dụng mật khẩu đã giải mã
        },
        ...emailServer.additionalSettings,
      });

      // Kiểm tra kết nối
      await transporter.verify();

      // Gửi email kiểm tra
      const subject = testEmailServerDto.subject || 'Kiểm tra kết nối máy chủ email';
      await transporter.sendMail({
        from: emailServer.username,
        to: testEmailServerDto.recipientEmail,
        subject,
        text: `Đây là email kiểm tra kết nối từ máy chủ ${emailServer.serverName}. Nếu bạn nhận được email này, kết nối đã thành công.`,
        html: `<p>Đây là email kiểm tra kết nối từ máy chủ <strong>${emailServer.serverName}</strong>.</p><p>Nếu bạn nhận được email này, kết nối đã thành công.</p>`,
      });

      return {
        success: true,
        message: 'Kết nối thành công! Email kiểm tra đã được gửi.',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Kết nối thất bại!',
        details: error.message || 'Không thể kết nối đến máy chủ email.',
      };
    }
  }
}
