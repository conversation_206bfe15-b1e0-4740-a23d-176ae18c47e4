import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { ProductAdminService } from '../services/product-admin.service';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { SwaggerApiTag } from '@common/swagger/swagger.tags';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryProductDto,
  ProductDetailResponseDto,
  UpdateProductStatusDto,
  UpdateProductDto,
  CreateProductAdminDto,
  CreateProductResponseDto,
  UpdateProductResponseDto,
  PaginatedProductResponseDto,
  UpdateMultipleProductsStatusDto,
  UpdateMultipleProductsStatusResponseDto,
  AddImageOperationDto,
  DeleteImageOperationDto
} from '../dto';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { PresignedUrlImageDto } from '../dto/presigned-url.dto';

/**
 * Controller xử lý các API liên quan đến sản phẩm cho admin
 */
@ApiTags(SwaggerApiTag.ADMIN_MARKETPLACE_PRODUCTS)
@ApiExtraModels(
  ApiResponseDto,
  ProductDetailResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
  CreateProductResponseDto,
  UpdateProductResponseDto,
  PaginatedProductResponseDto,
  PresignedUrlImageDto,
  UpdateMultipleProductsStatusResponseDto,
  AddImageOperationDto,
  DeleteImageOperationDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/marketplace/products')
export class ProductAdminController {
  constructor(private readonly productAdminService: ProductAdminService) {}

  /**
   * Lấy danh sách tất cả sản phẩm với phân trang, tìm kiếm, lọc và sắp xếp
   * Hỗ trợ các tham số query:
   * - page: Số trang (bắt đầu từ 1)
   * - limit: Số lượng sản phẩm trên một trang
   * - search: Từ khóa tìm kiếm (tìm theo tên sản phẩm)
   * - status: Lọc theo trạng thái sản phẩm
   * - category: Lọc theo loại sản phẩm
   * - userId: Lọc theo ID người dùng
   * - employeeId: Lọc theo ID nhân viên
   * - includeDeleted: Bao gồm cả sản phẩm đã bị xóa (mặc định: false)
   * - sortBy: Sắp xếp theo trường (name, createdAt, price, ...)
   * - sortOrder: Thứ tự sắp xếp (ASC, DESC)
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách tất cả sản phẩm',
    description: 'Lấy danh sách tất cả sản phẩm (bao gồm cả của admin và user). Mặc định loại trừ những sản phẩm có status = DELETED, trừ khi includeDeleted=true'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách sản phẩm phân trang',
    schema: ApiResponseDto.getSchema(PaginatedProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR,
    MARKETPLACE_ERROR_CODES.GENERAL_ERROR
  )
  async getAllProducts(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryProductDto
  ): Promise<ApiResponseDto<PaginatedProductResponseDto>> {
    // Admin không cần currentUserId để tính canPurchase, truyền undefined
    const result = await this.productAdminService.getProducts(employeeId, queryDto, undefined);

    // Chuyển đổi kết quả thành DTO phân trang
    const paginatedResponse: PaginatedProductResponseDto = {
      items: result.items as unknown as ProductDetailResponseDto[],
      meta: result.meta
    };
    return ApiResponseDto.success(paginatedResponse);
  }

  /**
   * Lấy thông tin chi tiết sản phẩm theo ID
   * Trả về thông tin chi tiết của sản phẩm bao gồm tên, mô tả, giá, hình ảnh, trạng thái, v.v.
   * Admin có thể xem thông tin chi tiết của tất cả sản phẩm, bao gồm cả sản phẩm của người dùng
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần xem thông tin
   * @returns Thông tin chi tiết sản phẩm
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết sản phẩm theo ID' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết sản phẩm',
    schema: ApiResponseDto.getSchema(ProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_FETCH_ERROR
  )
  async getProductById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number
  ): Promise<ApiResponseDto<ProductDetailResponseDto>> {
    // Admin không cần currentUserId để tính canPurchase, truyền undefined
    const result = await this.productAdminService.getProductById(employeeId, productId, undefined);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo sản phẩm mới
   * Tạo sản phẩm mới với trạng thái mặc định là DRAFT
   * Trả về thông tin sản phẩm đã tạo và các URL ký sẵn để upload tài liệu (hình ảnh, hướng dẫn sử dụng, chi tiết)
   * Sau khi tạo sản phẩm, cần upload các tài liệu lên các URL ký sẵn để hoàn tất quá trình tạo sản phẩm
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param createProductDto DTO chứa thông tin sản phẩm mới
   * @returns Thông tin sản phẩm đã tạo và các URL ký sẵn để upload tài liệu
   */
  @Post()
  @ApiOperation({ summary: 'Tạo sản phẩm mới' })
  @ApiBody({ type: CreateProductAdminDto })
  @ApiResponse({
    status: 201,
    description: 'Sản phẩm đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CreateProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_CREATION_FAILED,
    MARKETPLACE_ERROR_CODES.MISSING_REQUIRED_FIELDS,
    MARKETPLACE_ERROR_CODES.FILE_UPLOAD_FAILED,
    MARKETPLACE_ERROR_CODES.INVALID_PRICE
  )
  async createProduct(
    @CurrentEmployee('id') employeeId: number,
    @Body() createProductDto: CreateProductAdminDto
  ): Promise<ApiResponseDto<CreateProductResponseDto>> {
    const result = await this.productAdminService.createProduct(employeeId, createProductDto);
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật sản phẩm
   * Cho phép cập nhật thông tin sản phẩm như tên, mô tả, giá, hình ảnh, v.v.
   * Nếu cập nhật hình ảnh, sẽ trả về các URL ký sẵn để upload hình ảnh mới
   * Có thể yêu cầu đăng bán sản phẩm ngay sau khi cập nhật bằng cách đặt publishAfterUpdate = true
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật sản phẩm
   * @returns Sản phẩm đã cập nhật và các URL ký sẵn để upload tài liệu
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật sản phẩm' })
  @ApiParam({
    name: 'id',
    description: 'ID của sản phẩm',
    type: Number,
    example: 1,
  })
  @ApiBody({ type: UpdateProductDto })
  @ApiResponse({
    status: 200,
    description: 'Sản phẩm đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UpdateProductResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_UPDATE_FAILED,
    MARKETPLACE_ERROR_CODES.MISSING_REQUIRED_FIELDS,
    MARKETPLACE_ERROR_CODES.FILE_UPLOAD_FAILED
  )
  async updateProduct(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number,
    @Body() updateProductDto: UpdateProductDto
  ): Promise<ApiResponseDto<UpdateProductResponseDto>> {
    const serviceResult = await this.productAdminService.updateProduct(employeeId, productId, updateProductDto);

    // Chuyển đổi kết quả từ service thành DTO
    const responseDto: UpdateProductResponseDto = {
      product: serviceResult.product,
      presignedUrlImage: serviceResult.presignedUrlImage,
      presignedUrlDetail: serviceResult.presignedUrlDetail,
      presignedUrlUserManual: serviceResult.presignedUrlUserManual,
      publishError: serviceResult.publishError
    };

    // Kiểm tra nếu yêu cầu đăng bán và không có lỗi đăng bán
    if (updateProductDto.publishAfterUpdate && responseDto.product.status === ProductStatus.APPROVED && !responseDto.publishError) {
      return ApiResponseDto.success(responseDto, 'Sản phẩm đã được cập nhật và đăng bán thành công');
    }

    // Nếu có lỗi đăng bán
    if (updateProductDto.publishAfterUpdate && responseDto.publishError) {
      return ApiResponseDto.success(responseDto, 'Sản phẩm đã được cập nhật nhưng không thể đăng bán');
    }

    // Trường hợp chỉ cập nhật thông thường
    return ApiResponseDto.success(responseDto, 'Sản phẩm đã được cập nhật thành công');
  }

  /**
   * Đăng bán sản phẩm
   * Chuyển trạng thái sản phẩm từ APPROVED sang PUBLISHED để đăng bán
   * Sản phẩm chỉ có thể được đăng bán khi đã được phê duyệt (APPROVED)
   * Sản phẩm đã đăng bán sẽ hiển thị cho người dùng và có thể được mua
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần đăng bán
   * @returns Thông tin chi tiết sản phẩm sau khi đăng bán
   */
  @Post(':id/publish')
  @ApiOperation({ summary: 'Đăng bán sản phẩm' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Sản phẩm đã được đăng bán thành công',
    schema: ApiResponseDto.getSchema(ProductDetailResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.INVALID_STATUS,
    MARKETPLACE_ERROR_CODES.PRODUCT_UPDATE_FAILED
  )
  async publishProduct(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number
  ): Promise<ApiResponseDto<ProductDetailResponseDto>> {
    const result = await this.productAdminService.publishProduct(employeeId, productId);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa sản phẩm
   * Thực hiện xóa mềm sản phẩm (soft delete) bằng cách cập nhật trạng thái thành DELETED
   * Sản phẩm đã xóa sẽ không xuất hiện trong danh sách sản phẩm
   * Chỉ admin mới có quyền xóa sản phẩm
   * Cập nhật is_for_salre = false cho tài nguyên gốc
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm cần xóa
   * @returns Không có dữ liệu trả về (null)
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa sản phẩm' })
  @ApiParam({ name: 'id', description: 'ID của sản phẩm', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Sản phẩm đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.PRODUCT_DELETED
  )
  async deleteProduct(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number
  ): Promise<ApiResponseDto<null>> {
    await this.productAdminService.deleteProduct(employeeId, productId);
    return ApiResponseDto.success(null, 'Sản phẩm đã được xóa thành công');
  }


  /**
   * Cập nhật trạng thái nhiều sản phẩm
   * Cho phép admin thay đổi trạng thái nhiều sản phẩm cùng lúc
   * Mỗi trạng thái có các điều kiện và quy tắc riêng
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateMultipleStatusDto DTO chứa thông tin trạng thái mới và danh sách ID sản phẩm
   * @returns Danh sách ID sản phẩm đã cập nhật thành công và danh sách ID sản phẩm thất bại
   */
  @Post('batch-status-update')
  @ApiOperation({ summary: 'Cập nhật trạng thái nhiều sản phẩm' })
  @ApiBody({ type: UpdateMultipleProductsStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái các sản phẩm đã được cập nhật',
    schema: ApiResponseDto.getSchema(UpdateMultipleProductsStatusResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.INVALID_STATUS,
    MARKETPLACE_ERROR_CODES.PRODUCT_STATUS_CHANGE_FAILED,
    MARKETPLACE_ERROR_CODES.MULTIPLE_PRODUCTS_UPDATE_FAILED
  )
  async updateMultipleProductsStatus(
    @CurrentEmployee('id') employeeId: number,
    @Body() updateMultipleStatusDto: UpdateMultipleProductsStatusDto
  ): Promise<ApiResponseDto<UpdateMultipleProductsStatusResponseDto>> {
    const result = await this.productAdminService.updateMultipleProductsStatus(employeeId, updateMultipleStatusDto);
    return ApiResponseDto.success(result);
  }

}