import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module Marketplace (12000-12099)
 */
export const MARKETPLACE_ERROR_CODES = {
  // Lỗi chung
  GENERAL_ERROR: new ErrorCode(
    12000,
    'Lỗi xử lý Marketplace',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi sản phẩm
  PRODUCT_NOT_FOUND: new ErrorCode(
    12001,
    'Sản phẩm không tồn tại',
    HttpStatus.NOT_FOUND,
  ),
  UNAUTHORIZED: new ErrorCode(
    12002,
    'Không có quyền truy cập sản phẩm này',
    HttpStatus.FORBIDDEN,
  ),
  INVALID_STATUS: new ErrorCode(
    12003,
    'Trạng thái sản phẩm không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  MISSING_REQUIRED_FIELDS: new ErrorCode(
    12004,
    '<PERSON><PERSON><PERSON><PERSON> các trường bắt buộc',
    HttpStatus.BAD_REQUEST,
  ),
  PRODUCT_NOT_APPROVED: new ErrorCode(
    12005,
    'Sản phẩm chưa được phê duyệt',
    HttpStatus.BAD_REQUEST,
  ),
  CANNOT_BUY_OWN_PRODUCT: new ErrorCode(
    12006,
    'Không thể mua sản phẩm của chính mình',
    HttpStatus.BAD_REQUEST,
  ),
  PRODUCT_FETCH_ERROR: new ErrorCode(
    12007,
    'Lỗi khi lấy thông tin sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_RETRIEVAL_FAILED: new ErrorCode(
    12008,
    'Không thể truy xuất thông tin sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_FETCH_FAILED: new ErrorCode(
    12009,
    'Không thể lấy thông tin sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi giỏ hàng
  CART_NOT_FOUND: new ErrorCode(
    12010,
    'Giỏ hàng không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  CART_ITEM_NOT_FOUND: new ErrorCode(
    12011,
    'Sản phẩm không có trong giỏ hàng',
    HttpStatus.NOT_FOUND,
  ),

  CART_UPDATE_FAILED: new ErrorCode(
    12012,
    'Cập nhật giỏ hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CART_RETRIEVAL_FAILED: new ErrorCode(
    12013,
    'Lấy thông tin giỏ hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  CANNOT_ADD_OWN_PRODUCT: new ErrorCode(
    12014,
    'Không thể thêm sản phẩm của chính mình vào giỏ hàng',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi đơn hàng
  ORDER_NOT_FOUND: new ErrorCode(
    12020,
    'Đơn hàng không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  INSUFFICIENT_POINTS: new ErrorCode(
    12021,
    'Không đủ điểm để thanh toán',
    HttpStatus.BAD_REQUEST,
  ),

  EMPTY_CART: new ErrorCode(12022, 'Giỏ hàng trống', HttpStatus.BAD_REQUEST),

  ORDER_CREATION_FAILED: new ErrorCode(
    12023,
    'Tạo đơn hàng thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi tạo và cập nhật sản phẩm
  PRODUCT_CREATION_FAILED: new ErrorCode(
    12030,
    'Tạo sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_UPDATE_FAILED: new ErrorCode(
    12031,
    'Cập nhật sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_DELETE_FAILED: new ErrorCode(
    12032,
    'Xóa sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_STATUS_CHANGE_FAILED: new ErrorCode(
    12033,
    'Thay đổi trạng thái sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_UPDATE_NOT_ALLOWED: new ErrorCode(
    12034,
    'Không được phép cập nhật sản phẩm',
    HttpStatus.BAD_REQUEST,
  ),
  PRODUCT_DELETE_NOT_ALLOWED: new ErrorCode(
    12035,
    'Không được phép xóa sản phẩm',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_PRODUCT_STATUS: new ErrorCode(
    12036,
    'Trạng thái sản phẩm không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_PRODUCT_CATEGORY: new ErrorCode(
    12037,
    'Danh mục sản phẩm không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  PRODUCT_NOT_OWNED: new ErrorCode(
    12038,
    'Sản phẩm không thuộc sở hữu của bạn',
    HttpStatus.FORBIDDEN,
  ),
  PRODUCT_IMAGE_NOT_FOUND: new ErrorCode(
    12039,
    'Không tìm thấy ảnh sản phẩm',
    HttpStatus.NOT_FOUND,
  ),
  PRODUCT_DELETED: new ErrorCode(
    12040,
    'Sản phẩm đã bị xóa, không còn tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  // Lỗi upload file
  FILE_UPLOAD_FAILED: new ErrorCode(
    12041,
    'Upload file thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi lịch sử mua hàng
  PURCHASE_HISTORY_ERROR: new ErrorCode(
    12042,
    'Lỗi khi lấy lịch sử mua hàng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  PRESIGNED_URL_CREATION_FAILED: new ErrorCode(
    12043,
    'Tạo presigned URL thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi kiểm tra giá
  INVALID_PRICE: new ErrorCode(
    12043,
    'Giá không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi chuyển trạng thái
  INVALID_STATUS_TRANSITION: new ErrorCode(
    12044,
    'Chuyển trạng thái không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi giới hạn sản phẩm trong giỏ hàng
  CART_PRODUCT_LIMIT_EXCEEDED: new ErrorCode(
    12045,
    'Giỏ hàng đã đạt giới hạn tối đa 20 sản phẩm khác nhau',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi cập nhật nhiều sản phẩm
  MULTIPLE_PRODUCTS_UPDATE_FAILED: new ErrorCode(
    12046,
    'Cập nhật nhiều sản phẩm thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi sản phẩm đã mua
  PRODUCT_ALREADY_PURCHASED: new ErrorCode(
    12047,
    'Sản phẩm đã được mua trước đó',
    HttpStatus.BAD_REQUEST,
  ),
};
