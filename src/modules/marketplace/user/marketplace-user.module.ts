import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ProductUserController, CartUserController, OrderUserController, PaymentController  } from './controllers';
import {
  ProductUserService,
  CartUserService,
  OrderUserService,
  PaymentService
} from './services';
import { Product, Cart, CartItem, MarketOrder, MarketOrderLine } from '../entities';
import { ProductRepository, CartRepository, CartItemRepository, MarketOrderRepository, MarketOrderLineRepository } from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { RedisService } from '@shared/services/redis.service';
import { CdnService } from '@shared/services/cdn.service';
import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { User } from '@modules/user/entities';
import { ProductHelper, ValidationHelper, CartHelper, PurchaseHistoryHelper } from '../helpers';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product, Cart, CartItem, MarketOrder, MarketOrderLine, User]),
    ScheduleModule.forRoot(),
    SystemConfigurationModule,
  ],
  controllers: [ProductUserController, CartUserController, OrderUserController, PaymentController],
  providers: [
    ProductUserService,
    CartUserService,
    OrderUserService,
    PaymentService,
    ProductRepository,
    CartRepository,
    CartItemRepository,
    MarketOrderRepository,
    MarketOrderLineRepository,
    UserRepository,
    S3Service,
    RedisService,
    CdnService,
    ProductHelper,
    ValidationHelper,
    CartHelper,
    PurchaseHistoryHelper
  ],
  exports: [ProductUserService, CartUserService, OrderUserService, PaymentService],
})
export class MarketplaceUserModule {}
