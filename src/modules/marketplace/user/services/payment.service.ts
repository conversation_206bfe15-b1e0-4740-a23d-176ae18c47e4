import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { ProductRepository, MarketOrderRepository, CartItemRepository } from '@modules/marketplace/repositories';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { SystemConfigurationRepository } from '@modules/system-configuration/repositories/system-configuration.repository';
import { PaymentDto, PaymentResponseDto } from '../dto';
import { Product } from '@modules/marketplace/entities';
import { ProductStatus } from '@modules/marketplace/enums';

/**
 * Service xử lý các thao tác liên quan đến thanh toán sản phẩm
 */
@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  // <PERSON><PERSON> sàn mặc định (5%) - chỉ dùng khi không lấy được từ cấu hình
  private readonly DEFAULT_PLATFORM_FEE_PERCENT = 5.0;

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly userRepository: UserRepository,
    private readonly marketOrderRepository: MarketOrderRepository,
    private readonly cartItemRepository: CartItemRepository,
    private readonly systemConfigurationRepository: SystemConfigurationRepository,
  ) {}

  /**
   * Xử lý thanh toán sản phẩm
   * @param userId ID của người dùng
   * @param paymentDto DTO chứa thông tin thanh toán
   * @returns Thông tin thanh toán thành công
   */
  @Transactional()
  async processPayment(userId: number, paymentDto: PaymentDto): Promise<PaymentResponseDto> {
    try {
      this.logger.log(`Xử lý thanh toán cho user ${userId} với ${paymentDto.productIds.length} sản phẩm`);

      // 1. Kiểm tra và lấy thông tin sản phẩm
      const products = await this.validateAndGetProducts(userId, paymentDto.productIds);

      // 2. Tính tổng số điểm cần thanh toán
      const totalPoint = this.calculateTotalPoints(products);

      // 3. Kiểm tra số dư của người dùng
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(MARKETPLACE_ERROR_CODES.GENERAL_ERROR, 'Không tìm thấy thông tin người dùng');
      }

      // Log thông tin số dư và tổng điểm cần thanh toán
      this.logger.debug(`User balance: ${user.pointsBalance}, Total points needed: ${totalPoint}`);

      // Chuyển đổi sang number để đảm bảo so sánh chính xác
      const userBalance = Number(user.pointsBalance);

      // Kiểm tra số dư phải đủ để thanh toán
      if (userBalance < totalPoint) {
        // Định dạng số để hiển thị đúng
        const formattedTotal = totalPoint.toLocaleString('vi-VN');
        const formattedBalance = userBalance.toLocaleString('vi-VN');

        throw new AppException(
          MARKETPLACE_ERROR_CODES.INSUFFICIENT_POINTS,
          `Số dư không đủ. Cần ${formattedTotal} R-Point, hiện có ${formattedBalance} R-Point`
        );
      }

      // Lấy phí sàn từ cấu hình hệ thống
      const platformFeePercent = await this.getPlatformFeePercentage();
      this.logger.log(`Áp dụng phí sàn: ${platformFeePercent}%`);

      // 4. Tạo đơn hàng và chi tiết đơn hàng
      const order = await this.marketOrderRepository.createOrderWithLines(userId, totalPoint, products, platformFeePercent);

      // 5. Xử lý thanh toán cho người bán
      for (const product of products) {
        // Tính toán phí sàn và số tiền người bán nhận được
        const productPrice = Number(product.discountedPrice);
        const platformFee = Math.floor(productPrice * (platformFeePercent / 100));
        const sellerReceivePrice = productPrice - platformFee;

        // Xử lý thanh toán cho người bán
        await this.processSellerPayment(product, sellerReceivePrice);
      }

      // 6. Trừ điểm của người mua
      let updatedBalance: number;
      try {
        // Sử dụng repository để cập nhật số dư người dùng
        const updatedUser = await this.userRepository.updateUserBalance(userId, -totalPoint);
        updatedBalance = Number(updatedUser.pointsBalance);
        this.logger.debug(`User balance updated: ${updatedBalance} (deducted: ${totalPoint})`);
      } catch (error) {
        this.logger.error(`Lỗi khi trừ điểm người dùng: ${error.message}`, error.stack);
        throw new AppException(
          MARKETPLACE_ERROR_CODES.INSUFFICIENT_POINTS,
          error.message
        );
      }

      // 7. Xóa sản phẩm đã mua khỏi giỏ hàng
      await this.removeProductsFromCart(userId, paymentDto.productIds);

      // 8. Tạo response với số dư đã cập nhật
      return {
        orderId: order.id,
        totalPoint: Number(totalPoint),
        remainingBalance: updatedBalance,
        createdAt: order.createdAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi xử lý thanh toán: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        `Lỗi xử lý thanh toán: ${error.message}`
      );
    }
  }

  /**
   * Kiểm tra và lấy thông tin sản phẩm
   * @param userId ID của người dùng
   * @param productIds Danh sách ID sản phẩm
   * @returns Danh sách sản phẩm đã kiểm tra
   */
  private async validateAndGetProducts(userId: number, productIds: number[]): Promise<Product[]> {
    this.logger.debug(`Kiểm tra và lấy thông tin sản phẩm cho user ${userId}: ${productIds.join(', ')}`);

    // 1. Lấy thông tin sản phẩm từ repository
    const products = await this.productRepository.findProductsByIds(productIds);

    // Kiểm tra số lượng sản phẩm
    if (products.length !== productIds.length) {
      const foundIds = products.map(p => p.id);
      const missingIds = productIds.filter(id => !foundIds.includes(id));
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Không tìm thấy sản phẩm với ID: ${missingIds.join(', ')}`
      );
    }

    // Kiểm tra trạng thái sản phẩm
    for (const product of products) {
      // Kiểm tra sản phẩm đã được phê duyệt
      if (product.status !== ProductStatus.APPROVED) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_NOT_APPROVED,
          `Sản phẩm "${product.name}" chưa được phê duyệt hoặc đã bị xóa`
        );
      }

      // Kiểm tra sản phẩm không phải của chính người mua
      if (product.userId === userId) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.CANNOT_BUY_OWN_PRODUCT,
          `Không thể mua sản phẩm "${product.name}" của chính mình`
        );
      }
    }

    // 2. Kiểm tra sản phẩm đã mua chưa từ repository
    const purchasedProducts = await this.productRepository.checkProductsPurchased(userId, productIds);

    if (purchasedProducts.length > 0) {
      const purchasedNames = purchasedProducts.map(p => p.productName);

      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_ALREADY_PURCHASED,
        `Sản phẩm "${purchasedNames.join(', ')}" đã được mua trước đó`
      );
    }

    return products;
  }

  /**
   * Tính tổng số điểm cần thanh toán
   * @param products Danh sách sản phẩm
   * @returns Tổng số điểm
   */
  private calculateTotalPoints(products: Product[]): number {
    // Log giá trị discountedPrice của từng sản phẩm để debug
    products.forEach(product => {
      this.logger.debug(`Product ${product.id} - ${product.name}: discountedPrice = ${product.discountedPrice}, type: ${typeof product.discountedPrice}`);
    });

    // Tính tổng bằng cách chuyển đổi rõ ràng sang number
    const total = products.reduce((sum, product) => {
      // Đảm bảo discountedPrice là số
      const price = Number(product.discountedPrice);
      return sum + price;
    }, 0);

    this.logger.debug(`Total points calculated: ${total}, type: ${typeof total}`);
    return total;
  }



  /**
   * Xử lý thanh toán cho người bán
   * @param product Sản phẩm
   * @param sellerReceivePrice Số tiền người bán nhận được
   */
  private async processSellerPayment(product: Product, sellerReceivePrice: number): Promise<void> {
    // Nếu sản phẩm của employee thì không cần cộng điểm
    if (product.employeeId) {
      this.logger.log(`Sản phẩm ${product.id} thuộc về employee ${product.employeeId}, không cần cộng điểm`);
      return;
    }

    // Nếu sản phẩm của user thì cộng điểm cho user đó
    if (product.userId) {
      try {
        // Sử dụng repository để tăng số dư người bán
        await this.userRepository.increaseSellerBalance(product.userId, sellerReceivePrice);
        this.logger.log(`Đã cộng ${sellerReceivePrice} R-Point cho người bán ${product.userId}`);
      } catch (error) {
        this.logger.error(`Lỗi khi cộng điểm cho người bán ${product.userId}: ${error.message}`, error.stack);
        throw new AppException(
          MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
          `Lỗi khi cộng điểm cho người bán: ${error.message}`
        );
      }
    }
  }



  /**
   * Xóa sản phẩm đã mua khỏi giỏ hàng
   * @param userId ID của người dùng
   * @param productIds Danh sách ID sản phẩm đã mua
   */
  private async removeProductsFromCart(userId: number, productIds: number[]): Promise<void> {
    try {
      // Sử dụng repository để xóa sản phẩm khỏi giỏ hàng
      const removedCount = await this.cartItemRepository.removeProductsFromCart(userId, productIds);
      this.logger.log(`Đã xóa ${removedCount} sản phẩm khỏi giỏ hàng của người dùng ${userId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa sản phẩm khỏi giỏ hàng: ${error.message}`, error.stack);
      // Không ném lỗi vì đây không phải là lỗi nghiêm trọng
    }
  }

  /**
   * Lấy phần trăm phí sàn từ cấu hình hệ thống
   * @returns Phần trăm phí sàn
   */
  private async getPlatformFeePercentage(): Promise<number> {
    try {
      // Lấy cấu hình đang active từ repository
      const activeConfig = await this.systemConfigurationRepository.findActive();

      // Nếu có cấu hình và có giá trị feePercentage
      if (activeConfig && activeConfig.feePercentage !== null && activeConfig.feePercentage !== undefined) {
        this.logger.debug(`Lấy phí sàn từ cấu hình hệ thống: ${activeConfig.feePercentage}%`);
        return activeConfig.feePercentage;
      }

      // Nếu không có cấu hình hoặc không có giá trị feePercentage, sử dụng giá trị mặc định
      this.logger.warn(`Không tìm thấy cấu hình phí sàn, sử dụng giá trị mặc định: ${this.DEFAULT_PLATFORM_FEE_PERCENT}%`);
      return this.DEFAULT_PLATFORM_FEE_PERCENT;
    } catch (error) {
      // Nếu có lỗi, ghi log và sử dụng giá trị mặc định
      this.logger.error(`Lỗi khi lấy phí sàn từ cấu hình: ${error.message}`, error.stack);
      return this.DEFAULT_PLATFORM_FEE_PERCENT;
    }
  }
}