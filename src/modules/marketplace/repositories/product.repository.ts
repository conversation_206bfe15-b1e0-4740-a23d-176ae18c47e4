import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Product } from '../entities/product.entity';
import { ProductStatus } from '../enums';
import { QueryProductDto, QueryUserProductDto } from '../user/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { User } from '@modules/user/entities';
import { Employee } from '@modules/employee/entities';
import { QueryProductDto as AdminQueryProductDto } from '../admin/dto';

/**
 * <PERSON>ho lưu trữ tùy chỉnh cho sản phẩm
 */
@Injectable()
export class ProductRepository extends Repository<Product> {
  private readonly logger = new Logger(ProductRepository.name);

  constructor(private dataSource: DataSource) {
    super(Product, dataSource.createEntityManager());
  }

  /**
   * Tạo truy vấn cơ bản cho sản phẩm
   * @returns QueryBuilder cho sản phẩm
   */
  private createBaseQuery(): SelectQueryBuilder<Product> {
    return this.createQueryBuilder('product');
  }

  /**
   * Tạo truy vấn cơ bản cho sản phẩm với thông tin soldCount và canPurchase
   * @param currentUserId ID người dùng hiện tại (để tính canPurchase)
   * @returns QueryBuilder cho sản phẩm với soldCount và canPurchase
   */
  private createBaseQueryWithSalesInfo(currentUserId?: number): SelectQueryBuilder<Product> {
    const query = this.createQueryBuilder('product');

    // LEFT JOIN với market_order_line để tính soldCount
    query
      .leftJoin('market_order_line', 'mol', 'mol.product_id = product.id')
      .addSelect('COALESCE(SUM(mol.quantity), 0)', 'soldCount');

    // Nếu có currentUserId, tính canPurchase
    if (currentUserId !== undefined) {
      // Logic canPurchase: Kiểm tra xem user hiện tại có thể mua sản phẩm này không
      query.addSelect(`
        CASE
          WHEN product.user_id = :currentUserId THEN false
          WHEN EXISTS (
            SELECT 1 FROM market_order mo
            JOIN market_order_line mol ON mol.order_id = mo.id
            WHERE mol.product_id = product.id
            AND mo.user_id = :currentUserId
          ) THEN false
          ELSE true
        END
      `, 'canPurchase');
      query.setParameter('currentUserId', currentUserId);
    } else {
      // Nếu không có currentUserId, mặc định canPurchase = true
      query.addSelect('true', 'canPurchase');
    }

    // GROUP BY sẽ được thêm sau khi JOIN với các bảng khác
    query.groupBy('product.id');

    return query;
  }

  /**
   * Phương thức tiện ích để gắn raw data vào entity
   * @param entities Danh sách entity
   * @param raw Danh sách raw data
   */
  private attachRawDataToEntities(entities: Product[], raw: any[]): void {
    if (raw.length > 0 && entities.length > 0) {
      for (let i = 0; i < entities.length; i++) {
        const entity = entities[i] as any;
        const rawData = raw[i];

        // Copy raw data properties vào entity
        entity.users_id = rawData.users_id;
        entity.users_fullname = rawData.users_fullname;
        entity.users_email = rawData.users_email;
        entity.users_avatar = rawData.users_avatar;
        entity.users_phonenumber = rawData.users_phonenumber;
        entity.employees_id = rawData.employees_id;
        entity.employees_fullname = rawData.employees_fullname;
        entity.employees_email = rawData.employees_email;
        entity.employees_avatar = rawData.employees_avatar;
        entity.employees_phonenumber = rawData.employees_phonenumber;

        // Gắn soldCount và canPurchase từ raw data
        entity.soldCount = parseInt(rawData.soldCount) || 0;
        entity.canPurchase = rawData.canPurchase === 'true' || rawData.canPurchase === true;

        // Đảm bảo tất cả các trường numeric là number
        if (typeof entity.id === 'string') {
          entity.id = parseInt(entity.id);
        }
        if (typeof entity.listedPrice === 'string') {
          entity.listedPrice = parseInt(entity.listedPrice);
        }
        if (typeof entity.discountedPrice === 'string') {
          entity.discountedPrice = parseInt(entity.discountedPrice);
        }
        if (typeof entity.createdAt === 'string') {
          entity.createdAt = parseInt(entity.createdAt);
        }
        if (typeof entity.updatedAt === 'string') {
          entity.updatedAt = parseInt(entity.updatedAt);
        }
      }
    }
  }

  /**
   * Tìm sản phẩm theo ID
   * @param id ID sản phẩm
   * @param currentUserId ID người dùng hiện tại (tùy chọn)
   * @returns Sản phẩm hoặc null
   */
  async findById(id: number, currentUserId?: number): Promise<Product | null> {
    const query = this.createBaseQueryWithSalesInfo(currentUserId)
      .where('product.id = :id', { id })
      .andWhere('product.status != :deletedStatus', {
        deletedStatus: ProductStatus.DELETED,
      });

    // Thêm join với bảng users
    query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
      'users.id as users_id',
      'users.fullName as users_fullName',
      'users.email as users_email',
      'users.avatar as users_avatar',
      'users.phoneNumber as users_phoneNumber',
    ]);

    // Thêm join với bảng employees
    query
      .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
      .addSelect([
        'employees.id as employees_id',
        'employees.fullName as employees_fullName',
        'employees.email as employees_email',
        'employees.avatar as employees_avatar',
        'employees.phoneNumber as employees_phoneNumber',
      ]);

    // Cập nhật GROUP BY để bao gồm các cột từ JOIN
    query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

    // Sử dụng getRawAndEntities để lấy cả raw data
    const result = await query.getRawAndEntities();

    if (result.entities.length === 0) {
      return null;
    }

    // Gắn raw data vào entity
    this.attachRawDataToEntities(result.entities, result.raw);

    return result.entities[0];
  }

  /**
   * Tìm sản phẩm theo ID cho admin (bao gồm cả sản phẩm đã xóa)
   * @param id ID sản phẩm
   * @param currentUserId ID người dùng hiện tại (tùy chọn)
   * @returns Sản phẩm hoặc null
   */
  async findByIdForAdmin(id: number, currentUserId?: number): Promise<Product | null> {
    try {
      // Sử dụng query mới với soldCount và canPurchase
      const query = this.createBaseQueryWithSalesInfo(currentUserId)
        .where('product.id = :id', { id });

      // Thêm join với bảng users
      query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
        'users.id as users_id',
        'users.fullName as users_fullName',
        'users.email as users_email',
        'users.avatar as users_avatar',
        'users.phoneNumber as users_phoneNumber',
      ]);

      // Thêm join với bảng employees
      query
        .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
        .addSelect([
          'employees.id as employees_id',
          'employees.fullName as employees_fullName',
          'employees.email as employees_email',
          'employees.avatar as employees_avatar',
          'employees.phoneNumber as employees_phoneNumber',
        ]);

      // Cập nhật GROUP BY để bao gồm các cột từ JOIN
      query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

      // Sử dụng getRawAndEntities để lấy cả raw data
      const result = await query.getRawAndEntities();

      if (result.entities.length === 0) {
        return null;
      }

      // Gắn raw data vào entity
      this.attachRawDataToEntities(result.entities, result.raw);

      return result.entities[0];
    } catch (error) {
      this.logger.error(`Failed to find product by ID for admin: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm sản phẩm theo ID với thông tin người bán
   * @param id ID sản phẩm
   * @param currentUserId ID người dùng hiện tại (tùy chọn)
   * @returns Sản phẩm với thông tin người bán hoặc null
   */
  async findByIdWithSeller(id: number, currentUserId?: number): Promise<Product | null> {
    const query = this.createBaseQueryWithSalesInfo(currentUserId)
      .where('product.id = :id', { id })
      .andWhere('product.status != :deletedStatus', {
        deletedStatus: ProductStatus.DELETED,
      });

    // Thêm join với bảng users
    query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
      'users.id as users_id',
      'users.fullName as users_fullName',
      'users.email as users_email',
      'users.avatar as users_avatar',
      'users.phoneNumber as users_phoneNumber',
    ]);

    // Thêm join với bảng employees
    query
      .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
      .addSelect([
        'employees.id as employees_id',
        'employees.fullName as employees_fullName',
        'employees.email as employees_email',
        'employees.avatar as employees_avatar',
        'employees.phoneNumber as employees_phoneNumber',
      ]);

    // Cập nhật GROUP BY để bao gồm các cột từ JOIN
    query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

    // Sử dụng getRawAndEntities để lấy cả raw data
    const result = await query.getRawAndEntities();

    if (result.entities.length === 0) {
      return null;
    }

    // Gắn raw data vào entity
    this.attachRawDataToEntities(result.entities, result.raw);

    return result.entities[0];
  }

  /**
   * Tìm sản phẩm theo ID với đầy đủ thông tin chi tiết
   * @param id ID sản phẩm
   * @param currentUserId ID người dùng hiện tại (tùy chọn)
   * @returns Sản phẩm với đầy đủ thông tin chi tiết hoặc null
   */
  async findProductDetailById(id: number, currentUserId?: number): Promise<Product | null> {
    try {
      this.logger.debug(`Đang tìm thông tin chi tiết sản phẩm với ID: ${id}`);

      // Sử dụng query mới với soldCount và canPurchase
      const query = this.createBaseQueryWithSalesInfo(currentUserId)
        .where('product.id = :id', { id })
        .andWhere('product.status = :approvedStatus', {
          approvedStatus: ProductStatus.APPROVED,
        })
        .andWhere('product.status != :deletedStatus', {
          deletedStatus: ProductStatus.DELETED,
        });

      // Thêm join với bảng users
      query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
        'users.id as users_id',
        'users.fullName as users_fullName',
        'users.email as users_email',
        'users.avatar as users_avatar',
        'users.phoneNumber as users_phoneNumber',
      ]);

      // Thêm join với bảng employees
      query
        .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
        .addSelect([
          'employees.id as employees_id',
          'employees.fullName as employees_fullName',
          'employees.email as employees_email',
          'employees.avatar as employees_avatar',
          'employees.phoneNumber as employees_phoneNumber',
        ]);

      // Cập nhật GROUP BY để bao gồm các cột từ JOIN
      query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

      // Sử dụng getRawAndEntities để lấy cả raw data
      const result = await query.getRawAndEntities();

      if (result.entities.length === 0) {
        this.logger.debug(`Không tìm thấy sản phẩm với ID ${id}`);
        return null;
      }

      // Gắn raw data vào entity
      this.attachRawDataToEntities(result.entities, result.raw);

      this.logger.debug(`Tìm thấy sản phẩm với ID ${id}`);
      return result.entities[0];
    } catch (error) {
      this.logger.error(
        `Failed to find product detail with ID ${id}: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Tìm sản phẩm theo ID và ID người dùng
   * @param id ID sản phẩm
   * @param userId ID người dùng
   * @returns Sản phẩm hoặc null
   */
  async findByIdAndUserId(id: number, userId: number): Promise<Product | null> {
    // Sử dụng query mới với soldCount và canPurchase
    const query = this.createBaseQueryWithSalesInfo(userId)
      .where('product.id = :id', { id })
      .andWhere('product.userId = :userId', { userId });

    // Thêm join với bảng users
    query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
      'users.id as users_id',
      'users.fullName as users_fullName',
      'users.email as users_email',
      'users.avatar as users_avatar',
      'users.phoneNumber as users_phoneNumber',
    ]);

    // Thêm join với bảng employees
    query
      .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
      .addSelect([
        'employees.id as employees_id',
        'employees.fullName as employees_fullName',
        'employees.email as employees_email',
        'employees.avatar as employees_avatar',
        'employees.phoneNumber as employees_phoneNumber',
      ]);

    // Cập nhật GROUP BY để bao gồm các cột từ JOIN
    query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

    // Sử dụng getRawAndEntities để lấy cả raw data
    const result = await query.getRawAndEntities();

    if (result.entities.length === 0) {
      return null;
    }

    // Gắn raw data vào entity
    this.attachRawDataToEntities(result.entities, result.raw);

    return result.entities[0];
  }

  /**
   * Tìm danh sách sản phẩm được phê duyệt, không thuộc về người dùng hiện tại
   * @param queryDto DTO truy vấn
   * @param currentUserId ID người dùng hiện tại
   * @returns Danh sách sản phẩm phân trang
   */
  async findApprovedProducts(
    queryDto: QueryProductDto,
    currentUserId: number,
  ): Promise<PaginatedResult<Product>> {
    const {
      page,
      limit,
      search,
      category,
      minPrice,
      maxPrice,
      sortBy,
      sortDirection,
    } = queryDto;
    const skip = (page - 1) * limit;

    // Sử dụng query mới với soldCount và canPurchase
    const query = this.createBaseQueryWithSalesInfo(currentUserId)
      .where('product.status = :status', { status: ProductStatus.APPROVED })
      .andWhere('product.status != :deletedStatus', {
        deletedStatus: ProductStatus.DELETED,
      })
      .andWhere('(product.userId != :userId OR product.userId IS NULL)', {
        userId: currentUserId,
      });

    // Thêm join với bảng users
    query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
      'users.id as users_id',
      'users.fullName as users_fullName',
      'users.email as users_email',
      'users.avatar as users_avatar',
      'users.phoneNumber as users_phoneNumber',
    ]);

    // Thêm join với bảng employees
    query
      .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
      .addSelect([
        'employees.id as employees_id',
        'employees.fullName as employees_fullName',
        'employees.email as employees_email',
        'employees.avatar as employees_avatar',
        'employees.phoneNumber as employees_phoneNumber',
      ]);

    // Cập nhật GROUP BY để bao gồm các cột từ JOIN
    query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

    // Áp dụng các bộ lọc
    if (search) {
      query.andWhere('LOWER(product.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }

    if (category) {
      query.andWhere('product.category = :category', { category });
    }

    if (minPrice !== undefined) {
      query.andWhere('product.discountedPrice >= :minPrice', { minPrice });
    }

    if (maxPrice !== undefined) {
      query.andWhere('product.discountedPrice <= :maxPrice', { maxPrice });
    }

    // Sắp xếp
    query.orderBy(`product.${sortBy}`, sortDirection);

    // Phân trang
    query.skip(skip).take(limit);

    // Sử dụng getRawAndEntities để lấy cả raw data
    const result = await query.getRawAndEntities();
    const totalItems = await query.getCount();

    // Gắn raw data vào entity
    this.attachRawDataToEntities(result.entities, result.raw);

    return {
      items: result.entities,
      meta: {
        totalItems,
        itemCount: result.entities.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm danh sách sản phẩm theo danh sách ID
   * @param productIds Danh sách ID sản phẩm
   * @param currentUserId ID người dùng hiện tại (tùy chọn)
   * @returns Danh sách sản phẩm
   */
  async findProductsByIds(productIds: number[], currentUserId?: number): Promise<Product[]> {
    if (!productIds || productIds.length === 0) {
      this.logger.warn('Không có ID sản phẩm nào được cung cấp');
      return [];
    }

    this.logger.debug(`Tìm sản phẩm với các ID: ${productIds.join(', ')}`);

    const query = this.createBaseQueryWithSalesInfo(currentUserId)
      .where('product.id IN (:...productIds)', { productIds })
      .andWhere('product.status = :status', { status: ProductStatus.APPROVED })
      .andWhere('product.status != :deletedStatus', {
        deletedStatus: ProductStatus.DELETED,
      });

    // Thêm join với bảng users
    query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
      'users.id as users_id',
      'users.fullName as users_fullName',
      'users.email as users_email',
      'users.avatar as users_avatar',
      'users.phoneNumber as users_phoneNumber',
    ]);

    // Thêm join với bảng employees
    query
      .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
      .addSelect([
        'employees.id as employees_id',
        'employees.fullName as employees_fullName',
        'employees.email as employees_email',
        'employees.avatar as employees_avatar',
        'employees.phoneNumber as employees_phoneNumber',
      ]);

    // Cập nhật GROUP BY để bao gồm các cột từ JOIN
    query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

    // Sử dụng getRawAndEntities để lấy cả raw data
    const result = await query.getRawAndEntities();

    // Gắn raw data vào entity
    this.attachRawDataToEntities(result.entities, result.raw);

    return result.entities;
  }

  /**
   * Kiểm tra sản phẩm đã được mua bởi người dùng
   * @param userId ID người dùng
   * @param productIds Danh sách ID sản phẩm
   * @returns Danh sách sản phẩm đã mua
   */
  async checkProductsPurchased(
    userId: number,
    productIds: number[],
  ): Promise<{ productId: number; productName: string }[]> {
    if (!userId || !productIds || productIds.length === 0) {
      this.logger.warn('Không có ID người dùng hoặc ID sản phẩm nào được cung cấp');
      return [];
    }

    this.logger.debug(`Kiểm tra sản phẩm đã mua cho user ${userId} với các ID: ${productIds.join(', ')}`);

    // Tìm các sản phẩm đã mua
    const query = this.dataSource
      .createQueryBuilder()
      .select('ol.product_id', 'productId')
      .addSelect('p.name', 'productName')
      .from('market_order_line', 'ol')
      .innerJoin('market_order', 'o', 'o.id = ol.order_id')
      .innerJoin('products', 'p', 'p.id = ol.product_id')
      .where('o.user_id = :userId', { userId })
      .andWhere('ol.product_id IN (:...productIds)', { productIds });

    const purchasedProducts = await query.getRawMany<{ productId: number; productName: string }>();

    this.logger.debug(`Tìm thấy ${purchasedProducts.length} sản phẩm đã mua`);
    return purchasedProducts;
  }

  /**
   * Tìm danh sách sản phẩm của người dùng
   * @param queryDto DTO truy vấn
   * @param userId ID người dùng
   * @returns Danh sách sản phẩm phân trang
   */
  async findUserProducts(
    queryDto: QueryUserProductDto,
    userId: number,
  ): Promise<PaginatedResult<Product>> {
    const { page, limit, search, category, status, sortBy, sortDirection } =
      queryDto;
    const skip = (page - 1) * limit;

    // Sử dụng query mới với soldCount và canPurchase
    const query = this.createBaseQueryWithSalesInfo(userId)
      .where('product.userId = :userId', { userId })
      .andWhere('product.status != :deletedStatus', {
        deletedStatus: ProductStatus.DELETED,
      });

    // Thêm join với bảng users
    query.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
      'users.id as users_id',
      'users.fullName as users_fullName',
      'users.email as users_email',
      'users.avatar as users_avatar',
      'users.phoneNumber as users_phoneNumber',
    ]);

    // Thêm join với bảng employees
    query
      .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
      .addSelect([
        'employees.id as employees_id',
        'employees.fullName as employees_fullName',
        'employees.email as employees_email',
        'employees.avatar as employees_avatar',
        'employees.phoneNumber as employees_phoneNumber',
      ]);

    // Cập nhật GROUP BY để bao gồm các cột từ JOIN
    query.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

    // Áp dụng các bộ lọc
    if (search) {
      query.andWhere('LOWER(product.name) LIKE LOWER(:search)', {
        search: `%${search}%`,
      });
    }

    if (category) {
      query.andWhere('product.category = :category', { category });
    }

    if (status) {
      query.andWhere('product.status = :status', { status });
    }

    // Sắp xếp
    query.orderBy(`product.${sortBy}`, sortDirection);

    // Phân trang
    query.skip(skip).take(limit);

    // Log SQL query được tạo ra
    const sqlQuery = query.getSql();
    const parameters = query.getParameters();
    this.logger.debug(`SQL Query: ${sqlQuery}`);
    this.logger.debug(`Query Parameters: ${JSON.stringify(parameters)}`);

    // Lấy cả raw data và entities
    const result = await query.getRawAndEntities();
    const entities = result.entities;
    const raw = result.raw;

    // Lấy tổng số items
    const totalItems = await query.getCount();

    // Log thông tin chi tiết từ raw data và entities
    if (raw.length > 0) {
      this.logger.debug(`Raw data keys: ${Object.keys(raw[0])}`);
      this.logger.debug(`Raw data first item: ${JSON.stringify(raw[0], null, 2)}`);
    }

    if (entities.length > 0) {
      this.logger.debug(`Entity keys: ${Object.keys(entities[0])}`);

      // Log cấu trúc của entity đầu tiên (không log toàn bộ dữ liệu)
      const sampleEntity = entities[0];
      this.logger.debug(`First entity structure: ${JSON.stringify({
        id: sampleEntity.id,
        name: sampleEntity.name,
        userId: sampleEntity.userId,
        // Thêm log để xem tất cả các thuộc tính
        allProperties: Object.keys(sampleEntity)
      }, null, 2)})`);
    }

    // Gắn raw data vào entities
    this.attachRawDataToEntities(entities, raw);

    // Kiểm tra lại sau khi gắn
    if (entities.length > 0) {
      const sampleEntity = entities[0] as any;
      this.logger.debug(`After attachment - Entity has user data: ${!!sampleEntity.users_fullname}`);
    }

    return {
      items: entities,
      meta: {
        totalItems,
        itemCount: entities.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID sản phẩm
   * @param updateData Dữ liệu cập nhật
   * @returns Sản phẩm đã cập nhật
   */
  async updateProduct(
    id: number,
    updateData: Partial<Product>,
  ): Promise<Product> {
    await this.update(id, {
      ...updateData,
      updatedAt: Math.floor(Date.now()),
    });

    // Nếu đang cập nhật status thành DELETED, sử dụng findByIdForAdmin để tìm sản phẩm
    // vì findById sẽ lọc ra các sản phẩm đã xóa
    const updatedProduct = updateData.status === ProductStatus.DELETED
      ? await this.findByIdForAdmin(id)
      : await this.findById(id);

    if (!updatedProduct) {
      throw new Error(`Product with ID ${id} not found after update`);
    }
    return updatedProduct;
  }

  /**
   * Cập nhật trạng thái sản phẩm
   * @param id ID sản phẩm
   * @param status Trạng thái mới
   * @returns Sản phẩm đã cập nhật
   */
  async updateProductStatus(
    id: number,
    status: ProductStatus,
  ): Promise<Product> {
    await this.update(id, {
      status,
      updatedAt: Math.floor(Date.now()),
    });

    // Nếu đang cập nhật status thành DELETED, sử dụng findByIdForAdmin để tìm sản phẩm
    // vì findById sẽ lọc ra các sản phẩm đã xóa
    const updatedProduct = status === ProductStatus.DELETED
      ? await this.findByIdForAdmin(id)
      : await this.findById(id);

    if (!updatedProduct) {
      throw new Error(`Product with ID ${id} not found after status update`);
    }
    return updatedProduct;
  }

  /**
   * Kiểm tra sản phẩm có thuộc về người dùng không
   * @param productId ID sản phẩm
   * @param userId ID người dùng
   * @returns true nếu sản phẩm thuộc về người dùng
   */
  async isProductOwnedByUser(
    productId: number,
    userId: number,
  ): Promise<boolean> {
    const count = await this.count({
      where: {
        id: productId,
        userId,
      },
    });
    return count > 0;
  }

  /**
   * Cập nhật trạng thái bán hàng cho bảng agents
   * @param queryRunner QueryRunner để thực hiện transaction
   * @param sourceId ID nguồn
   * @param isForSale Trạng thái bán hàng
   * @param timestamp Thời gian cập nhật
   */
  private async updateAgentsIsForSale(
    queryRunner: any,
    sourceId: string,
    isForSale: boolean,
    timestamp: number,
  ): Promise<void> {
    try {
      // Kiểm tra cấu trúc bảng
      const tableInfo = await queryRunner.query(
        `SELECT column_name FROM information_schema.columns WHERE table_name = 'agents'`
      );

      // Kiểm tra xem có trường updated_at không
      const hasUpdatedAt = tableInfo.some(col => col.column_name === 'updated_at');

      // Tạo câu lệnh SQL phù hợp
      let sql = '';
      let params: any[] = [];

      if (hasUpdatedAt) {
        sql = `UPDATE agents SET is_for_sale = $1, updated_at = $2 WHERE id = $3 OR source_id = $3`;
        params = [isForSale, timestamp, sourceId];
      } else {
        sql = `UPDATE agents SET is_for_sale = $1 WHERE id = $2 OR source_id = $2`;
        params = [isForSale, sourceId];
      }

      // Thực hiện cập nhật
      await queryRunner.query(sql, params);
    } catch (error) {
      // Nếu có lỗi, thử cập nhật chỉ với is_for_sale
      this.logger.warn(`Lỗi khi cập nhật agents: ${error.message}. Thử cập nhật chỉ với is_for_sale.`);
      await queryRunner.query(
        `UPDATE agents SET is_for_sale = $1 WHERE id = $2 OR source_id = $2`,
        [isForSale, sourceId]
      );
    }

    this.logger.log(
      `Đã cập nhật trạng thái bán hàng thành ${isForSale} cho bảng agents với sourceId: ${sourceId}`,
    );
  }

  /**
   * Cập nhật trạng thái bán hàng cho bảng admin_tools
   * @param queryRunner QueryRunner để thực hiện transaction
   * @param sourceId ID nguồn
   * @param isForSale Trạng thái bán hàng
   * @param timestamp Thời gian cập nhật
   */
  private async updateAdminToolsIsForSale(
    queryRunner: any,
    sourceId: string,
    isForSale: boolean,
    timestamp: number
  ): Promise<void> {
    try {
      // Kiểm tra cấu trúc bảng
      const tableInfo = await queryRunner.query(
        `SELECT column_name FROM information_schema.columns WHERE table_name = 'admin_tools'`
      );

      // Kiểm tra xem có trường updated_at không
      const hasUpdatedAt = tableInfo.some(col => col.column_name === 'updated_at');

      // Tạo câu lệnh SQL phù hợp
      let sql = '';
      let params: any[] = [];

      if (hasUpdatedAt) {
        sql = `UPDATE admin_tools SET is_for_sale = $1, updated_at = $2 WHERE id = $3`;
        params = [isForSale, timestamp, sourceId];
      } else {
        sql = `UPDATE admin_tools SET is_for_sale = $1 WHERE id = $2`;
        params = [isForSale, sourceId];
      }

      // Thực hiện cập nhật
      await queryRunner.query(sql, params);
    } catch (error) {
      // Nếu có lỗi, thử cập nhật chỉ với is_for_sale
      this.logger.warn(`Lỗi khi cập nhật admin_tools: ${error.message}. Thử cập nhật chỉ với is_for_sale.`);
      await queryRunner.query(
        `UPDATE admin_tools SET is_for_sale = $1 WHERE id = $2`,
        [isForSale, sourceId]
      );
    }

    this.logger.log(`Đã cập nhật trạng thái bán hàng thành ${isForSale} cho bảng admin_tools với sourceId: ${sourceId}`);
  }

  /**
   * Cập nhật trạng thái bán hàng cho bảng knowledge_files
   * @param queryRunner QueryRunner để thực hiện transaction
   * @param sourceId ID nguồn
   * @param isForSale Trạng thái bán hàng
   * @param timestamp Thời gian cập nhật
   */
  private async updateKnowledgeFilesIsForSale(
    queryRunner: any,
    sourceId: string,
    isForSale: boolean,
    timestamp: number,
  ): Promise<void> {
    try {
      // Trước tiên, kiểm tra cấu trúc bảng để xác định trường cập nhật thời gian
      const tableInfo = await queryRunner.query(
        `SELECT column_name FROM information_schema.columns WHERE table_name = 'knowledge_files'`
      );

      // Log cấu trúc bảng
      this.logger.debug(`Cấu trúc bảng knowledge_files: ${JSON.stringify(tableInfo.map(col => col.column_name))}`);

      // Kiểm tra xem có trường updated_at hay modified_at không
      const hasUpdatedAt = tableInfo.some(col => col.column_name === 'updated_at');
      const hasModifiedAt = tableInfo.some(col => col.column_name === 'modified_at');
      const hasCreatedAt = tableInfo.some(col => col.column_name === 'created_at');

      // Chọn trường thời gian phù hợp
      let timeField = '';
      if (hasUpdatedAt) {
        timeField = 'updated_at';
      } else if (hasModifiedAt) {
        timeField = 'modified_at';
      } else if (hasCreatedAt) {
        // Nếu không có trường updated_at hoặc modified_at, chỉ cập nhật is_for_sale
        timeField = '';
      }

      // Tạo câu lệnh SQL phù hợp
      let sql = '';
      let params: any[] = [];

      if (timeField) {
        sql = `UPDATE knowledge_files SET is_for_sale = $1, ${timeField} = $2 WHERE id = $3 OR source_id = $3`;
        params = [isForSale, timestamp, sourceId];
      } else {
        sql = `UPDATE knowledge_files SET is_for_sale = $1 WHERE id = $2 OR source_id = $2`;
        params = [isForSale, sourceId];
      }

      // Log SQL query
      this.logger.debug(`Executing SQL: ${sql} with params: ${JSON.stringify(params)}`);

      // Thực hiện cập nhật
      await queryRunner.query(sql, params);
    } catch (error) {
      // Nếu có lỗi, thử cập nhật chỉ với is_for_sale
      this.logger.warn(`Lỗi khi cập nhật đầy đủ: ${error.message}. Thử cập nhật chỉ với is_for_sale.`);
      await queryRunner.query(
        `UPDATE knowledge_files SET is_for_sale = $1 WHERE id = $2 OR source_id = $2`,
        [isForSale, sourceId]
      );
    }

    this.logger.log(
      `Đã cập nhật trạng thái bán hàng thành ${isForSale} cho bảng knowledge_files với sourceId: ${sourceId}`,
    );
  }

  /**
   * Cập nhật trạng thái bán hàng cho bảng data_fine_tuning
   * @param queryRunner QueryRunner để thực hiện transaction
   * @param sourceId ID nguồn
   * @param isForSale Trạng thái bán hàng
   * @param timestamp Thời gian cập nhật
   */
  private async updateDataFineTuningIsForSale(
    queryRunner: any,
    sourceId: string,
    isForSale: boolean,
    timestamp: number,
  ): Promise<void> {
    try {
      // Kiểm tra cấu trúc bảng
      const tableInfo = await queryRunner.query(
        `SELECT column_name FROM information_schema.columns WHERE table_name = 'data_fine_tuning'`
      );

      // Kiểm tra xem có trường updated_at không
      const hasUpdatedAt = tableInfo.some(col => col.column_name === 'updated_at');

      // Tạo câu lệnh SQL phù hợp
      let sql = '';
      let params: any[] = [];

      if (hasUpdatedAt) {
        sql = `UPDATE data_fine_tuning SET is_for_sale = $1, updated_at = $2 WHERE id = $3 OR source_id = $3`;
        params = [isForSale, timestamp, sourceId];
      } else {
        sql = `UPDATE data_fine_tuning SET is_for_sale = $1 WHERE id = $2 OR source_id = $2`;
        params = [isForSale, sourceId];
      }

      // Thực hiện cập nhật
      await queryRunner.query(sql, params);
    } catch (error) {
      // Nếu có lỗi, thử cập nhật chỉ với is_for_sale
      this.logger.warn(`Lỗi khi cập nhật data_fine_tuning: ${error.message}. Thử cập nhật chỉ với is_for_sale.`);
      await queryRunner.query(
        `UPDATE data_fine_tuning SET is_for_sale = $1 WHERE id = $2 OR source_id = $2`,
        [isForSale, sourceId]
      );
    }

    this.logger.log(
      `Đã cập nhật trạng thái bán hàng thành ${isForSale} cho bảng data_fine_tuning với sourceId: ${sourceId}`,
    );
  }

  /**
   * Cập nhật trạng thái bán hàng cho bảng strategy_agents
   * @param queryRunner QueryRunner để thực hiện transaction
   * @param sourceId ID nguồn
   * @param isForSale Trạng thái bán hàng
   * @param timestamp Thời gian cập nhật
   */
  private async updateStrategyAgentsIsForSale(
    queryRunner: any,
    sourceId: string,
    isForSale: boolean,
    timestamp: number
  ): Promise<void> {
    try {
      // Kiểm tra cấu trúc bảng
      const tableInfo = await queryRunner.query(
        `SELECT column_name FROM information_schema.columns WHERE table_name = 'strategy_agents'`
      );

      // Kiểm tra xem có trường updated_at không
      const hasUpdatedAt = tableInfo.some(col => col.column_name === 'updated_at');

      // Tạo câu lệnh SQL phù hợp
      let sql = '';
      let params: any[] = [];

      if (hasUpdatedAt) {
        sql = `UPDATE strategy_agents SET is_for_sale = $1, updated_at = $2 WHERE id = $3`;
        params = [isForSale, timestamp, sourceId];
      } else {
        sql = `UPDATE strategy_agents SET is_for_sale = $1 WHERE id = $2`;
        params = [isForSale, sourceId];
      }

      // Thực hiện cập nhật
      await queryRunner.query(sql, params);
    } catch (error) {
      // Nếu có lỗi, thử cập nhật chỉ với is_for_sale
      this.logger.warn(`Lỗi khi cập nhật strategy_agents: ${error.message}. Thử cập nhật chỉ với is_for_sale.`);
      await queryRunner.query(
        `UPDATE strategy_agents SET is_for_sale = $1 WHERE id = $2`,
        [isForSale, sourceId]
      );
    }

    this.logger.log(`Đã cập nhật trạng thái bán hàng thành ${isForSale} cho bảng strategy_agents với sourceId: ${sourceId}`);
  }

  /**
   * Cập nhật trạng thái bán hàng của tất cả các bảng
   * @param sourceId ID nguồn
   * @param isForSale Trạng thái bán hàng
   * @returns true nếu cập nhật thành công
   */
  async updateAllTablesIsForSale(
    sourceId: string,
    isForSale: boolean
  ): Promise<boolean> {
    const queryRunner = this.dataSource.createQueryRunner();
    const timestamp = Math.floor(Date.now());

    try {
      // Bắt đầu transaction
      await queryRunner.connect();
      await queryRunner.startTransaction();

      // Cập nhật tất cả các bảng
      await this.updateAgentsIsForSale(queryRunner, sourceId, isForSale, timestamp);
      await this.updateAdminToolsIsForSale(queryRunner, sourceId, isForSale, timestamp);
      await this.updateKnowledgeFilesIsForSale(queryRunner, sourceId, isForSale, timestamp);
      await this.updateDataFineTuningIsForSale(queryRunner, sourceId, isForSale, timestamp);
      await this.updateStrategyAgentsIsForSale(queryRunner, sourceId, isForSale, timestamp);

      // Commit transaction
      await queryRunner.commitTransaction();
      this.logger.log(`Đã cập nhật trạng thái bán hàng thành ${isForSale} cho tất cả bảng với sourceId: ${sourceId}`);
      return true;
    } catch (error) {
      // Rollback nếu có lỗi
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi cập nhật trạng thái bán hàng cho tất cả bảng: ${error.message}`, error.stack);
      return false;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }

  /**
   * Cập nhật trạng thái bán hàng cho bảng cụ thể
   * @param tableName Tên bảng hoặc category
   * @param sourceId ID nguồn
   * @param isForSale Trạng thái bán hàng
   * @returns true nếu cập nhật thành công
   */
  async updateTableIsForSale(
    tableName: string,
    sourceId: string,
    isForSale: boolean
  ): Promise<boolean> {
    const queryRunner = this.dataSource.createQueryRunner();
    const timestamp = Math.floor(Date.now());

    try {
      // Bắt đầu transaction
      await queryRunner.connect();
      await queryRunner.startTransaction();

      // Chuyển đổi từ category sang tên bảng
      const normalizedTableName = this.getCategoryTableName(tableName);
      this.logger.debug(`Chuyển đổi từ category ${tableName} sang tên bảng ${normalizedTableName}`);

      // Cập nhật bảng cụ thể
      switch (normalizedTableName) {
        case 'agents':
          await this.updateAgentsIsForSale(queryRunner, sourceId, isForSale, timestamp);
          break;

        case 'admin_tools':
          await this.updateAdminToolsIsForSale(queryRunner, sourceId, isForSale, timestamp);
          break;

        case 'knowledge_files':
          await this.updateKnowledgeFilesIsForSale(queryRunner, sourceId, isForSale, timestamp);
          break;

        case 'data_fine_tuning':
          await this.updateDataFineTuningIsForSale(queryRunner, sourceId, isForSale, timestamp);
          break;

        case 'strategy_agents':
          await this.updateStrategyAgentsIsForSale(queryRunner, sourceId, isForSale, timestamp);
          break;

        default:
          this.logger.warn(`Không tìm thấy bảng: ${normalizedTableName} (từ category: ${tableName})`);
          throw new Error(`Bảng không hợp lệ: ${tableName}`);
      }

      // Commit transaction
      await queryRunner.commitTransaction();
      return true;
    } catch (error) {
      // Rollback nếu có lỗi
      await queryRunner.rollbackTransaction();
      this.logger.error(`Lỗi khi cập nhật trạng thái bán hàng cho bảng ${tableName}: ${error.message}`, error.stack);
      return false;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }

  /**
   * Chuyển đổi từ category sang tên bảng
   * @param category Category hoặc tên bảng
   * @returns Tên bảng tương ứng
   */
  private getCategoryTableName(category: string): string {
    // Nếu đã là tên bảng hợp lệ, trả về nguyên
    const validTableNames = ['agents', 'admin_tools', 'knowledge_files', 'data_fine_tuning', 'strategy_agents'];
    if (validTableNames.includes(category.toLowerCase())) {
      return category.toLowerCase();
    }

    // Chuyển đổi từ category sang tên bảng
    switch (category.toUpperCase()) {
      case 'AGENT':
      case 'AGENTS':
        return 'agents';

      case 'ADMIN_TOOL':
      case 'ADMIN_TOOLS':
        return 'admin_tools';

      case 'KNOWLEDGE_FILE':
      case 'KNOWLEDGE_FILES':
        return 'knowledge_files';

      case 'DATA_FINE_TUNING':
      case 'DATA_FINE_TUNINGS':
        return 'data_fine_tuning';

      case 'STRATEGY_AGENT':
      case 'STRATEGY_AGENTS':
        return 'strategy_agents';

      default:
        this.logger.warn(`Không thể chuyển đổi category: ${category} sang tên bảng`);
        return category.toLowerCase(); // Trả về giá trị mặc định để xử lý lỗi ở nơi gọi
    }
  }

  /**
   * Phương thức chính để cập nhật trạng thái bán hàng
   * @param sourceId ID của tài nguyên gốc
   * @param isForSale Trạng thái bán hàng mới
   * @param tableName Tên bảng hoặc category cần cập nhật (tùy chọn)
   * @returns true nếu cập nhật thành công
   */
  async updateIsForSale(
    sourceId: string,
    isForSale: boolean,
    tableName?: string
  ): Promise<boolean> {
    try {
      if (!sourceId) {
        this.logger.warn('Không có sourceId để cập nhật trạng thái bán hàng');
        return false;
      }

      if (tableName) {
        this.logger.debug(`Cập nhật trạng thái bán hàng cho bảng/category: ${tableName}`);
        return await this.updateTableIsForSale(tableName, sourceId, isForSale);
      } else {
        return await this.updateAllTablesIsForSale(sourceId, isForSale);
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật trạng thái bán hàng: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Tìm danh sách sản phẩm cho admin với phân trang, tìm kiếm, lọc và sắp xếp
   * @param queryDto DTO truy vấn từ admin
   * @param currentUserId ID người dùng hiện tại (tùy chọn, cho admin thường không cần)
   * @returns Danh sách sản phẩm phân trang
   */
  async findAdminProducts(
    queryDto: AdminQueryProductDto,
    currentUserId?: number
  ): Promise<PaginatedResult<Product>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        category,
        status,
        userId,
        employeeId,
        includeDeleted = false,
        sortBy = 'createdAt',
        sortDirection = 'DESC'
      } = queryDto;

      const skip = (page - 1) * limit;

      // Sử dụng query mới với soldCount và canPurchase
      const queryBuilder = this.createBaseQueryWithSalesInfo(currentUserId);

      // Thêm join với bảng users
      queryBuilder.leftJoin(User, 'users', 'users.id = product.user_id').addSelect([
        'users.id as users_id',
        'users.fullName as users_fullName',
        'users.email as users_email',
        'users.avatar as users_avatar',
        'users.phoneNumber as users_phoneNumber',
      ]);

      // Thêm join với bảng employees
      queryBuilder
        .leftJoin(Employee, 'employees', 'employees.id = product.employee_id')
        .addSelect([
          'employees.id as employees_id',
          'employees.fullName as employees_fullName',
          'employees.email as employees_email',
          'employees.avatar as employees_avatar',
          'employees.phoneNumber as employees_phoneNumber',
        ]);

      // Cập nhật GROUP BY để bao gồm các cột từ JOIN
      queryBuilder.groupBy('product.id, product.category, product.name, product.description, product.listed_price, product.discounted_price, product.images, product.user_manual, product.detail, product.status, product.created_at, product.updated_at, product.employee_id, product.user_id, product.source_id, users.id, users.fullName, users.email, users.avatar, users.phoneNumber, employees.id, employees.fullName, employees.email, employees.avatar, employees.phoneNumber');

      // Áp dụng các bộ lọc
      if (search) {
        queryBuilder.andWhere('LOWER(product.name) LIKE LOWER(:search)', { search: `%${search}%` });
      }

      if (category) {
        queryBuilder.andWhere('product.category = :category', { category });
      }

      if (status) {
        queryBuilder.andWhere('product.status = :status', { status });
      } else {
        // Nếu không có lọc theo status và không bao gồm sản phẩm đã xóa
        if (!includeDeleted) {
          queryBuilder.andWhere('product.status != :deletedStatus', { deletedStatus: ProductStatus.DELETED });
        }
      }

      if (userId) {
        // Sử dụng tên trường đúng trong cơ sở dữ liệu
        queryBuilder.andWhere('product.user_id = :userId', { userId });
        this.logger.debug(`Filtering by userId: ${userId}`);

        // Log SQL query để debug
        const sqlQuery = queryBuilder.getSql();
        const parameters = queryBuilder.getParameters();
        this.logger.debug(`SQL Query with userId filter: ${sqlQuery}`);
        this.logger.debug(`Query Parameters: ${JSON.stringify(parameters)}`);
      }

      if (employeeId) {
        // Sử dụng tên trường đúng trong cơ sở dữ liệu
        queryBuilder.andWhere('product.employee_id = :employeeId', { employeeId });
        this.logger.debug(`Filtering by employeeId: ${employeeId}`);
      }

      // Sắp xếp
      queryBuilder.orderBy(`product.${sortBy}`, sortDirection as 'ASC' | 'DESC');

      // Phân trang
      queryBuilder.skip(skip).take(limit);

      // Lấy dữ liệu và tổng số bản ghi
      const [result, totalItems] = await Promise.all([
        queryBuilder.getRawAndEntities(),
        queryBuilder.getCount(),
      ]);

      // Gắn raw data vào entity
      this.attachRawDataToEntities(result.entities, result.raw);

      // Log thông tin raw data để debug
      if (result.entities.length > 0 && result.raw.length > 0) {
        this.logger.debug(`Raw data keys for first product: ${Object.keys(result.raw[0]).join(', ')}`);
        this.logger.debug(`Found ${result.entities.length} products`);
      } else {
        this.logger.debug(`No products found for the query`);
      }

      const items = result.entities;

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to find admin products: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái sản phẩm và xử lý các tác vụ liên quan
   * @param productId ID sản phẩm
   * @param newStatus Trạng thái mới
   * @returns Sản phẩm đã cập nhật
   */
  async updateProductStatusWithRelatedTasks(
    productId: number,
    newStatus: ProductStatus
  ): Promise<Product> {
    try {
      // Tìm sản phẩm theo ID
      const product = await this.findById(productId);

      if (!product) {
        throw new Error(`Product with ID ${productId} not found`);
      }

      const currentStatus = product.status;

      // Cập nhật trạng thái sản phẩm
      product.status = newStatus;
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      const savedProduct = await this.save(product);

      // Cập nhật trạng thái bán hàng trong bảng danh mục tương ứng
      if (product.sourceId && product.category) {
        // Log chi tiết về sourceId và category
        this.logger.debug(`Product ${productId} có sourceId: '${product.sourceId}' và category: '${product.category}'`);

        // Nếu chuyển sang APPROVED, cập nhật isForSale = true
        if (newStatus === ProductStatus.APPROVED) {
          this.logger.debug(`Cập nhật isForSale = true cho ${product.category} với sourceId ${product.sourceId}`);
          const updateResult = await this.updateIsForSale(product.sourceId, true, product.category);
          this.logger.log(`Kết quả cập nhật trạng thái bán hàng: ${updateResult ? 'Thành công' : 'Thất bại'} cho ${product.category} với sourceId ${product.sourceId}`);
        }
        // Nếu chuyển từ APPROVED sang trạng thái khác, cập nhật isForSale = false
        else if (currentStatus === ProductStatus.APPROVED) {
          this.logger.debug(`Cập nhật isForSale = false cho ${product.category} với sourceId ${product.sourceId}`);
          const updateResult = await this.updateIsForSale(product.sourceId, false, product.category);
          this.logger.log(`Kết quả cập nhật trạng thái không bán hàng: ${updateResult ? 'Thành công' : 'Thất bại'} cho ${product.category} với sourceId ${product.sourceId}`);
        }
      } else {
        this.logger.warn(`Không thể cập nhật trạng thái bán hàng: sourceId=${product.sourceId}, category=${product.category}`);
      }

      return savedProduct;
    } catch (error) {
      this.logger.error(`Failed to update product status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa sản phẩm (chuyển trạng thái sang DELETED)
   * @param productId ID sản phẩm
   * @returns true nếu xóa thành công
   */
  async deleteProductById(productId: number): Promise<boolean> {
    try {
      // Tìm sản phẩm theo ID
      const product = await this.findById(productId);

      if (!product) {
        throw new Error(`Product with ID ${productId} not found`);
      }

      // Cập nhật trạng thái thành DELETED
      product.status = ProductStatus.DELETED;
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      await this.save(product);

      // Nếu sản phẩm có sourceId và category, cập nhật trạng thái bán hàng
      if (product.sourceId && product.category) {
        await this.updateIsForSale(product.sourceId, false, product.category);
        this.logger.log(`Đã cập nhật trạng thái không bán hàng cho ${product.category} với sourceId ${product.sourceId}`);
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to delete product: ${error.message}`, error.stack);
      return false;
    }
  }
}