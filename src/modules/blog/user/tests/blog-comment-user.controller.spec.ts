import { Test, TestingModule } from '@nestjs/testing';
import { B<PERSON><PERSON>ommentUserController } from '../controllers/blog-comment-user.controller';
import { BlogCommentUserService } from '../services/blog-comment-user.service';
import { CreateBlogCommentDto } from '../../dto/create-blog-comment.dto';
import { GetBlogCommentsDto } from '../../dto/get-blog-comments.dto';
import { NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { AuthorTypeEnum } from '../../enums';

// Mock the SWAGGER_API_TAGS import
jest.mock('@/common/swagger/swagger.tags', () => ({
  SWAGGER_API_TAGS: {
    BLOG_COMMENTS: 'blog-comments'
  }
}));

// Mock the ErrorResponseSchema import
jest.mock('@/common/swagger', () => ({
  ErrorResponseSchema: {}
}));

// Mock ConfigModule
jest.mock('@nestjs/config', () => {
  const originalModule = jest.requireActual('@nestjs/config');
  return {
    ...originalModule,
    ConfigModule: {
      forRoot: jest.fn().mockReturnValue({
        module: class ConfigModule {},
        providers: [],
      }),
    },
  };
});

// Mock S3Service
jest.mock('@/shared/services/s3.service', () => ({
  S3Service: jest.fn().mockImplementation(() => ({
    createPresignedWithID: jest.fn(),
  })),
}));

// Mock JwtAuthGuard
jest.mock('@/modules/auth/guards', () => ({
  JwtAuthGuard: jest.fn().mockImplementation(() => ({
    canActivate: jest.fn().mockReturnValue(true),
  })),
}));

// Mock CurrentUser decorator
jest.mock('@/common/decorators/current-user.decorator', () => ({
  CurrentUser: () => () => 1, // Always return user ID 1
}));

describe('BlogCommentUserController', () => {
  let controller: BlogCommentUserController;
  let service: BlogCommentUserService;

  const mockBlogCommentUserService = {
    createComment: jest.fn(),
    deleteComment: jest.fn(),
    getBlogComments: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BlogCommentUserController],
      providers: [
        {
          provide: BlogCommentUserService,
          useValue: mockBlogCommentUserService,
        },
      ],
    }).compile();

    controller = module.get<BlogCommentUserController>(BlogCommentUserController);
    service = module.get<BlogCommentUserService>(BlogCommentUserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createComment', () => {
    it('should create a comment and return success response', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test comment',
      };
      mockBlogCommentUserService.createComment.mockResolvedValue({
        id: 1,
        blogId: 1,
        userId: 1,
        content: 'Test comment',
        createdAt: Date.now(),
        authorType: AuthorTypeEnum.USER,
        employeeId: null,
        parentCommentId: null,
      });

      // Act
      const result = await controller.createComment(blogId, userId, createCommentDto);

      // Assert
      expect(service.createComment).toHaveBeenCalledWith(blogId, userId, createCommentDto);
      expect(result).toEqual({
        code: 201,
        message: 'Comment created successfully',
      });
    });

    it('should create a reply comment and return success response', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test reply comment',
        parent_comment_id: 1,
      };
      mockBlogCommentUserService.createComment.mockResolvedValue({
        id: 2,
        blogId: 1,
        userId: 1,
        content: 'Test reply comment',
        createdAt: Date.now(),
        authorType: AuthorTypeEnum.USER,
        employeeId: null,
        parentCommentId: 1,
      });

      // Act
      const result = await controller.createComment(blogId, userId, createCommentDto);

      // Assert
      expect(service.createComment).toHaveBeenCalledWith(blogId, userId, createCommentDto);
      expect(result).toEqual({
        code: 201,
        message: 'Comment created successfully',
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test comment',
      };
      mockBlogCommentUserService.createComment.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.createComment(blogId, userId, createCommentDto)).rejects.toThrow(NotFoundException);
    });

    it('should handle parent comment not found error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const createCommentDto: CreateBlogCommentDto = {
        content: 'Test reply comment',
        parent_comment_id: 999,
      };
      mockBlogCommentUserService.createComment.mockRejectedValue(
        new NotFoundException('Parent comment not found')
      );

      // Act & Assert
      await expect(controller.createComment(blogId, userId, createCommentDto)).rejects.toThrow(NotFoundException);
    });

    it('should handle validation exceptions', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const createCommentDto: CreateBlogCommentDto = {
        content: '', // Empty content should fail validation
      };
      mockBlogCommentUserService.createComment.mockRejectedValue(
        new BadRequestException('Content cannot be empty')
      );

      // Act & Assert
      await expect(controller.createComment(blogId, userId, createCommentDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment and return success response', async () => {
      // Arrange
      const commentId = 1;
      const userId = 1;
      mockBlogCommentUserService.deleteComment.mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteComment(commentId, userId);

      // Assert
      expect(service.deleteComment).toHaveBeenCalledWith(commentId, userId);
      expect(result).toEqual({
        code: 200,
        message: 'Comment deleted successfully',
        result: null,
      });
    });

    it('should handle comment not found error', async () => {
      // Arrange
      const commentId = 999;
      const userId = 1;
      mockBlogCommentUserService.deleteComment.mockRejectedValue(new NotFoundException('Comment not found'));

      // Act & Assert
      await expect(controller.deleteComment(commentId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should handle permission error when deleting a comment', async () => {
      // Arrange
      const commentId = 1;
      const userId = 2; // Different user than the comment owner
      mockBlogCommentUserService.deleteComment.mockRejectedValue(
        new ForbiddenException('You do not have permission to delete this comment')
      );

      // Act & Assert
      await expect(controller.deleteComment(commentId, userId)).rejects.toThrow(ForbiddenException);
    });
  });

  describe('getBlogComments', () => {
    it('should return paginated comments', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockComments = {
        content: [
          {
            id: 1,
            blogId: 1,
            userId: 10,
            createdAt: 1632474086123,
            content: 'Test comment',
            authorType: 'USER',
            employeeId: null,
            parentCommentId: null,
            replies: [],
            user: {
              id: 10,
              fullName: 'Comment Author',
              avatar: 'https://cdn.example.com/avatars/user10.jpg',
            },
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogCommentUserService.getBlogComments.mockResolvedValue(mockComments);

      // Act
      const result = await controller.getBlogComments(blogId, query);

      // Assert
      expect(service.getBlogComments).toHaveBeenCalledWith(blogId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockComments,
      });
    });

    it('should return paginated comments with replies', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockComments = {
        content: [
          {
            id: 1,
            blogId: 1,
            userId: 10,
            createdAt: 1632474086123,
            content: 'Test comment',
            authorType: 'USER',
            employeeId: null,
            parentCommentId: null,
            user: {
              id: 10,
              fullName: 'Comment Author',
              avatar: 'https://cdn.example.com/avatars/user10.jpg',
            },
            replies: [
              {
                id: 2,
                blogId: 1,
                userId: 11,
                createdAt: 1632474186123,
                content: 'Test reply',
                authorType: 'USER',
                employeeId: null,
                parentCommentId: 1,
                user: {
                  id: 11,
                  fullName: 'Reply Author',
                  avatar: 'https://cdn.example.com/avatars/user11.jpg',
                },
              },
            ],
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogCommentUserService.getBlogComments.mockResolvedValue(mockComments);

      // Act
      const result = await controller.getBlogComments(blogId, query);

      // Assert
      expect(service.getBlogComments).toHaveBeenCalledWith(blogId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockComments,
      });
    });

    it('should handle empty comments list', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockEmptyComments = {
        content: [],
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      };
      mockBlogCommentUserService.getBlogComments.mockResolvedValue(mockEmptyComments);

      // Act
      const result = await controller.getBlogComments(blogId, query);

      // Assert
      expect(service.getBlogComments).toHaveBeenCalledWith(blogId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyComments,
      });
    });

    it('should handle pagination with different page and limit values', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 2, limit: 5 };
      const mockComments = {
        content: [
          {
            id: 6,
            blogId: 1,
            userId: 10,
            createdAt: 1632474086123,
            content: 'Another test comment',
            authorType: 'USER',
            employeeId: null,
            parentCommentId: null,
            replies: [],
            user: {
              id: 10,
              fullName: 'Comment Author',
              avatar: 'https://cdn.example.com/avatars/user10.jpg',
            },
          },
        ],
        totalItems: 6,
        itemCount: 1,
        itemsPerPage: 5,
        totalPages: 2,
        currentPage: 2,
      };
      mockBlogCommentUserService.getBlogComments.mockResolvedValue(mockComments);

      // Act
      const result = await controller.getBlogComments(blogId, query);

      // Assert
      expect(service.getBlogComments).toHaveBeenCalledWith(blogId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockComments,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };
      mockBlogCommentUserService.getBlogComments.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.getBlogComments(blogId, query)).rejects.toThrow(NotFoundException);
    });
  });
});
