import { Injectable, Logger } from '@nestjs/common';
import { RuleContractRepository, UserRepository } from '../repositories';
import {
  RuleContractContext,
  RuleContractEvent,
  IndividualContractData
} from './rule-contract.types';
import { ContractStatusEnum, ContractTypeEnum } from '../entities/rule-contract.entity';
import { EmailService } from '@/modules/email/services/email.service';
import { UserTypeEnum } from '@/modules/user/enums';
import { AppException } from '@common/exceptions';
import { RULE_CONTRACT_ERROR_CODES } from '../errors';
import { ContractHelperService } from '../user/services/contract-helper.service';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { PdfPosition } from '@/shared/interface/pdf-edit.interface';
import { EmailPlaceholderService } from '@/modules/email/services';

/**
 * Service triển khai các actions và guards cho máy trạng thái hợp đồng nguyên tắc
 */
@Injectable()
export class RuleContractActionsService {
  private readonly logger = new Logger(RuleContractActionsService.name);

  constructor(
    private readonly ruleContractRepository: RuleContractRepository,
    private readonly emailService: EmailPlaceholderService,
    private readonly userRepository: UserRepository,
    private readonly contractHelperService: ContractHelperService,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lưu hợp đồng mới hoặc cập nhật hợp đồng hiện có
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns Ngữ cảnh đã cập nhật
   */
  async saveContract(
    context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): Promise<Partial<RuleContractContext>> {
    try {
      const { userId, contractId, contractType, contractStatus, contractKey } = context;
      const { data } = event;

      // Nếu đã có contractId, cập nhật hợp đồng
      if (contractId) {
        const contract = await this.ruleContractRepository.findById(contractId);
        if (contract) {
          // Cập nhật thông tin hợp đồng
          contract.type = data?.contractType || contractType;
          contract.contractUrlPdf = data?.contractKey || contractKey;
          contract.updatedAt = Date.now();

          await this.ruleContractRepository.save(contract);

          return {
            contractType: contract.type,
            contractKey: contract.contractUrlPdf,
            updatedAt: contract.updatedAt,
          };
        }
      }

      // Tạo hợp đồng mới
      const newContract = this.ruleContractRepository.create();
      newContract.userId = userId || 0; // Đảm bảo không null
      newContract.type = data?.contractType || contractType;
      newContract.status = contractStatus;
      newContract.contractUrlPdf = data?.contractKey || contractKey;
      newContract.createdAt = Date.now();
      newContract.updatedAt = Date.now();

      // Lưu hợp đồng vào database
      const savedContract = await this.ruleContractRepository.save(newContract);

      // Cập nhật ngữ cảnh
      return {
        contractId: savedContract.id,
        contractType: savedContract.type,
        contractStatus: savedContract.status,
        contractKey: savedContract.contractUrlPdf,
        createdAt: savedContract.createdAt,
        updatedAt: savedContract.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lưu hợp đồng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lưu chữ ký của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns Ngữ cảnh đã cập nhật
   */
  async saveUserSignature(
    context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): Promise<Partial<RuleContractContext>> {
    try {
      const { contractId } = context;
      const { data } = event;

      if (!contractId) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy ID hợp đồng trong ngữ cảnh',
        );
      }

      // Lấy hợp đồng từ database
      const contract = await this.ruleContractRepository.findById(contractId);
      if (!contract) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Không tìm thấy hợp đồng với ID ${contractId}`,
        );
      }

      // Cập nhật trạng thái và thời gian ký
      const now = Date.now();
      contract.status = ContractStatusEnum.PENDING_APPROVAL;
      contract.userSignatureAt = now;
      contract.updatedAt = now;

      // Lưu hợp đồng vào database
      await this.ruleContractRepository.save(contract);

      // Cập nhật ngữ cảnh
      return {
        contractStatus: contract.status,
        userSignatureAt: contract.userSignatureAt,
        signatureData: data?.signatureData,
        updatedAt: contract.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lưu chữ ký người dùng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lưu phê duyệt của admin
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns Ngữ cảnh đã cập nhật
   */
  async saveAdminApproval(
    context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): Promise<Partial<RuleContractContext>> {
    try {
      const { contractId } = context;
      const { data } = event;

      if (!contractId) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy ID hợp đồng trong ngữ cảnh',
        );
      }

      // Lấy hợp đồng từ database
      const contract = await this.ruleContractRepository.findById(contractId);
      if (!contract) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Không tìm thấy hợp đồng với ID ${contractId}`,
        );
      }

      // Cập nhật trạng thái và thời gian phê duyệt
      const now = Date.now();
      contract.status = ContractStatusEnum.APPROVED;
      contract.adminSignatureAt = now;
      contract.updatedAt = now;

      // Lưu hợp đồng vào database
      await this.ruleContractRepository.save(contract);

      // Cập nhật ngữ cảnh
      return {
        contractStatus: contract.status,
        adminId: data?.adminId,
        adminSignatureAt: contract.adminSignatureAt,
        updatedAt: contract.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lưu phê duyệt của admin: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lưu từ chối của admin
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns Ngữ cảnh đã cập nhật
   */
  async saveRejection(
    context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): Promise<Partial<RuleContractContext>> {
    try {
      const { contractId } = context;
      const { data } = event;

      if (!contractId) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy ID hợp đồng trong ngữ cảnh',
        );
      }

      // Lấy hợp đồng từ database
      const contract = await this.ruleContractRepository.findById(contractId);
      if (!contract) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Không tìm thấy hợp đồng với ID ${contractId}`,
        );
      }

      // Cập nhật trạng thái và lý do từ chối
      const now = Date.now();
      contract.status = ContractStatusEnum.REJECTED;
      contract.rejectReason = data?.rejectionReason;
      contract.updatedAt = now;

      // Lưu hợp đồng vào database
      await this.ruleContractRepository.save(contract);

      // Cập nhật ngữ cảnh
      return {
        contractStatus: contract.status,
        adminId: data?.adminId,
        rejectionReason: data?.rejectionReason,
        updatedAt: contract.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lưu từ chối của admin: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa lý do từ chối khi gửi lại hợp đồng
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns Ngữ cảnh đã cập nhật
   */
  async clearRejectionReason(
    context: RuleContractContext,
    _event: { type: RuleContractEvent; data?: any }
  ): Promise<Partial<RuleContractContext>> {
    try {
      const { contractId } = context;

      if (!contractId) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy ID hợp đồng trong ngữ cảnh',
        );
      }

      // Lấy hợp đồng từ database
      const contract = await this.ruleContractRepository.findById(contractId);
      if (!contract) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          `Không tìm thấy hợp đồng với ID ${contractId}`,
        );
      }

      // Cập nhật trạng thái và xóa lý do từ chối
      const now = Date.now();
      contract.status = ContractStatusEnum.DRAFT;
      contract.rejectReason = '';
      contract.updatedAt = now;

      // Lưu hợp đồng vào database
      await this.ruleContractRepository.save(contract);

      // Cập nhật ngữ cảnh
      return {
        contractStatus: contract.status,
        rejectionReason: null,
        updatedAt: contract.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa lý do từ chối: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Nâng cấp loại hợp đồng từ cá nhân lên doanh nghiệp
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns Ngữ cảnh đã cập nhật
   */
  async upgradeContractType(
    context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): Promise<Partial<RuleContractContext>> {
    try {
      const { contractId, userId } = context;
      const { data } = event;

      // Nếu có contractId, cập nhật hợp đồng
      if (contractId) {
        const contract = await this.ruleContractRepository.findById(contractId);
        if (contract) {
          // Cập nhật loại hợp đồng
          const now = Date.now();
          contract.type = ContractTypeEnum.BUSINESS;
          contract.contractUrlPdf = data?.newContractKey || contract.contractUrlPdf;
          contract.updatedAt = now;

          await this.ruleContractRepository.save(contract);

          // Cập nhật loại người dùng
          if (userId) {
            const user = await this.userRepository.findById(userId);
            if (user) {
              user.type = UserTypeEnum.BUSINESS;
              await this.userRepository.saveUser(user);
            }
          }

          return {
            contractType: contract.type,
            contractKey: contract.contractUrlPdf,
            updatedAt: contract.updatedAt,
          };
        }
      }

      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
        'Không tìm thấy hợp đồng để nâng cấp',
      );
    } catch (error) {
      this.logger.error(`Lỗi khi nâng cấp loại hợp đồng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gửi thông báo cho admin khi người dùng ký hợp đồng
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   */
  async notifyAdmin(
    context: RuleContractContext,
    _event: { type: RuleContractEvent; data?: any }
  ): Promise<void> {
    try {
      const { contractId, userId, contractType } = context;

      // Lấy thông tin người dùng
      if (!userId) {
        this.logger.warn(`ID người dùng không hợp lệ`);
        return;
      }

      const user = await this.userRepository.findById(userId);
      if (!user) {
        this.logger.warn(`Không tìm thấy người dùng với ID ${userId}`);
        return;
      }

      // Gửi email thông báo cho admin
      await this.emailService.sendRuleContractProcessing({
        EMAIL: process.env.ADMIN_EMAIL || '<EMAIL>',
        RULE_CONTRACT_ID: String(contractId || 0),
        NAME: user.fullName,
        USER_ID: String(userId)
      });
    } catch (error) {
      this.logger.error(`Lỗi khi gửi thông báo cho admin: ${error.message}`, error.stack);
      // Không throw lỗi để không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Gửi thông báo cho người dùng khi admin phê duyệt hoặc từ chối hợp đồng
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   */
  async notifyUser(
    context: RuleContractContext,
    _event: { type: RuleContractEvent; data?: any }
  ): Promise<void> {
    try {
      const { contractId, userId, contractType, contractStatus, rejectionReason } = context;

      // Lấy thông tin người dùng
      if (!userId) {
        this.logger.warn(`ID người dùng không hợp lệ`);
        return;
      }

      const user = await this.userRepository.findById(userId);
      if (!user) {
        this.logger.warn(`Không tìm thấy người dùng với ID ${userId}`);
        return;
      }

      // Gửi email thông báo cho người dùng
      if (contractStatus === ContractStatusEnum.APPROVED) {
        await this.emailService.sendRuleContractSigned({
          EMAIL: user.email,
          RULE_CONTRACT_ID: String(contractId || 0),
          NAME: user.fullName,
          RULE_CONTRACT_SIGNED_DATE: new Date().toISOString()
        });
      } else if (contractStatus === ContractStatusEnum.REJECTED) {
        await this.emailService.sendRuleContractSignFailed({
          EMAIL: user.email,
          RULE_CONTRACT_ID: String(contractId || 0),
          NAME: user.fullName,
          RULE_REASON: rejectionReason || 'Không đáp ứng yêu cầu'
        });
      }
    } catch (error) {
      this.logger.error(`Lỗi khi gửi thông báo cho người dùng: ${error.message}`, error.stack);
      // Không throw lỗi để không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Hoàn tất hợp đồng khi được phê duyệt
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   */
  async finalizeContract(
    context: RuleContractContext,
    _event: { type: RuleContractEvent; data?: any }
  ): Promise<void> {
    try {
      const { userId, contractType } = context;

      // Cập nhật loại người dùng nếu là hợp đồng doanh nghiệp
      if (contractType === ContractTypeEnum.BUSINESS && userId) {
        const user = await this.userRepository.findById(userId);
        if (user) {
          user.type = UserTypeEnum.BUSINESS;
          await this.userRepository.saveUser(user);
        }
      }
    } catch (error) {
      this.logger.error(`Lỗi khi hoàn tất hợp đồng: ${error.message}`, error.stack);
      // Không throw lỗi để không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Kiểm tra chữ ký có hợp lệ không
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns true nếu chữ ký hợp lệ
   */
  isValidSignature(
    _context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): boolean {
    // Kiểm tra chữ ký có hợp lệ không
    // Trong thực tế, bạn có thể kiểm tra định dạng, kích thước, v.v.
    return !!event.data?.signatureData;
  }

  /**
   * Kiểm tra người dùng có phải là admin không
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns true nếu người dùng là admin
   */
  isAdmin(
    _context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): boolean {
    // Kiểm tra người dùng có phải là admin không
    // Trong thực tế, bạn cần kiểm tra quyền của người dùng
    return !!event.data?.adminId;
  }

  /**
   * Kiểm tra có thể nâng cấp lên loại doanh nghiệp không
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns true nếu có thể nâng cấp
   */
  canUpgradeToBusinessType(
    context: RuleContractContext,
    _event: { type: RuleContractEvent; data?: any }
  ): boolean {
    // Kiểm tra có thể nâng cấp lên loại doanh nghiệp không
    return context.contractType === ContractTypeEnum.INDIVIDUAL;
  }

  /**
   * Tạo và lưu hợp đồng cá nhân
   * @param context Ngữ cảnh của máy trạng thái
   * @param event Sự kiện
   * @returns Thông tin hợp đồng đã tạo
   */
  async saveIndividualContract(
    context: RuleContractContext,
    event: { type: RuleContractEvent; data?: any }
  ): Promise<{ contractId: number; contractKey: string; contractBase64: string; contractUrl?: string }> {
    try {
      const { userId } = context;
      const { data } = event;
      const individualData = data?.individualContractData;

      if (!individualData) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_DATA,
          'Dữ liệu hợp đồng cá nhân không hợp lệ',
        );
      }

      // Tạo danh sách vị trí cần chỉnh sửa trên PDF
      const positions = this.createIndividualContractPositions(individualData);

      // Sử dụng ContractHelperService để tạo hợp đồng
      const { contractKey, contractBase64 } = await this.contractHelperService.createIndividualRuleContract(
        userId || 0,
        positions,
      );

      // Tạo hợp đồng mới trong database
      const newContract = this.ruleContractRepository.create({
        userId: userId || 0,
        type: ContractTypeEnum.INDIVIDUAL,
        status: ContractStatusEnum.DRAFT,
        contractUrlPdf: contractKey,
        createdAt: Date.now(),
      });

      // Lưu hợp đồng vào database
      const savedContract = await this.ruleContractRepository.save(newContract);

      // Tạo URL có chữ ký cho file hợp đồng
      const contractUrl = this.cdnService.generateUrlView(
        contractKey,
        TimeIntervalEnum.ONE_HOUR,
      );

      return {
        contractId: savedContract.id,
        contractKey,
        contractBase64,
        contractUrl: contractUrl || '',
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo và lưu hợp đồng cá nhân: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo danh sách vị trí cần chỉnh sửa trên PDF hợp đồng cá nhân
   * @param data Thông tin hợp đồng
   * @returns Danh sách vị trí cần chỉnh sửa
   */
  private createIndividualContractPositions(
    data: IndividualContractData,
  ): PdfPosition[] {
    // Chuyển đổi định dạng ngày tháng
    const formatDate = (dateStr: string) => {
      const date = new Date(dateStr);
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
    };

    const formattedDateOfBirth = formatDate(data.dateOfBirth);
    const formattedIssueDate = formatDate(data.issueDate);

    // Tạo danh sách vị trí
    // Lưu ý: Các vị trí này cần được điều chỉnh theo mẫu hợp đồng thực tế
    const positions: PdfPosition[] = [
      // Thông tin cá nhân
      { pageIndex: 0, text: data.name, xMm: 50, yMm: 50, size: 12 },
      { pageIndex: 0, text: formattedDateOfBirth, xMm: 50, yMm: 60, size: 12 },
      { pageIndex: 0, text: data.cccd, xMm: 50, yMm: 70, size: 12 },
      { pageIndex: 0, text: formattedIssueDate, xMm: 50, yMm: 80, size: 12 },
      { pageIndex: 0, text: data.issuePlace, xMm: 50, yMm: 90, size: 12 },
      { pageIndex: 0, text: data.phone, xMm: 50, yMm: 100, size: 12 },
      { pageIndex: 0, text: data.address, xMm: 50, yMm: 110, size: 12 },
    ];

    // Thêm mã số thuế nếu có
    if (data.taxCode) {
      positions.push({ pageIndex: 0, text: data.taxCode, xMm: 50, yMm: 120, size: 12 });
    }

    // Thêm tên người ký ở cuối hợp đồng
    positions.push({
      pageIndex: 2,
      text: data.name.toUpperCase(),
      xMm: 150,
      yMm: 200,
      size: 14,
    });

    return positions;
  }
}
