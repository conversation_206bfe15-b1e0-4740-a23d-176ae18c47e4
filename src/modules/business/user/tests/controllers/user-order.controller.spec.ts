import { Test, TestingModule } from '@nestjs/testing';
import { UserOrderController } from '../../controllers/user-order.controller';
import { UserOrderService } from '../../services/user-order.service';
import { QueryUserOrderDto, UserOrderSortField } from '../../dto/query-user-order.dto';
import { UserOrderListItemDto, UserOrderResponseDto } from '../../dto/user-order-response.dto';
import { SortDirection } from '@common/dto/query.dto';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

describe('UserOrderController', () => {
  let controller: UserOrderController;
  let service: UserOrderService;

  const mockUserOrderService = {
    findAll: jest.fn(),
    findById: jest.fn(),
  };

  const mockUser: JwtPayload = {
    id: 1001,
    email: '<EMAIL>',
    role: 'user',
  };

  const mockUserOrderListItem: UserOrderListItemDto = {
    id: 1,
    userConvertCustomerId: 101,
    billInfo: {
      total: 350000,
      paymentMethod: 'COD',
    },
    shippingStatus: 'pending',
    createdAt: 1741708800000,
    updatedAt: 1741708800000,
    source: 'website',
  };

  const mockUserOrderResponse: UserOrderResponseDto = {
    id: 1,
    userConvertCustomerId: 101,
    userId: 1001,
    productInfo: [
      {
        id: 1,
        name: 'Áo thun nam',
        quantity: 2,
        price: 150000,
        totalPrice: 300000,
      },
    ],
    billInfo: {
      subtotal: 300000,
      tax: 30000,
      shipping: 20000,
      discount: 0,
      total: 350000,
      paymentMethod: 'COD',
    },
    hasShipping: true,
    shippingStatus: 'pending',
    logisticInfo: {
      address: '123 Đường ABC, Quận 1, TP.HCM',
      receiver: 'Nguyễn Văn A',
      phone: '**********',
      carrier: 'GHN',
      trackingNumber: 'GHN123456789',
    },
    createdAt: 1741708800000,
    updatedAt: 1741708800000,
    source: 'website',
  };

  const mockPaginatedResult = {
    items: [mockUserOrderListItem],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserOrderController],
      providers: [
        {
          provide: UserOrderService,
          useValue: mockUserOrderService,
        },
      ],
    }).compile();

    controller = module.get<UserOrderController>(UserOrderController);
    service = module.get<UserOrderService>(UserOrderService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('nên lấy danh sách đơn hàng thành công', async () => {
      // Arrange
      const queryDto: QueryUserOrderDto = {
        page: 1,
        limit: 10,
        sortBy: UserOrderSortField.CREATED_AT,
        sortDirection: SortDirection.DESC,
      };

      mockUserOrderService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.findAll(queryDto, mockUser);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(mockUser.id, queryDto);
      expect(result.data.items).toHaveLength(1);
      expect(result.data.meta.totalItems).toBe(1);
      expect(result.message).toBe('Lấy danh sách đơn hàng thành công');
    });

    it('nên ném lỗi khi service thất bại', async () => {
      // Arrange
      const queryDto: QueryUserOrderDto = {
        page: 1,
        limit: 10,
        sortBy: UserOrderSortField.CREATED_AT,
        sortDirection: SortDirection.DESC,
      };

      const error = new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        'Lỗi khi lấy danh sách đơn hàng'
      );

      mockUserOrderService.findAll.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.findAll(queryDto, mockUser)).rejects.toThrow(AppException);
      expect(service.findAll).toHaveBeenCalledWith(mockUser.id, queryDto);
    });
  });

  describe('findById', () => {
    it('nên lấy chi tiết đơn hàng thành công', async () => {
      // Arrange
      const orderId = 1;

      mockUserOrderService.findById.mockResolvedValue(mockUserOrderResponse);

      // Act
      const result = await controller.findById(orderId, mockUser);

      // Assert
      expect(service.findById).toHaveBeenCalledWith(orderId, mockUser.id);
      expect(result.data.id).toBe(1);
      expect(result.data.userId).toBe(1001);
      expect(result.message).toBe('Lấy chi tiết đơn hàng thành công');
    });

    it('nên ném lỗi khi không tìm thấy đơn hàng', async () => {
      // Arrange
      const orderId = 999;

      const error = new AppException(
        BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
        'Không tìm thấy đơn hàng với ID 999'
      );

      mockUserOrderService.findById.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.findById(orderId, mockUser)).rejects.toThrow(AppException);
      expect(service.findById).toHaveBeenCalledWith(orderId, mockUser.id);
    });

    it('nên ném lỗi khi người dùng không có quyền truy cập', async () => {
      // Arrange
      const orderId = 1;

      const error = new AppException(
        BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
        'Bạn không có quyền truy cập đơn hàng này'
      );

      mockUserOrderService.findById.mockRejectedValue(error);

      // Act & Assert
      await expect(controller.findById(orderId, mockUser)).rejects.toThrow(AppException);
      expect(service.findById).toHaveBeenCalledWith(orderId, mockUser.id);
    });
  });
});
