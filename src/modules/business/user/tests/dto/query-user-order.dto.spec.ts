import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryUserOrderDto, UserOrderSortField } from '../../dto/query-user-order.dto';
import { SortDirection } from '@common/dto/query.dto';

describe('QueryUserOrderDto', () => {
  it('nên xác thực DTO hợp lệ với các tham số mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.sortBy).toBe(UserOrderSortField.CREATED_AT);
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      page: 2,
      limit: 20,
      userConvertCustomerId: 123,
      shippingStatus: 'shipped',
      source: 'website',
      sortBy: UserOrderSortField.UPDATED_AT,
      sortDirection: SortDirection.ASC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.userConvertCustomerId).toBe(123);
    expect(dto.shippingStatus).toBe('shipped');
    expect(dto.source).toBe('website');
    expect(dto.sortBy).toBe(UserOrderSortField.UPDATED_AT);
    expect(dto.sortDirection).toBe(SortDirection.ASC);
  });

  it('nên xác thực thất bại với page không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      page: 'invalid',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('page');
  });

  it('nên xác thực thất bại với limit không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      limit: 'invalid',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('limit');
  });

  it('nên xác thực thất bại với userConvertCustomerId không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      userConvertCustomerId: 'invalid',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('userConvertCustomerId');
  });

  it('nên xác thực thất bại với sortBy không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      sortBy: 'invalidField',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('sortBy');
  });

  it('nên xác thực thất bại với sortDirection không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      sortDirection: 'invalidDirection',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('sortDirection');
  });

  it('nên chấp nhận các trường tùy chọn là null hoặc undefined', async () => {
    // Arrange
    const dto = plainToInstance(QueryUserOrderDto, {
      userConvertCustomerId: null,
      shippingStatus: undefined,
      source: null,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
