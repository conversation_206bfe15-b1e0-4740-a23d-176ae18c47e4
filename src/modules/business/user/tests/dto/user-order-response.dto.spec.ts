import { plainToInstance } from 'class-transformer';
import { UserOrderResponseDto, UserOrderListItemDto } from '../../dto/user-order-response.dto';

describe('UserOrderResponseDto', () => {
  const mockOrderData = {
    id: 1,
    userConvertCustomerId: 101,
    userId: 1001,
    productInfo: [
      {
        id: 1,
        name: '<PERSON><PERSON> thun nam',
        quantity: 2,
        price: 150000,
        totalPrice: 300000,
      },
    ],
    billInfo: {
      subtotal: 300000,
      tax: 30000,
      shipping: 20000,
      discount: 0,
      total: 350000,
      paymentMethod: 'COD',
    },
    hasShipping: true,
    shippingStatus: 'pending',
    logisticInfo: {
      address: '123 Đường ABC, Quận 1, TP.HCM',
      receiver: 'Nguyễn Văn A',
      phone: '0912345678',
      carrier: 'GHN',
      trackingNumber: 'GHN123456789',
    },
    createdAt: 1741708800000,
    updatedAt: 1741708800000,
    source: 'website',
  };

  it('nên chuyển đổi dữ liệu thành DTO thành công', () => {
    // Act
    const dto = plainToInstance(UserOrderResponseDto, mockOrderData, {
      excludeExtraneousValues: true,
    });

    // Assert
    expect(dto.id).toBe(1);
    expect(dto.userConvertCustomerId).toBe(101);
    expect(dto.userId).toBe(1001);
    expect(dto.productInfo).toEqual(mockOrderData.productInfo);
    expect(dto.billInfo).toEqual(mockOrderData.billInfo);
    expect(dto.hasShipping).toBe(true);
    expect(dto.shippingStatus).toBe('pending');
    expect(dto.logisticInfo).toEqual(mockOrderData.logisticInfo);
    expect(dto.createdAt).toBe(1741708800000);
    expect(dto.updatedAt).toBe(1741708800000);
    expect(dto.source).toBe('website');
  });

  it('nên xử lý các trường null/undefined', () => {
    // Arrange
    const dataWithNulls = {
      id: 1,
      userConvertCustomerId: null,
      userId: 1001,
      productInfo: null,
      billInfo: null,
      hasShipping: false,
      shippingStatus: null,
      logisticInfo: null,
      createdAt: 1741708800000,
      updatedAt: 1741708800000,
      source: null,
    };

    // Act
    const dto = plainToInstance(UserOrderResponseDto, dataWithNulls, {
      excludeExtraneousValues: true,
    });

    // Assert
    expect(dto.id).toBe(1);
    expect(dto.userConvertCustomerId).toBeNull();
    expect(dto.userId).toBe(1001);
    expect(dto.productInfo).toBeNull();
    expect(dto.billInfo).toBeNull();
    expect(dto.hasShipping).toBe(false);
    expect(dto.shippingStatus).toBeNull();
    expect(dto.logisticInfo).toBeNull();
    expect(dto.createdAt).toBe(1741708800000);
    expect(dto.updatedAt).toBe(1741708800000);
    expect(dto.source).toBeNull();
  });
});

describe('UserOrderListItemDto', () => {
  const mockListItemData = {
    id: 1,
    userConvertCustomerId: 101,
    billInfo: {
      total: 350000,
      paymentMethod: 'COD',
    },
    shippingStatus: 'pending',
    createdAt: 1741708800000,
    updatedAt: 1741708800000,
    source: 'website',
  };

  it('nên chuyển đổi dữ liệu thành DTO danh sách thành công', () => {
    // Act
    const dto = plainToInstance(UserOrderListItemDto, mockListItemData, {
      excludeExtraneousValues: true,
    });

    // Assert
    expect(dto.id).toBe(1);
    expect(dto.userConvertCustomerId).toBe(101);
    expect(dto.billInfo).toEqual(mockListItemData.billInfo);
    expect(dto.shippingStatus).toBe('pending');
    expect(dto.createdAt).toBe(1741708800000);
    expect(dto.updatedAt).toBe(1741708800000);
    expect(dto.source).toBe('website');
  });

  it('nên xử lý các trường null trong DTO danh sách', () => {
    // Arrange
    const dataWithNulls = {
      id: 1,
      userConvertCustomerId: null,
      billInfo: null,
      shippingStatus: null,
      createdAt: 1741708800000,
      updatedAt: 1741708800000,
      source: null,
    };

    // Act
    const dto = plainToInstance(UserOrderListItemDto, dataWithNulls, {
      excludeExtraneousValues: true,
    });

    // Assert
    expect(dto.id).toBe(1);
    expect(dto.userConvertCustomerId).toBeNull();
    expect(dto.billInfo).toBeNull();
    expect(dto.shippingStatus).toBeNull();
    expect(dto.createdAt).toBe(1741708800000);
    expect(dto.updatedAt).toBe(1741708800000);
    expect(dto.source).toBeNull();
  });
});
