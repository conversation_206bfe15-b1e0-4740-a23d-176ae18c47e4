import { Test, TestingModule } from '@nestjs/testing';
import { UserFolderService } from '../../services/user-folder.service';
import { FolderRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreateFolderDto, UpdateFolderDto, QueryFolderDto } from '../../dto/folder';
import { Folder } from '@modules/business/entities';
import { JwtPayload } from '@common/interfaces';
import { plainToInstance } from 'class-transformer';
import { FolderResponseDto } from '../../dto/folder/folder-response.dto';

describe('UserFolderService', () => {
  let service: UserFolderService;
  let folderRepository: FolderRepository;
  let validationHelper: ValidationHelper;

  // Mock data
  const mockFolders: Folder[] = [
    {
      id: 1,
      name: 'Th<PERSON> mục 1',
      parentId: null,
      userId: 1,
      path: '/Th<PERSON> mục 1',
      root: 1,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    },
    {
      id: 2,
      name: 'Thư mục 2',
      parentId: 1,
      userId: 1,
      path: '/Thư mục 1/Thư mục 2',
      root: 1,
      createdAt: 1625097700000,
      updatedAt: 1625097700000,
    },
  ];

  const mockUser: JwtPayload = {
    id: 1,
    email: '<EMAIL>',
    role: 'user',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserFolderService,
        {
          provide: FolderRepository,
          useValue: {
            createFolder: jest.fn(),
            findById: jest.fn(),
            findDetailById: jest.fn(),
            findByParentIdAndName: jest.fn(),
            findRootFoldersByUserId: jest.fn(),
            findAll: jest.fn(),
            updateFolder: jest.fn(),
            deleteFolder: jest.fn(),
          },
        },
        {
          provide: ValidationHelper,
          useValue: {
            validateCreateFolder: jest.fn(),
            validateFolderExists: jest.fn(),
            validateUpdateFolder: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserFolderService>(UserFolderService);
    folderRepository = module.get<FolderRepository>(FolderRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createFolder', () => {
    it('nên tạo thư mục mới thành công khi không có parentId', async () => {
      // Arrange
      const createDto: CreateFolderDto = {
        name: 'Thư mục mới',
        root: 1,
      };
      const savedFolder = {
        id: 3,
        name: 'Thư mục mới',
        parentId: null,
        userId: 1,
        path: '/Thư mục mới',
        root: 1,
        createdAt: 1625097800000,
        updatedAt: 1625097800000,
      };

      jest.spyOn(validationHelper, 'validateCreateFolder').mockResolvedValue(undefined);
      jest.spyOn(folderRepository, 'createFolder').mockResolvedValue(savedFolder as Folder);

      // Act
      const result = await service.createFolder(createDto, mockUser);

      // Assert
      expect(validationHelper.validateCreateFolder).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(folderRepository.createFolder).toHaveBeenCalled();
      expect(result).toBeInstanceOf(FolderResponseDto);
      expect(result.id).toBe(3);
      expect(result.name).toBe('Thư mục mới');
      expect(result.parentId).toBeNull();
      expect(result.userId).toBe(1);
      expect(result.path).toBe('/Thư mục mới');
      expect(result.root).toBe(1);
    });

    it('nên tạo thư mục mới thành công khi có parentId', async () => {
      // Arrange
      const createDto: CreateFolderDto = {
        name: 'Thư mục con',
        parentId: 1,
        root: 1,
      };
      const parentFolder = mockFolders[0];
      const savedFolder = {
        id: 3,
        name: 'Thư mục con',
        parentId: 1,
        userId: 1,
        path: '/Thư mục 1/Thư mục con',
        root: 1,
        createdAt: 1625097800000,
        updatedAt: 1625097800000,
      };

      jest.spyOn(validationHelper, 'validateCreateFolder').mockResolvedValue(undefined);
      jest.spyOn(validationHelper, 'validateFolderExists').mockResolvedValue(parentFolder as Folder);
      jest.spyOn(folderRepository, 'createFolder').mockResolvedValue(savedFolder as Folder);

      // Act
      const result = await service.createFolder(createDto, mockUser);

      // Assert
      expect(validationHelper.validateCreateFolder).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(1);
      expect(folderRepository.createFolder).toHaveBeenCalled();
      expect(result).toBeInstanceOf(FolderResponseDto);
      expect(result.id).toBe(3);
      expect(result.name).toBe('Thư mục con');
      expect(result.parentId).toBe(1);
      expect(result.userId).toBe(1);
      expect(result.path).toBe('/Thư mục 1/Thư mục con');
      expect(result.root).toBe(1);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const createDto: CreateFolderDto = {
        name: 'Thư mục mới',
        root: 1,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateCreateFolder').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createFolder(createDto, mockUser)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateFolder).toHaveBeenCalledWith(createDto, mockUser.id);
    });

    it('nên ném lỗi khi tạo thư mục thất bại do ràng buộc khóa ngoại', async () => {
      // Arrange
      const createDto: CreateFolderDto = {
        name: 'Thư mục mới',
        root: 999, // ID kho ảo không tồn tại
      };

      jest.spyOn(validationHelper, 'validateCreateFolder').mockResolvedValue(undefined);
      jest.spyOn(folderRepository, 'createFolder').mockRejectedValue(
        new Error('violates foreign key constraint "folders_virtual_warehouse_warehouse_id_fk"')
      );

      // Act & Assert
      await expect(service.createFolder(createDto, mockUser)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateFolder).toHaveBeenCalledWith(createDto, mockUser.id);
      expect(folderRepository.createFolder).toHaveBeenCalled();
    });
  });

  describe('updateFolder', () => {
    it('nên cập nhật thư mục thành công', async () => {
      // Arrange
      const folderId = 1;
      const updateDto: UpdateFolderDto = {
        name: 'Thư mục đã cập nhật',
      };
      const existingFolder = mockFolders[0];
      const updatedFolder = { ...existingFolder, name: 'Thư mục đã cập nhật', updatedAt: 1625097900000 };

      jest.spyOn(validationHelper, 'validateFolderExists').mockResolvedValue(existingFolder as Folder);
      jest.spyOn(validationHelper, 'validateUpdateFolder').mockResolvedValue(undefined);
      jest.spyOn(folderRepository, 'updateFolder').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      jest.spyOn(folderRepository, 'findById').mockResolvedValue(updatedFolder as Folder);

      // Act
      const result = await service.updateFolder(folderId, updateDto);

      // Assert
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
      expect(validationHelper.validateUpdateFolder).toHaveBeenCalledWith(updateDto, existingFolder);
      expect(folderRepository.updateFolder).toHaveBeenCalledWith(folderId, { name: 'Thư mục đã cập nhật' });
      expect(folderRepository.findById).toHaveBeenCalledWith(folderId);
      expect(result).toBeInstanceOf(FolderResponseDto);
      expect(result.id).toBe(1);
      expect(result.name).toBe('Thư mục đã cập nhật');
    });

    it('nên ném lỗi khi thư mục không tồn tại', async () => {
      // Arrange
      const folderId = 999;
      const updateDto: UpdateFolderDto = {
        name: 'Thư mục đã cập nhật',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND, 'Thư mục không tồn tại');

      jest.spyOn(validationHelper, 'validateFolderExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateFolder(folderId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const folderId = 1;
      const updateDto: UpdateFolderDto = {
        name: 'Thư mục đã cập nhật',
      };
      const existingFolder = mockFolders[0];
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateFolderExists').mockResolvedValue(existingFolder as Folder);
      jest.spyOn(validationHelper, 'validateUpdateFolder').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateFolder(folderId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
      expect(validationHelper.validateUpdateFolder).toHaveBeenCalledWith(updateDto, existingFolder);
    });
  });

  describe('getFolderById', () => {
    it('nên lấy thông tin thư mục theo ID thành công', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(validationHelper, 'validateFolderExists').mockResolvedValue(mockFolders[0] as Folder);

      // Act
      const result = await service.getFolderById(folderId);

      // Assert
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
      expect(result).toBeInstanceOf(FolderResponseDto);
      expect(result.id).toBe(1);
      expect(result.name).toBe('Thư mục 1');
    });

    it('nên ném lỗi khi thư mục không tồn tại', async () => {
      // Arrange
      const folderId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND, 'Thư mục không tồn tại');

      jest.spyOn(validationHelper, 'validateFolderExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.getFolderById(folderId)).rejects.toThrow(AppException);
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
    });
  });

  describe('getFolderDetailById', () => {
    it('nên lấy thông tin chi tiết thư mục theo ID thành công', async () => {
      // Arrange
      const folderId = 1;
      const folderDetail = {
        ...mockFolders[0],
        parentFolder: null,
        user: {
          id: 1,
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
        },
        virtualWarehouse: {
          warehouseId: 1,
          associatedSystem: 'System A',
          purpose: 'Purpose A',
        },
      };

      jest.spyOn(folderRepository, 'findDetailById').mockResolvedValue(folderDetail);

      // Act
      const result = await service.getFolderDetailById(folderId);

      // Assert
      expect(folderRepository.findDetailById).toHaveBeenCalledWith(folderId);
      expect(result).toEqual(folderDetail);
    });

    it('nên ném lỗi khi lấy thông tin chi tiết thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(folderRepository, 'findDetailById').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getFolderDetailById(folderId)).rejects.toThrow(AppException);
      expect(folderRepository.findDetailById).toHaveBeenCalledWith(folderId);
    });
  });

  describe('getFolders', () => {
    it('nên lấy danh sách thư mục với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryFolderDto = {
        page: 1,
        limit: 10,
        userId: 1,
      };
      const paginatedResult = {
        items: mockFolders,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(folderRepository, 'findAll').mockResolvedValue(paginatedResult);

      // Act
      const result = await service.getFolders(queryDto);

      // Assert
      expect(folderRepository.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.items.length).toBe(2);
      expect(result.items[0]).toBeInstanceOf(FolderResponseDto);
      expect(result.items[0].id).toBe(1);
      expect(result.items[0].name).toBe('Thư mục 1');
      expect(result.meta.totalItems).toBe(2);
    });

    it('nên ném lỗi khi lấy danh sách thư mục thất bại', async () => {
      // Arrange
      const queryDto: QueryFolderDto = {
        page: 1,
        limit: 10,
      };

      jest.spyOn(folderRepository, 'findAll').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getFolders(queryDto)).rejects.toThrow(AppException);
      expect(folderRepository.findAll).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('getRootFolders', () => {
    it('nên lấy danh sách thư mục gốc thành công', async () => {
      // Arrange
      jest.spyOn(folderRepository, 'findRootFoldersByUserId').mockResolvedValue([mockFolders[0]]);

      // Act
      const result = await service.getRootFolders(mockUser);

      // Assert
      expect(folderRepository.findRootFoldersByUserId).toHaveBeenCalledWith(mockUser.id);
      expect(result.length).toBe(1);
      expect(result[0]).toBeInstanceOf(FolderResponseDto);
      expect(result[0].id).toBe(1);
      expect(result[0].name).toBe('Thư mục 1');
      expect(result[0].parentId).toBeNull();
    });

    it('nên ném lỗi khi lấy danh sách thư mục gốc thất bại', async () => {
      // Arrange
      jest.spyOn(folderRepository, 'findRootFoldersByUserId').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getRootFolders(mockUser)).rejects.toThrow(AppException);
      expect(folderRepository.findRootFoldersByUserId).toHaveBeenCalledWith(mockUser.id);
    });
  });

  describe('deleteFolder', () => {
    it('nên xóa thư mục thành công', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(validationHelper, 'validateFolderExists').mockResolvedValue(mockFolders[0] as Folder);
      jest.spyOn(folderRepository, 'deleteFolder').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

      // Act
      await service.deleteFolder(folderId);

      // Assert
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
      expect(folderRepository.deleteFolder).toHaveBeenCalledWith(folderId);
    });

    it('nên ném lỗi khi thư mục không tồn tại', async () => {
      // Arrange
      const folderId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND, 'Thư mục không tồn tại');

      jest.spyOn(validationHelper, 'validateFolderExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.deleteFolder(folderId)).rejects.toThrow(AppException);
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
    });

    it('nên ném lỗi khi xóa thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(validationHelper, 'validateFolderExists').mockResolvedValue(mockFolders[0] as Folder);
      jest.spyOn(folderRepository, 'deleteFolder').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.deleteFolder(folderId)).rejects.toThrow(AppException);
      expect(validationHelper.validateFolderExists).toHaveBeenCalledWith(folderId);
      expect(folderRepository.deleteFolder).toHaveBeenCalledWith(folderId);
    });
  });
});
