import { Test, TestingModule } from '@nestjs/testing';
import { UserOrderService } from '../../services/user-order.service';
import { UserOrderRepository } from '@modules/business/repositories';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { QueryUserOrderDto, UserOrderSortField } from '../../dto/query-user-order.dto';
import { SortDirection } from '@common/dto/query.dto';
import { UserOrder } from '@modules/business/entities';

describe('UserOrderService', () => {
  let service: UserOrderService;
  let repository: UserOrderRepository;

  const mockUserOrderRepository = {
    findAll: jest.fn(),
    findById: jest.fn(),
  };

  const mockUserOrder: UserOrder = {
    id: 1,
    userConvertCustomerId: 101,
    userId: 1001,
    productInfo: [
      {
        id: 1,
        name: '<PERSON><PERSON> thun nam',
        quantity: 2,
        price: 150000,
        totalPrice: 300000,
      },
    ],
    billInfo: {
      subtotal: 300000,
      tax: 30000,
      shipping: 20000,
      discount: 0,
      total: 350000,
      paymentMethod: 'COD',
    },
    hasShipping: true,
    shippingStatus: 'pending',
    logisticInfo: {
      address: '123 Đường ABC, Quận 1, TP.HCM',
      receiver: 'Nguyễn Văn A',
      phone: '**********',
      carrier: 'GHN',
      trackingNumber: 'GHN123456789',
    },
    createdAt: 1741708800000,
    updatedAt: 1741708800000,
    source: 'website',
  };

  const mockPaginatedResult = {
    items: [mockUserOrder],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserOrderService,
        {
          provide: UserOrderRepository,
          useValue: mockUserOrderRepository,
        },
      ],
    }).compile();

    service = module.get<UserOrderService>(UserOrderService);
    repository = module.get<UserOrderRepository>(UserOrderRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('nên lấy danh sách đơn hàng thành công', async () => {
      // Arrange
      const userId = 1001;
      const queryDto: QueryUserOrderDto = {
        page: 1,
        limit: 10,
        sortBy: UserOrderSortField.CREATED_AT,
        sortDirection: SortDirection.DESC,
      };

      mockUserOrderRepository.findAll.mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await service.findAll(userId, queryDto);

      // Assert
      expect(repository.findAll).toHaveBeenCalledWith(userId, queryDto);
      expect(result.items).toHaveLength(1);
      expect(result.meta.totalItems).toBe(1);
    });

    it('nên ném lỗi khi repository thất bại', async () => {
      // Arrange
      const userId = 1001;
      const queryDto: QueryUserOrderDto = {
        page: 1,
        limit: 10,
        sortBy: UserOrderSortField.CREATED_AT,
        sortDirection: SortDirection.DESC,
      };

      mockUserOrderRepository.findAll.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.findAll(userId, queryDto)).rejects.toThrow(AppException);
      expect(repository.findAll).toHaveBeenCalledWith(userId, queryDto);
    });
  });

  describe('findById', () => {
    it('nên lấy chi tiết đơn hàng thành công', async () => {
      // Arrange
      const orderId = 1;
      const userId = 1001;

      mockUserOrderRepository.findById.mockResolvedValue(mockUserOrder);

      // Act
      const result = await service.findById(orderId, userId);

      // Assert
      expect(repository.findById).toHaveBeenCalledWith(orderId);
      expect(result.id).toBe(1);
      expect(result.userId).toBe(1001);
    });

    it('nên ném lỗi khi không tìm thấy đơn hàng', async () => {
      // Arrange
      const orderId = 999;
      const userId = 1001;

      mockUserOrderRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findById(orderId, userId)).rejects.toThrow(AppException);
      expect(repository.findById).toHaveBeenCalledWith(orderId);
    });

    it('nên ném lỗi khi người dùng không có quyền truy cập', async () => {
      // Arrange
      const orderId = 1;
      const userId = 2002; // Different user ID

      mockUserOrderRepository.findById.mockResolvedValue(mockUserOrder);

      // Act & Assert
      await expect(service.findById(orderId, userId)).rejects.toThrow(AppException);
      expect(repository.findById).toHaveBeenCalledWith(orderId);
    });

    it('nên ném lỗi khi repository thất bại', async () => {
      // Arrange
      const orderId = 1;
      const userId = 1001;

      mockUserOrderRepository.findById.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.findById(orderId, userId)).rejects.toThrow(AppException);
      expect(repository.findById).toHaveBeenCalledWith(orderId);
    });
  });
});
