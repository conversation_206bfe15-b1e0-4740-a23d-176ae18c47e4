import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

/**
 * DTO cho tạo trường tùy chỉnh
 */
export class CreateCustomFieldDto {
  /**
   * Thành phần UI
   * @example "Text Input"
   */
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
  })
  @IsNotEmpty({ message: 'Thành phần UI không được để trống' })
  @IsString({ message: 'Thành phần UI phải là chuỗi' })
  component: string;

  /**
   * ID cấu hình (bắt buộc nhập)
   * @example "custom-text-001"
   */
  @ApiProperty({
    description: 'ID cấu hình (bắt buộc nhập)',
    example: 'custom-text-001',
    required: true
  })
  @IsNotEmpty({ message: 'ID cấu hình không được để trống' })
  @IsString({ message: 'ID cấu hình phải là chuỗi' })
  configId: string;

  /**
   * Nhãn hiển thị
   * @example "Số điện thoại"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Số điện thoại',
  })
  @IsNotEmpty({ message: 'Nhãn hiển thị không được để trống' })
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  @MaxLength(255, { message: 'Nhãn hiển thị không được vượt quá 255 ký tự' })
  label: string;

  /**
   * Loại trường
   * @example "text"
   */
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsNotEmpty({ message: 'Loại trường không được để trống' })
  @IsString({ message: 'Loại trường phải là chuỗi' })
  type: string;

  /**
   * Trường bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsBoolean({ message: 'Trường bắt buộc phải là boolean' })
  required: boolean;

  /**
   * Cấu hình JSON
   * @example { "validation": { "pattern": "^[0-9]{10}$" }, "placeholder": "Nhập số điện thoại", "variant": "outlined", "size": "small" }
   */
  @ApiProperty({
    description: 'Cấu hình JSON',
    example: {
      validation: { pattern: '^[0-9]{10}$' },
      placeholder: 'Nhập số điện thoại',
      variant: 'outlined',
      size: 'small',
    },
  })
  @IsObject({ message: 'Cấu hình JSON phải là đối tượng' })
  @IsOptional()
  configJson: any = {};

  /**
   * ID nhân viên tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID nhân viên tạo phải là số' })
  employeeId?: number;

  /**
   * ID người dùng tạo
   * @example 1001
   */
  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1001,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng tạo phải là số' })
  userId?: number;

  /**
   * ID nhóm trường tùy chỉnh
   * @example 1
   */
  @ApiProperty({
    description: 'ID nhóm trường tùy chỉnh',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID nhóm trường tùy chỉnh phải là số' })
  formGroupId?: number;

  /**
   * Cấu hình grid
   * @example { "i": "field1", "x": 0, "y": 0, "w": 6, "h": 2 }
   */
  @IsOptional()
  @IsObject({ message: 'Cấu hình grid phải là đối tượng' })
  grid?: any = { i: 'default', x: 0, y: 0, w: 6, h: 2 };

  /**
   * Giá trị mặc định
   * @example { "value": "example" }
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: { value: 'example' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Giá trị mặc định phải là đối tượng' })
  value?: { value: string } = { value: '' };
}
