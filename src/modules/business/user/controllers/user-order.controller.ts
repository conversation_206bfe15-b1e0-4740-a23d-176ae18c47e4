import { Controller, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserOrderService } from '../services/user-order.service';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { QueryUserOrderDto, UserOrderListItemDto, UserOrderResponseDto } from '../dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * <PERSON> <PERSON><PERSON> lý các request liên quan đến đơn hàng của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_ORDER)
@Controller('user/user-orders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, UserOrderResponseDto, UserOrderListItemDto, PaginatedResult)
export class UserOrderController {
  constructor(private readonly userOrderService: UserOrderService) {}

  /**
   * Lấy danh sách đơn hàng của người dùng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param user Thông tin người dùng từ JWT
   * @returns Danh sách đơn hàng với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng',
    description: 'Lấy danh sách đơn hàng của người dùng với phân trang, tìm kiếm và lọc'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách đơn hàng',
    schema: ApiResponseDto.getPaginatedSchema(UserOrderListItemDto),
  })
  @ApiErrorResponse(BUSINESS_ERROR_CODES.ORDER_FIND_FAILED)
  async findAll(
    @Query() queryDto: QueryUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<UserOrderListItemDto>>> {
    const orders = await this.userOrderService.findAll(user.id, queryDto);
    return ApiResponseDto.success(orders, 'Lấy danh sách đơn hàng thành công');
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param user Thông tin người dùng từ JWT
   * @returns Chi tiết đơn hàng
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết đơn hàng',
    description: 'Lấy thông tin chi tiết của một đơn hàng cụ thể'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của đơn hàng',
    type: Number,
    example: 1
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết đơn hàng',
    schema: ApiResponseDto.getSchema(UserOrderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED
  )
  async findById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    const order = await this.userOrderService.findById(id, user.id);
    return ApiResponseDto.success(order, 'Lấy chi tiết đơn hàng thành công');
  }
}
