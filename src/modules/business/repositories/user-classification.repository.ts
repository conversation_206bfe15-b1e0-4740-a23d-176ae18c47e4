import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm'; // SelectQueryBuilder from admin
import { UserClassification, CustomFieldClassification } from '@modules/business/entities'; // CustomFieldClassification from user

/**
 * Repository xử lý truy vấn dữ liệu cho entity UserClassification,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class UserClassificationRepository extends Repository<UserClassification> {
  // Using user's logger style (private readonly) as it's a common NestJS pattern.
  // If admin's 'protected' was intentional for subclassing, this might need adjustment.
  private readonly logger = new Logger(UserClassificationRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserClassification, dataSource.createEntityManager());
  }

  // --- Admin specific or unique methods ---

  /**
   * Tạo query builder c<PERSON> bản cho UserClassification (Admin context)
   * @returns SelectQueryBuilder<UserClassification>
   */
  protected createBaseQuery_admin(): SelectQueryBuilder<UserClassification> {
    // Renamed to avoid potential conflict if user context ever adds one,
    // and to clearly indicate its origin.
    // Kept as protected as in the original admin file.
    return this.createQueryBuilder('userClassification');
  }

  // --- User specific or unique methods ---

  /**
   * Tìm phân loại theo ID (User context)
   * @param id ID của phân loại
   * @returns Phân loại hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<UserClassification | null> {
    try {
      return await this.findOne({ where: { id } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm phân loại theo ID ${id}: ${error.message}`);
      throw new Error(`Lỗi khi tìm phân loại theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm phân loại theo ID sản phẩm và ID phân loại (User context)
   * @param productId ID của sản phẩm
   * @param id ID của phân loại
   * @returns Phân loại hoặc null nếu không tìm thấy
   */
  async findByProductIdAndId(productId: number, id: number): Promise<UserClassification | null> {
    try {
      return await this.findOne({ where: { productId, id } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm phân loại theo productId ${productId} và id ${id}: ${error.message}`);
      throw new Error(`Lỗi khi tìm phân loại theo productId ${productId} và id ${id}: ${error.message}`);
    }
  }

  // --- Methods with same name, differentiated by suffix ---

  /**
   * Tìm phân loại theo ID sản phẩm (User context version)
   * @param productId ID của sản phẩm
   * @returns Danh sách phân loại
   */
  async findByProductId_user(productId: number): Promise<UserClassification[]> {
    try {
      return await this.find({ where: { productId } });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm phân loại theo productId ${productId} (user): ${error.message}`);
      throw new Error(`Lỗi khi tìm phân loại theo productId ${productId} (user): ${error.message}`);
    }
  }

  /**
   * Tìm phân loại sản phẩm theo ID sản phẩm (Admin context version)
   * @param productId ID của sản phẩm
   * @returns Danh sách phân loại sản phẩm
   */
  async findByProductId_admin(productId: number): Promise<UserClassification[]> {
    this.logger.log(`(Admin) Tìm phân loại sản phẩm với productId: ${productId}`);
    try {
      const classifications = await this.find({
        where: { productId }
      });

      this.logger.log(`(Admin) Đã tìm thấy ${classifications.length} phân loại cho sản phẩm ${productId}`);
      return classifications;
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi tìm phân loại sản phẩm với productId ${productId}: ${error.message}`, error.stack);
      throw error; // Admin version re-throws the original error
    }
  }
}