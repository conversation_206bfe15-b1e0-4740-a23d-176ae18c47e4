import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CustomFieldConfigDto } from './custom-field-config.dto';

/**
 * DTO cho việc cập nhật trường tùy chỉnh
 */
export class UpdateCustomFieldDto {
  @ApiPropertyOptional({
    description: 'Thành phần UI',
    example: 'input',
  })
  @IsOptional()
  @IsString()
  component?: string;

  @ApiPropertyOptional({
    description: 'ID cấu hình (phải là unique)',
    example: 'product_color',
  })
  @IsOptional()
  @IsString()
  configId?: string;

  @ApiPropertyOptional({
    description: 'Nhãn hiển thị',
    example: 'Màu sắc sản phẩm',
  })
  @IsOptional()
  @IsString()
  label?: string;

  @ApiPropertyOptional({
    description: 'Loại trường',
    example: 'text',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Trường bắt buộc hay không',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Cấu hình chi tiết',
    type: () => CustomFieldConfigDto,
  })
  @IsOptional()
  configJson?: any; // Sử dụng any để hỗ trợ nhiều kiểu dữ liệu

  constructor(partial: Partial<UpdateCustomFieldDto>) {
    Object.assign(this, partial);
  }
}
