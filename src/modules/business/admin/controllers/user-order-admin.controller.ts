import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserOrderAdminService } from '@modules/business/admin/services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryUserOrderDto,
  UserOrderResponseDto,
  UserOrderDetailResponseDto
} from '../dto';
import { ApiErrorResponse, ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { JWTPayload } from '@modules/auth/interfaces';

/**
 * Controller xử lý các API liên quan đến đơn hàng của người dùng cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS)
@ApiExtraModels(
  ApiResponseDto,
  UserOrderResponseDto,
  UserOrderDetailResponseDto,
  QueryUserOrderDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/user-orders')
export class UserOrderAdminController {
  constructor(private readonly userOrderAdminService: UserOrderAdminService) {}

  /**
   * Lấy danh sách đơn hàng với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employee Thông tin nhân viên từ JWT
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách đơn hàng phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng',
    description: 'Lấy danh sách đơn hàng với phân trang, tìm kiếm, lọc và sắp xếp',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách đơn hàng',
    schema: ApiResponseDto.getPaginatedSchema(UserOrderResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async findAll(
    @CurrentEmployee() employee: JWTPayload,
    @Query() queryDto: QueryUserOrderDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserOrderResponseDto>>> {
    const result = await this.userOrderAdminService.getUserOrders(employee.id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách đơn hàng thành công');
  }

  /**
   * Lấy thông tin chi tiết đơn hàng theo ID
   * @param employee Thông tin nhân viên từ JWT
   * @param orderId ID của đơn hàng
   * @returns Thông tin chi tiết đơn hàng
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết đơn hàng theo ID',
    description: 'Lấy thông tin chi tiết của một đơn hàng cụ thể bao gồm thông tin khách hàng',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của đơn hàng',
    example: 1,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin chi tiết đơn hàng',
    schema: ApiResponseDto.getSchema(UserOrderDetailResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_ORDER_FETCH_ERROR,
  )
  async findById(
    @CurrentEmployee() employee: JWTPayload,
    @Param('id', ParseIntPipe) orderId: number,
  ): Promise<ApiResponseDto<UserOrderDetailResponseDto>> {
    const result = await this.userOrderAdminService.getUserOrderById(employee.id, orderId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết đơn hàng thành công');
  }
}
