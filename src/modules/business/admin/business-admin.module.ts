import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  UserProduct,
  UserClassification,
  CustomFieldClassification,
  CustomField,
  UserOrder,
  Inventory,
  UserConvert,
  UserConvertCustomer,
  CustomGroupForm,
  CustomGroupFormField,
  Warehouse,
  PhysicalWarehouse,
  VirtualWarehouse,
  WarehouseCustomField,
  File,
  Folder
} from '../entities';

import {
  ProductAdminController,
  CustomFieldAdminController,
  UserConvertAdminController,
  UserConvertCustomerAdminController,
  UserOrderAdminController,
  AdminWarehouseController,
  AdminWarehouseCustomFieldController,
  AdminVirtualWarehouseController,
  AdminPhysicalWarehouseController,
  AdminFileController,
  AdminFolderController
} from './controllers';

import {
  ProductAdminService,
  CustomFieldAdminService,
  UserConvertAdminService,
  UserConvertCustomerAdminService,
  UserOrderAdminService,
  AdminWarehouseService,
  AdminWarehouseCustomFieldService,
  AdminVirtualWarehouseService,
  AdminPhysicalWarehouseService,
  AdminFileService,
  AdminFolderService
} from './services';

import { ValidationHelper, WarehouseValidationHelper, FileValidationHelper, FolderValidationHelper, FileHelper, FolderHelper } from './helpers';
import {
  UserProductAdminRepository,
  CustomFieldRepository,
  CustomGroupFormRepository,
  CustomGroupFormFieldRepository,
  UserClassificationRepository,
  CustomFieldClassificationRepository,
  UserConvertRepository,
  UserConvertCustomerRepository,
  UserOrderRepository,
  WarehouseRepository,
  PhysicalWarehouseRepository,
  VirtualWarehouseRepository,
  WarehouseCustomFieldRepository,
  FileRepository,
  FolderRepository
} from '../repositories';

/**
 * Module quản lý chức năng business cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProduct,
      UserClassification,
      CustomFieldClassification,
      CustomField,
      UserOrder,
      Inventory,
      UserConvert,
      UserConvertCustomer,
      CustomGroupForm,
      CustomGroupFormField,
      Warehouse,
      PhysicalWarehouse,
      VirtualWarehouse,
      WarehouseCustomField,
      File,
      Folder
    ])
  ],
  controllers: [
    ProductAdminController,
    CustomFieldAdminController,
    UserConvertAdminController,
    UserConvertCustomerAdminController,
    UserOrderAdminController,
    AdminWarehouseController,
    AdminWarehouseCustomFieldController,
    AdminVirtualWarehouseController,
    AdminPhysicalWarehouseController,
    AdminFileController,
    AdminFolderController
  ],
  providers: [
    // Helpers
    ValidationHelper,
    WarehouseValidationHelper,
    FileValidationHelper,
    FolderValidationHelper,
    FileHelper,
    FolderHelper,

    // Services
    ProductAdminService,
    CustomFieldAdminService,
    UserConvertAdminService,
    UserConvertCustomerAdminService,
    UserOrderAdminService,
    AdminWarehouseService,
    AdminWarehouseCustomFieldService,
    AdminVirtualWarehouseService,
    AdminPhysicalWarehouseService,
    AdminFileService,
    AdminFolderService,

    // Repositories
    UserProductAdminRepository,
    CustomFieldRepository,
    CustomGroupFormRepository,
    CustomGroupFormFieldRepository,
    UserClassificationRepository,
    CustomFieldClassificationRepository,
    UserConvertRepository,
    UserConvertCustomerRepository,
    UserOrderRepository,
    WarehouseRepository,
    PhysicalWarehouseRepository,
    VirtualWarehouseRepository,
    WarehouseCustomFieldRepository,
    FileRepository,
    FolderRepository,

    // Helpers
    ValidationHelper
  ],
  exports: [
    TypeOrmModule,
    ProductAdminService,
    CustomFieldAdminService,
    UserConvertAdminService,
    UserConvertCustomerAdminService,
    UserOrderAdminService,
    AdminWarehouseService,
    AdminWarehouseCustomFieldService,
    AdminVirtualWarehouseService,
    AdminPhysicalWarehouseService,
    AdminFileService,
    AdminFolderService
  ],
})
export class BusinessAdminModule {}
