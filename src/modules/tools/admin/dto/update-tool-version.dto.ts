import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsObject, IsOptional, IsString, Matches, MaxLength } from 'class-validator';
import { ToolStatusEnum } from '@/modules/tools/constants';

/**
 * DTO cho việc cập nhật phiên bản tool
 */
export class UpdateToolVersionDto {
  @ApiProperty({
    description: 'Tên tool trong định nghĩa code (chỉ chứa a-z, A-Z, 0-9, _)',
    example: 'searchToolV2',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Tên tool chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
  })
  @MaxLength(64)
  toolName?: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về chức năng của tool',
    example: 'Tìm kiếm thông tin từ nhiều nguồn dữ liệu với bộ lọc nâng cao',
    required: false,
  })
  @IsString()
  @IsOptional()
  toolDescription?: string;

  @ApiProperty({
    description: 'Tham số của tool',
    example: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Câu truy vấn tìm kiếm',
        },
        filters: {
          type: 'object',
          description: 'Các bộ lọc',
        },
      },
      required: ['query'],
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  parameters?: Record<string, any>;

  @ApiProperty({
    description: 'Mô tả những thay đổi so với phiên bản trước',
    example: 'Thêm tham số lọc theo ngày và loại tài liệu',
    required: false,
  })
  @IsString()
  @IsOptional()
  changeDescription?: string;

  @ApiProperty({
    description: 'Trạng thái của phiên bản',
    enum: ToolStatusEnum,
    required: false,
  })
  @IsEnum(ToolStatusEnum)
  @IsOptional()
  status?: ToolStatusEnum;
}
