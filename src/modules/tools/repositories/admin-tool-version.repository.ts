import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminToolVersion } from '../entities/admin-tool-version.entity';
import { FunctionStatusEnum } from '../constants/tools-status.enum';

@Injectable()
export class AdminToolVersionRepository extends Repository<AdminToolVersion> {
  private readonly logger = new Logger(AdminToolVersionRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminToolVersion, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho admin tool version
   * @returns SelectQueryBuilder<AdminToolVersion> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<AdminToolVersion> {
    return this.createQueryBuilder('version');
  }

  /**
   * Tìm phiên bản tool theo ID
   * @param id ID của phiên bản tool cần tìm
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findVersionById(id: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm phiên bản tool theo ID tool và tên phiên bản
   * @param functionId ID của tool
   * @param versionName Tên phiên bản
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findVersionByFunctionIdAndVersionName(
    functionId: string,
    versionName: string,
  ): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .andWhere('version.versionName = :versionName', { versionName })
      .getOne();
  }

  /**
   * Tìm phiên bản tool theo ID tool và số phiên bản (deprecated)
   * @param functionId ID của tool
   * @param versionNumber Số phiên bản
   * @returns Phiên bản tool nếu tìm thấy, null nếu không tìm thấy
   * @deprecated Sử dụng findVersionByFunctionIdAndVersionName thay thế
   */
  async findVersionByFunctionIdAndVersionNumber(
    functionId: string,
    versionNumber: number,
  ): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .andWhere('version.versionNumber = :versionNumber', { versionNumber })
      .getOne();
  }

  /**
   * Lấy phiên bản mới nhất của tool
   * @param functionId ID của tool
   * @returns Phiên bản mới nhất của tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findLatestVersionByFunctionId(functionId: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Lấy danh sách phiên bản của tool
   * @param functionId ID của tool
   * @returns Danh sách phiên bản của tool
   */
  async findVersionsByToolId(functionId: string): Promise<AdminToolVersion[]> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy tên phiên bản mới nhất của tool
   * @param functionId ID của tool
   * @returns Tên phiên bản mới nhất của tool
   */
  async getLatestVersionName(functionId: string): Promise<string> {
    const latestVersion = await this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();

    return latestVersion ? latestVersion.versionName : 'v1.0.0';
  }

  /**
   * Lấy số phiên bản mới nhất của tool (deprecated)
   * @param functionId ID của tool
   * @returns Số phiên bản mới nhất của tool
   * @deprecated Sử dụng getLatestVersionName thay thế
   */
  async getLatestVersionNumber(functionId: string): Promise<number> {
    const latestVersion = await this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.versionNumber', 'DESC')
      .getOne();

    return latestVersion ? latestVersion.versionNumber : 0;
  }

  /**
   * Kiểm tra xem tên hàm đã tồn tại trong phiên bản khác chưa
   * @param toolName Tên hàm cần kiểm tra
   * @param excludeVersionId ID của phiên bản cần loại trừ khi kiểm tra (dùng khi update)
   * @returns true nếu tên hàm đã tồn tại, false nếu chưa tồn tại
   */
  async isToolNameExists(
    toolName: string,
    excludeVersionId?: string,
  ): Promise<boolean> {
    const qb = this.createQueryBuilder('version')
      .where('version.toolName = :toolName', { toolName });

    if (excludeVersionId) {
      qb.andWhere('version.id != :excludeVersionId', { excludeVersionId });
    }

    const count = await qb.getCount();
    return count > 0;
  }

  /**
   * Lấy phiên bản được phê duyệt mới nhất của tool
   * @param functionId ID của tool
   * @returns Phiên bản được phê duyệt mới nhất của tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findLatestApprovedVersionByFunctionId(functionId: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .andWhere('version.status = :status', { status: FunctionStatusEnum.APPROVED })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }

  /**
   * Lấy phiên bản mới nhất dựa trên createdAt thay vì versionNumber
   * @param functionId ID của tool
   * @returns Phiên bản mới nhất
   */
  async findLatestVersionByCreatedAt(functionId: string): Promise<AdminToolVersion | null> {
    return this.createBaseQuery()
      .where('version.toolId = :functionId', { functionId })
      .orderBy('version.createdAt', 'DESC')
      .getOne();
  }
}
