import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { ToolStatusEnum } from '../constants/tool-status.enum';

/**
 * Entity đại diện cho bảng admin_tool_versions trong cơ sở dữ liệu
 * Bảng lưu trữ các phiên bản của tool do admin tạo hoặc cập nhật
 */
@Entity('admin_tool_versions')
@Unique('unique_tool_version', ['toolId', 'versionName'])
export class AdminToolVersion {
  /**
   * ID duy nhất của phiên bản, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * ID của tool gốc, tham chiếu đến bảng admin_tools
   */
  @Column({ name: 'tool_id', type: 'uuid' })
  toolId: string;

  /**
   * Tên định danh cho phiên bản, d<PERSON> nhớ hơn số (ví dụ: "v1.0", "v2.0-beta")
   */
  @Column({ name: 'version_name', length: 50 })
  versionName: string;

  /**
   * Số phiên bản, được sử dụng để sắp xếp (ví dụ: 1, 2, 3)
   * @deprecated Sử dụng versionName thay thế
   */
  @Column({ name: 'version_number', type: 'integer', nullable: true })
  versionNumber: number;

  /**
   * Mô tả những thay đổi so với phiên bản trước
   */
  @Column({ name: 'change_description', type: 'text', nullable: true })
  changeDescription: string | null;

  /**
   * ID của nhân viên tạo phiên bản
   */
  @Column({ name: 'created_by', type: 'integer' })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật phiên bản gần nhất
   */
  @Column({ name: 'updates_by', type: 'integer' })
  updatesBy: number;

  /**
   * Thời điểm tạo phiên bản, tính bằng millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối, tính bằng millisecond
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Trạng thái của phiên bản: DRAFT, APPROVED, DEPRECATED
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.DRAFT
  })
  status: ToolStatusEnum;

  /**
   * Tên của hàm trong định nghĩa code
   */
  @Column({ name: 'tool_name', length: 64 })
  toolName: string;

  /**
   * Mô tả chi tiết về chức năng của hàm
   */
  @Column({ name: 'tool_description', type: 'text', nullable: true })
  toolDescription: string | null;

  /**
   * Tham số của hàm, định dạng JSONB
   */
  @Column({ name: 'parameters', type: 'jsonb', default: '{}' })
  parameters: any;
}
