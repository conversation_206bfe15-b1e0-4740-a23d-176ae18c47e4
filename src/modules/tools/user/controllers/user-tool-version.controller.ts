import {
  Controller,
  Get,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserToolVersionService } from '../services';
import { UserToolVersionDto } from '../dto';
import { ApiResponseDto } from '@/common/response';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { SWAGGER_API_TAGS } from '@common/swagger';

@ApiTags(SWAGGER_API_TAGS.USER_VERSION_TOOL)
@Controller('user/tools/:toolId/versions')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserToolVersionController {
  constructor(private readonly userToolVersionService: UserToolVersionService) {}

  @Get(':versionId')
  @ApiOperation({ summary: 'L<PERSON><PERSON> thông tin chi tiết phiên bản của người dùng' })
  @ApiParam({ name: 'toolId', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết phiên bản của người dùng',
    type: () => ApiResponseDto.success(UserToolVersionDto),
  })
  async getVersionById(
    @Param('versionId') versionId: string,
    @CurrentUser('id') userId: number,
  ) {
    const version = await this.userToolVersionService.getVersionById(versionId, userId);
    return ApiResponseDto.success(version);
  }

  @Post(':versionId/set-default')
  @ApiOperation({ summary: 'Đặt phiên bản làm mặc định' })
  @ApiParam({ name: 'toolId', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Phiên bản đã được đặt làm mặc định',
    type: ApiResponseDto,
  })
  async setDefaultVersion(
    @Param('toolId') toolId: string,
    @Param('versionId') versionId: string,
    @CurrentUser('id') userId: number,
  ) {
    await this.userToolVersionService.setDefaultVersion(
      toolId,
      versionId,
      userId,
    );
    return ApiResponseDto.success({ message: 'Phiên bản đã được đặt làm mặc định' });
  }
}
