import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { UserToolVersion } from '../../entities';
import {
  UserToolRepository,
  UserToolVersionRepository,
} from '../../repositories';
import { UserToolVersionDto } from '../dto';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { Transactional } from 'typeorm-transactional';
import { ToolStatusEnum } from '../../constants/tool-status.enum';

@Injectable()
export class UserToolVersionService {
  private readonly logger = new Logger(UserToolVersionService.name);

  constructor(
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolVersionRepository: UserToolVersionRepository,
  ) {}

  /**
   * Lấy thông tin phiên bản
   * @param versionId ID của phiên bản
   * @param userId ID của người dùng
   * @returns Thông tin phiên bản
   */
  async getVersionById(versionId: string, userId: number): Promise<UserToolVersionDto> {
    try {
      // Lấy thông tin phiên bản
      const version = await this.userToolVersionRepository.findVersionById(versionId, userId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Chuyển đổi sang DTO
      return this.mapVersionToDto(version);
    } catch (error) {
      this.logger.error(`Failed to get version by ID: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND, error.message);
    }
  }

  /**
   * Đặt phiên bản làm mặc định
   * @param toolId ID của tool
   * @param versionId ID của phiên bản
   * @param userId ID của người dùng
   * @returns true nếu thành công
   */
  @Transactional()
  async setDefaultVersion(
    toolId: string,
    versionId: string,
    userId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra tool có tồn tại không và thuộc về người dùng không
      const tool = await this.userToolRepository.findToolById(toolId, userId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra phiên bản có tồn tại không và thuộc về tool và người dùng không
      const version = await this.userToolVersionRepository.findVersionById(versionId, userId);
      if (!version || version.originalToolId !== toolId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Cập nhật thời gian cập nhật cho tool
      tool.updatedAt = Date.now();

      // Lưu tool
      await this.userToolRepository.save(tool);

      return true;
    } catch (error) {
      this.logger.error(`Failed to set default version: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED, error.message);
    }
  }



  /**
   * Chuyển đổi phiên bản sang DTO
   * @param version Phiên bản
   * @returns DTO của phiên bản
   */
  private mapVersionToDto(version: UserToolVersion): UserToolVersionDto {
    const versionDto = new UserToolVersionDto();
    versionDto.id = version.id;
    versionDto.versionName = version.versionName || '';
    versionDto.toolName = version.toolName;
    versionDto.toolDescription = version.toolDescription;
    versionDto.parameters = version.parameters;
    versionDto.changeDescription = version.changeDescription;
    versionDto.status = version.status;
    versionDto.createdAt = version.createdAt;
    versionDto.edited = version.edited;
    return versionDto;
  }
}
