import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

// Mock interfaces
enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  PENDING = 'PENDING'
}

enum PackageType {
  TIME_ONLY = 'TIME_ONLY',
  USAGE_BASED = 'USAGE_BASED',
  HYBRID = 'HYBRID'
}

interface Subscription {
  id: number;
  userId: number;
  planPricingId: number;
  startDate: number;
  endDate: number;
  autoRenew: boolean;
  status: SubscriptionStatus;
  usageLimit?: number;
  currentUsage?: number;
  remainingValue?: number;
  usageUnit?: string;
  createdAt: number;
  updatedAt: number;
}

interface PlanPricing {
  id: number;
  planId: number;
  billingCycle: string;
  price: number;
  usageLimit: number;
  usageUnit: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

interface Plan {
  id: number;
  name: string;
  description: string;
  packageType: PackageType;
  createdAt: number;
  updatedAt: number;
}

interface User {
  id: number;
  fullName: string;
  email: string;
  pointsBalance: number;
}

interface OrderPlanHistory {
  id: number;
  userId: number;
  planId: number;
  planPricingId: number;
  subscriptionId: number;
  planName: string;
  point: string;
  billingCycle: string;
  usageLimit: string | null;
  usageUnit: string | null;
  createdAt: string;
}

// Mock service
class EnhancedSubscriptionUserService {
  constructor(
    private subscriptionRepository,
    private planPricingRepository,
    private planRepository,
    private userRepository,
    private orderPlanHistoryRepository,
    private subscriptionCustomRepository,
    private dataSource
  ) {}

  async createSubscription(
    userId: number,
    planPricingId: number,
    autoRenew: boolean = true
  ): Promise<Subscription> {
    // Mock implementation of the transaction
    const queryRunner = {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      manager: {
        save: jest.fn(entity => ({ ...entity, id: 1 }))
      },
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn()
    };

    this.dataSource.createQueryRunner = jest.fn(() => queryRunner);

    try {
      // Kiểm tra tùy chọn giá có tồn tại không
      const planPricing = await this.planPricingRepository.findOne({
        where: { id: planPricingId, isActive: true }
      });

      if (!planPricing) {
        throw new NotFoundException(`PlanPricing with ID ${planPricingId} not found or not active`);
      }

      // Lấy thông tin gói dịch vụ
      const plan = await this.planRepository.findOne({
        where: { id: planPricing.planId }
      });

      if (!plan) {
        throw new NotFoundException(`Plan with ID ${planPricing.planId} not found`);
      }

      // Kiểm tra người dùng có tồn tại không
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Kiểm tra người dùng đã có đăng ký nào đang hoạt động không
      const activeSubscriptions = await this.subscriptionRepository.find({
        where: { userId, status: SubscriptionStatus.ACTIVE }
      });

      if (activeSubscriptions.length > 0) {
        throw new BadRequestException('User already has active subscriptions');
      }

      // Kiểm tra số điểm của người dùng có đủ không
      const pointsRequired = Number(planPricing.price);
      if (user.pointsBalance < pointsRequired) {
        throw new BadRequestException(`User does not have enough points. Required: ${pointsRequired}, Available: ${user.pointsBalance}`);
      }

      // Tính toán thời gian bắt đầu và kết thúc
      const now = Date.now();
      let endDate = now;

      // Tính toán thời gian kết thúc dựa trên chu kỳ thanh toán và loại gói
      if (plan.packageType === PackageType.TIME_ONLY || plan.packageType === PackageType.HYBRID) {
        // Tính toán thời gian kết thúc dựa trên chu kỳ thanh toán
        switch (planPricing.billingCycle) {
          case 'MONTHLY':
            endDate = now + 30 * 24 * 60 * 60 * 1000; // 30 ngày
            break;
          case 'QUARTERLY':
            endDate = now + 90 * 24 * 60 * 60 * 1000; // 90 ngày
            break;
          case 'YEARLY':
            endDate = now + 365 * 24 * 60 * 60 * 1000; // 365 ngày
            break;
          default:
            endDate = now + 30 * 24 * 60 * 60 * 1000; // Mặc định 30 ngày
        }
      } else {
        // Đối với gói USAGE_BASED, thời gian kết thúc có thể là một giá trị xa trong tương lai
        endDate = now + 365 * 5 * 24 * 60 * 60 * 1000; // 5 năm
      }

      // Xác định usageLimit và remainingValue dựa trên loại gói
      let usageLimit = 0;
      let remainingValue = 0;

      if (plan.packageType === PackageType.USAGE_BASED || plan.packageType === PackageType.HYBRID) {
        usageLimit = planPricing.usageLimit;
        remainingValue = planPricing.usageLimit;
      }

      // Tạo đăng ký mới
      const subscription = this.subscriptionRepository.create({
        userId,
        planPricingId,
        startDate: now,
        endDate,
        autoRenew,
        status: SubscriptionStatus.ACTIVE,
        usageLimit,
        currentUsage: 0,
        remainingValue,
        usageUnit: planPricing.usageUnit,
        createdAt: now,
        updatedAt: now
      });

      // Lưu đăng ký
      const savedSubscription = await queryRunner.manager.save(subscription);

      // Trừ điểm của người dùng
      user.pointsBalance -= pointsRequired;
      await queryRunner.manager.save(user);

      // Tạo lịch sử mua gói dịch vụ
      const orderHistory = this.orderPlanHistoryRepository.create({
        userId,
        planId: plan.id,
        planPricingId,
        subscriptionId: savedSubscription.id,
        planName: plan.name,
        point: pointsRequired.toString(),
        billingCycle: planPricing.billingCycle,
        usageLimit: planPricing.usageLimit ? planPricing.usageLimit.toString() : null,
        usageUnit: planPricing.usageUnit,
        createdAt: now.toString()
      });

      await queryRunner.manager.save(orderHistory);

      // Commit transaction
      await queryRunner.commitTransaction();

      return savedSubscription;
    } catch (error) {
      // Rollback transaction nếu có lỗi
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }
}

describe('EnhancedSubscriptionUserService', () => {
  let service: EnhancedSubscriptionUserService;
  let subscriptionRepository;
  let planPricingRepository;
  let planRepository;
  let userRepository;
  let orderPlanHistoryRepository;
  let dataSource;

  beforeEach(() => {
    // Mock data
    const mockPlanPricing: PlanPricing = {
      id: 1,
      planId: 1,
      billingCycle: 'MONTHLY',
      price: 100,
      usageLimit: 1000,
      usageUnit: 'API_CALLS',
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const mockPlan: Plan = {
      id: 1,
      name: 'Basic Plan',
      description: 'Basic plan for new users',
      packageType: PackageType.TIME_ONLY,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const mockUser: User = {
      id: 10,
      fullName: 'Test User',
      email: '<EMAIL>',
      pointsBalance: 500
    };

    const mockSubscription: Subscription = {
      id: 1,
      userId: 10,
      planPricingId: 1,
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      autoRenew: true,
      status: SubscriptionStatus.ACTIVE,
      usageLimit: 1000,
      currentUsage: 0,
      remainingValue: 1000,
      usageUnit: 'API_CALLS',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // Mock repositories
    subscriptionRepository = {
      create: jest.fn().mockReturnValue(mockSubscription),
      find: jest.fn().mockResolvedValue([])
    };

    planPricingRepository = {
      findOne: jest.fn().mockResolvedValue(mockPlanPricing)
    };

    planRepository = {
      findOne: jest.fn().mockResolvedValue(mockPlan)
    };

    userRepository = {
      findOne: jest.fn().mockResolvedValue(mockUser)
    };

    orderPlanHistoryRepository = {
      create: jest.fn().mockReturnValue({})
    };

    dataSource = {
      createQueryRunner: jest.fn()
    };

    service = new EnhancedSubscriptionUserService(
      subscriptionRepository,
      planPricingRepository,
      planRepository,
      userRepository,
      orderPlanHistoryRepository,
      {},
      dataSource
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSubscription', () => {
    it('should create a subscription for TIME_ONLY package type', async () => {
      const result = await service.createSubscription(10, 1, true);
      
      expect(result).toBeDefined();
      expect(result.userId).toBe(10);
      expect(result.planPricingId).toBe(1);
      expect(result.status).toBe(SubscriptionStatus.ACTIVE);
    });

    it('should throw NotFoundException if plan pricing is not found', async () => {
      planPricingRepository.findOne = jest.fn().mockResolvedValue(null);
      
      await expect(service.createSubscription(10, 999, true)).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if plan is not found', async () => {
      planRepository.findOne = jest.fn().mockResolvedValue(null);
      
      await expect(service.createSubscription(10, 1, true)).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if user is not found', async () => {
      userRepository.findOne = jest.fn().mockResolvedValue(null);
      
      await expect(service.createSubscription(999, 1, true)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if user already has active subscriptions', async () => {
      subscriptionRepository.find = jest.fn().mockResolvedValue([{ id: 1 }]);
      
      await expect(service.createSubscription(10, 1, true)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if user does not have enough points', async () => {
      userRepository.findOne = jest.fn().mockResolvedValue({
        id: 10,
        fullName: 'Test User',
        email: '<EMAIL>',
        pointsBalance: 50 // Not enough points
      });
      
      await expect(service.createSubscription(10, 1, true)).rejects.toThrow(BadRequestException);
    });

    it('should create a subscription for USAGE_BASED package type', async () => {
      planRepository.findOne = jest.fn().mockResolvedValue({
        id: 2,
        name: 'Usage Plan',
        description: 'Usage-based plan',
        packageType: PackageType.USAGE_BASED,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });
      
      const result = await service.createSubscription(10, 1, true);
      
      expect(result).toBeDefined();
      expect(result.usageLimit).toBe(1000);
      expect(result.remainingValue).toBe(1000);
    });

    it('should create a subscription for HYBRID package type', async () => {
      planRepository.findOne = jest.fn().mockResolvedValue({
        id: 3,
        name: 'Hybrid Plan',
        description: 'Hybrid plan',
        packageType: PackageType.HYBRID,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });
      
      const result = await service.createSubscription(10, 1, true);
      
      expect(result).toBeDefined();
      expect(result.usageLimit).toBe(1000);
      expect(result.remainingValue).toBe(1000);
    });
  });
});
