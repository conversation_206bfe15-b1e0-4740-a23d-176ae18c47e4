import { QueryDto } from '@/common/dto';
import { AppException } from '@/common/exceptions/app.exception';
import { PaginatedResult } from '@/common/response';
import { DataFineTunningStatusEnum } from '@modules/model-training/constants/data-fine-tunning.enum';
import { CreateDataFineTuningReq } from '@modules/model-training/dto/data-fine-tuning/create-data-fine-tuning.dto';
import {
  DeleteManyDto,
  UpdateDataFineTuningDto
} from '@modules/model-training/dto/data-fine-tuning/data-fine-tuning.dto';
import {
  UserDataFineTuning
} from '@modules/model-training/entities';
import { MODEL_TRAINING_ERROR_CODES } from '@modules/model-training/exceptions';
import { DataFineTuningValidationHelper } from '@modules/model-training/helpers/data-fine-tuning-validation.helper';
import {
  DataFineTuningRepository,
  UserDataFineTuningRepository,
  UserDataFineTuningResult,
  UserDataFineTuningDetailResult
} from '@modules/model-training/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';

/**
 * Interface cho tham số liên kết dữ liệu fine-tuning với nguồn
 */
export interface LinkDataFineTuningParams {
  /** ID của dữ liệu fine-tuning */
  dataFineTuningId: string;
  /** ID của nguồn dữ liệu (tùy chọn) */
  sourceId?: string;
}

/**
 * Service xử lý các thao tác liên quan đến liên kết dữ liệu fine-tuning với người dùng
 */
@Injectable()
export class UserDataFineTuningService {
  private readonly logger = new Logger(UserDataFineTuningService.name);

  constructor(
    private readonly dataFineTuningRepository: DataFineTuningRepository,
    private readonly userDataFineTuningRepository: UserDataFineTuningRepository,
    private readonly dataFineTuningValidationHelper: DataFineTuningValidationHelper,
  ) { }

  /**
   * Tạo mới một bộ dữ liệu fine-tuning
   * @param createDto Dữ liệu để tạo bộ dữ liệu
   * @param userId ID của người dùng tạo bộ dữ liệu
   * @returns ID của bộ dữ liệu đã tạo
   */
  @Transactional()
  async createDataFineTuning(
    createDto: CreateDataFineTuningReq,
    userId: number,
  ): Promise<string> {
    try {
      const now = Date.now();

      // Tạo bản ghi dữ liệu fine-tuning
      const newData = this.dataFineTuningRepository.create({
        name: createDto.name,
        description: createDto.description,
        trainData: createDto.trainData,
        validationData: createDto.validationData,
        createdAt: now,
        updatedAt: now,
        isForSale: false,
        status: DataFineTunningStatusEnum.DRAFT,
      });

      // Lưu bản ghi dữ liệu fine-tuning
      const savedData = await this.dataFineTuningRepository.save(newData);

      // Tạo liên kết với người dùng
      const userDataFineTuning = this.userDataFineTuningRepository.create({
        id: savedData.id,
        userId,
      });

      // Lưu liên kết
      await this.userDataFineTuningRepository.save(userDataFineTuning);

      return savedData.id;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo dữ liệu fine-tuning: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_FAILED,
        `Lỗi khi tạo dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin bộ dữ liệu fine-tuning
   * @param id ID của bộ dữ liệu cần cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @param userId ID của người dùng thực hiện cập nhật
   */
  @Transactional()
  async updateDataFineTuning(
    id: string,
    updateDto: UpdateDataFineTuningDto,
    userId: number,
  ): Promise<void> {
    try {
      // Tìm liên kết
      const userDataFineTuning = await this.userDataFineTuningRepository.findByIdAndUserId(id, userId);
      if (!userDataFineTuning) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
          'Không tìm thấy dữ liệu fine-tuning',
        );
      }

      // Tìm dữ liệu fine-tuning
      const dataFineTuning = await this.dataFineTuningRepository.findById(id);
      if (!dataFineTuning) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
          'Không tìm thấy dữ liệu fine-tuning',
        );
      }

      // Chỉ cho phép cập nhật khi trạng thái là DRAFT
      if (dataFineTuning.status !== DataFineTunningStatusEnum.DRAFT) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_STATUS,
          'Chỉ có thể cập nhật dữ liệu ở trạng thái DRAFT',
        );
      }

      // Cập nhật thông tin
      await this.dataFineTuningRepository.update(id, {
        name: updateDto.name,
        description: updateDto.description,
        trainData: updateDto.trainData,
        validationData: updateDto.validationData,
        updatedAt: Date.now(),
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật dữ liệu fine-tuning: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Xóa một bộ dữ liệu fine-tuning
   * @param id ID của bộ dữ liệu cần xóa
   * @param userId ID của người dùng thực hiện xóa
   */
  @Transactional()
  async deleteDataFineTuning(id: string, userId: number): Promise<void> {
    try {
      // Tìm liên kết
      const userDataFineTuning = await this.userDataFineTuningRepository.findByIdAndUserId(id, userId);
      if (!userDataFineTuning) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
          'Không tìm thấy dữ liệu fine-tuning',
        );
      }

      // Xóa mềm bản ghi
      await this.dataFineTuningRepository.update(id, {
        deletedAt: Date.now(),
        status: DataFineTunningStatusEnum.DELETED,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi xóa dữ liệu fine-tuning: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DELETE_FAILED,
        `Lỗi khi xóa dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều bộ dữ liệu fine-tuning
   * @param deleteDto Danh sách ID của các bộ dữ liệu cần xóa
   * @param userId ID của người dùng thực hiện xóa
   */
  @Transactional()
  async deleteMultipleDataFineTuning(deleteDto: DeleteManyDto, userId: number): Promise<void> {
    try {
      // Kiểm tra từng bản ghi
      for (const id of deleteDto.ids) {
        const userDataFineTuning = await this.userDataFineTuningRepository.findByIdAndUserId(id, userId);
        if (!userDataFineTuning) {
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
            `Không tìm thấy dữ liệu fine-tuning với ID ${id}`,
          );
        }
      }

      // Xóa mềm các bản ghi
      const now = Date.now();
      for (const id of deleteDto.ids) {
        await this.dataFineTuningRepository.update(id, {
          deletedAt: now,
          status: DataFineTunningStatusEnum.DELETED,
        });
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều dữ liệu fine-tuning: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DELETE_FAILED,
        `Lỗi khi xóa nhiều dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Liên kết dữ liệu fine-tuning với nguồn
   * @param params Tham số liên kết
   * @param userId ID của người dùng
   * @returns Thông tin liên kết đã tạo
   */
  @Transactional()
  async linkDataFineTuningToModel(
    params: LinkDataFineTuningParams,
    userId: number,
  ): Promise<UserDataFineTuning> {
    try {
      // Tìm dữ liệu fine-tuning
      const dataFineTuning = await this.dataFineTuningRepository.findById(params.dataFineTuningId);
      this.dataFineTuningValidationHelper.validateDataFineTuningExists(dataFineTuning);

      // Kiểm tra quyền truy cập
      this.dataFineTuningValidationHelper.validateDataFineTuningPermission(dataFineTuning, userId);

      // Tạo liên kết
      const userDataFineTuning = this.userDataFineTuningRepository.create({
        id: params.dataFineTuningId,
        userId,
        sourceId: params.sourceId
      });

      // Lưu liên kết
      return await this.userDataFineTuningRepository.save(userDataFineTuning);
    } catch (error) {
      this.logger.error(`Lỗi khi liên kết dữ liệu fine-tuning với nguồn: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi liên kết dữ liệu fine-tuning với nguồn: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách liên kết dữ liệu fine-tuning của người dùng
   * @param userId ID của người dùng
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách liên kết dữ liệu fine-tuning
   */
  async getUserDataFineTuningList(
    userId: number,
    query: QueryDto,
  ): Promise<PaginatedResult<UserDataFineTuningResult>> {
    try {
      return await this.userDataFineTuningRepository.findByUserId(userId, query);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách liên kết dữ liệu fine-tuning: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy danh sách liên kết dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết liên kết dữ liệu fine-tuning
   * @param id ID của dữ liệu fine-tuning
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết liên kết dữ liệu fine-tuning
   */
  async getUserDataFineTuningDetail(
    id: string,
    userId: number,
  ): Promise<UserDataFineTuningDetailResult> {
    try {
      const userDataFineTuning = await this.userDataFineTuningRepository.findByIdAndUserId(id, userId);
      if (!userDataFineTuning) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
          'Không tìm thấy liên kết dữ liệu fine-tuning',
        );
      }

      return userDataFineTuning;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết liên kết dữ liệu fine-tuning: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy chi tiết liên kết dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }
}
