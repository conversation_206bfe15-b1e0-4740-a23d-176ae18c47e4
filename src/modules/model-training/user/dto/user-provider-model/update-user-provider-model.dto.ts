import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật provider model của user
 */
export class UpdateUserProviderModelDto {
  /**
   * Tên định danh cho provider model
   */
  @ApiProperty({
    description: 'Tên định danh cho provider model',
    example: 'My OpenAI Provider Updated',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  @MaxLength(255, { message: 'Tên không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * API key của nhà cung cấp
   */
  @ApiProperty({
    description: 'API key của nhà cung cấp',
    example: 'sk-abcdefghijklmnopqrstuvwxyz123456',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'API key phải là chuỗi' })
  apiKey?: string;
}
