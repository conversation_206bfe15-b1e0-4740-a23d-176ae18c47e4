import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards,
  Query,
  ParseUUIDPipe
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { UserProviderModelService } from '../services/user-provider-model.service';
import { 
  CreateUserProviderModelDto, 
  UpdateUserProviderModelDto,
  UserProviderModelResponseDto
} from '../dto/user-provider-model';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { QueryDto } from '@common/dto';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@/modules/auth/decorators';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API cho User Provider Model
 */
@ApiTags(SWAGGER_API_TAGS.USER_PROVIDER_MODEL)
@Controller('user/provider-model')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserProviderModelController {
  constructor(private readonly userProviderModelService: UserProviderModelService) {}

  /**
   * Tạo mới provider model
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới provider model' })
  @ApiResponse({ 
    status: 201, 
    description: 'Tạo mới provider model thành công',
    type: ApiResponseDto
  })
  create(
    @Body() createUserProviderModelDto: CreateUserProviderModelDto,
    @CurrentUser('id') userId: number
  ) {
    return this.userProviderModelService.create(userId, createUserProviderModelDto);
  }

  /**
   * Lấy danh sách provider model có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách provider model có phân trang' })
  @ApiResponse({ 
    status: 200, 
    description: 'Danh sách provider model',
    type: ApiResponseDto
  })
  findAll(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryDto
  ) {
    return this.userProviderModelService.findAll(userId, queryDto);
  }

  /**
   * Cập nhật provider model
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật provider model' })
  @ApiResponse({ 
    status: 200, 
    description: 'Cập nhật provider model thành công',
    type: ApiResponseDto
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserProviderModelDto: UpdateUserProviderModelDto,
    @CurrentUser('id') userId: number
  ) {
    return this.userProviderModelService.update(userId, id, updateUserProviderModelDto);
  }

  /**
   * Xóa provider model
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa provider model' })
  @ApiResponse({ 
    status: 200, 
    description: 'Xóa provider model thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userProviderModelService.remove(userId, id);
  }
}
