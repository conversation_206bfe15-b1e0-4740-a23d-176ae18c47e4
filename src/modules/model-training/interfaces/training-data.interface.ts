/**
 * Interface cho tin nhắn trong dữ liệu huấn luyện
 */
export interface TrainingMessage {
  /** Vai trò của tin nhắn (system, user, assistant) */
  role: 'system' | 'user' | 'assistant';
  /** Nội dung của tin nhắn */
  content: string;
}

/**
 * Interface cho một cuộc hội thoại trong dữ liệu huấn luyện (định dạng JSONL)
 * Mỗi dòng trong file JSONL là một đối tượng JSON với cấu trúc này
 */
export interface TrainingConversation {
  /** Danh sách các tin nhắn trong cuộc hội thoại */
  messages: TrainingMessage[];
}

/**
 * Interface cho tập dữ liệu huấn luyện (nhiều cuộc hội thoại)
 */
export interface TrainingDataset {
  /** Mảng các cuộc hội thoại */
  conversations: TrainingConversation[];
}

/**
 * Interface cho tham số tạo dữ liệu huấn luyện
 */
export interface CreateTrainingDataParams {
  /** Tên của dữ liệu huấn luyện */
  name: string;
  /** Mô tả về dữ liệu huấn luyện */
  description?: string;
  /** Danh sách các cuộc hội thoại */
  conversations: TrainingConversation[];
  /** ID của người dùng tạo dữ liệu huấn luyện */
  userId: number;
}

/**
 * Interface cho kết quả tạo dữ liệu huấn luyện
 */
export interface CreateTrainingDataResponse {
  /** ID của dữ liệu huấn luyện */
  id: number;
  /** Tên của dữ liệu huấn luyện */
  name: string;
  /** Mô tả về dữ liệu huấn luyện */
  description?: string;
  /** Số lượng cuộc hội thoại */
  conversationCount: number;
  /** Số lượng tin nhắn */
  messageCount: number;
  /** Thời gian tạo */
  createdAt: Date;
  /** ID của người dùng tạo dữ liệu huấn luyện */
  userId: number;
  /** S3 key của file dữ liệu huấn luyện */
  fileKey?: string;
}
