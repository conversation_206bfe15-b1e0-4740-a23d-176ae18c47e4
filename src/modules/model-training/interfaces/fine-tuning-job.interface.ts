/**
 * Interface cho trạng thái của fine-tuning job
 */
export enum FineTuningJobStatus {
  VALIDATING_FILES = 'validating_files',
  QUEUED = 'queued',
  RUNNING = 'running',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * Interface cho loại nhà cung cấp AI
 */
export enum AIProvider {
  OPENAI = 'openai',
  GOOGLE_AI = 'google_ai',
  META_AI = 'meta_ai',
  ANTHROPIC = 'anthropic',
  DEEPSEEK = 'deepseek',
  XAI = 'xai'
}

/**
 * Interface cho tham số tạo fine-tuning job
 */
export interface CreateFineTuningJobParams {
  /** Tên của fine-tuning job */
  name: string;
  /** Mô tả về fine-tuning job */
  description?: string;
  /** ID của model cơ sở để fine-tune */
  baseModelId: string;
  /** ID của dữ liệu huấn luyện */
  trainingDataId: number;
  /** ID của dữ liệu validation (tùy chọn) */
  validationDataId?: number;
  /** Nhà cung cấp AI */
  provider: AIProvider;
  /** Siêu tham số cho quá trình fine-tuning */
  hyperParameters?: {
    /** Số epoch để huấn luyện */
    nEpochs?: number;
    /** Kích thước batch */
    batchSize?: number | 'auto';
    /** Tốc độ học */
    learningRateMultiplier?: number | 'auto';
  };
  /** ID của người dùng tạo fine-tuning job */
  userId: number;
}

/**
 * Interface cho kết quả tạo fine-tuning job
 */
export interface FineTuningJobResponse {
  /** ID của fine-tuning job */
  id: number;
  /** Tên của fine-tuning job */
  name: string;
  /** Mô tả về fine-tuning job */
  description?: string;
  /** ID của model cơ sở */
  baseModelId: string;
  /** ID của dữ liệu huấn luyện */
  trainingDataId: number;
  /** ID của dữ liệu validation (nếu có) */
  validationDataId?: number;
  /** Nhà cung cấp AI */
  provider: AIProvider;
  /** Trạng thái của job */
  status: FineTuningJobStatus;
  /** ID của model fine-tuned (chỉ có khi job hoàn thành) */
  fineTunedModelId?: string;
  /** Thông tin về lỗi (nếu có) */
  error?: {
    code: string;
    message: string;
  };
  /** Siêu tham số đã sử dụng */
  hyperParameters?: {
    nEpochs: number | 'auto';
    batchSize: number | 'auto';
    learningRateMultiplier: number | 'auto';
  };
  /** Số lượng token đã xử lý */
  trainedTokens?: number;
  /** Thời gian tạo */
  createdAt: Date;
  /** Thời gian cập nhật cuối cùng */
  updatedAt: Date;
  /** Thời gian hoàn thành */
  finishedAt?: Date;
  /** ID của người dùng tạo fine-tuning job */
  userId: number;
  /** ID của job trên nền tảng của nhà cung cấp */
  providerJobId: string;
}
