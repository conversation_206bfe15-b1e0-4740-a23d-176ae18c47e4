/**
 * Interface cho tin nhắn trong định dạng JSONL
 * Khớp chính xác với cấu trúc JSON trong file JSONL
 */
export interface JSONLMessage {
  /** Vai trò của tin nhắn (system, user, assistant) */
  role: 'system' | 'user' | 'assistant';
  /** Nội dung của tin nhắn */
  content: string;
}

/**
 * Interface cho một dòng trong file JSONL
 * Mỗi dòng trong file JSONL là một đối tượng JSON với cấu trúc này
 */
export interface JSONLConversation {
  /** Danh sách các tin nhắn trong cuộc hội thoại */
  messages: JSONLMessage[];
}

/**
 * Interface cho tham số chuyển đổi dữ liệu sang định dạng JSONL
 */
export interface ConvertToJSONLParams {
  /** Danh sách các cuộc hội thoại */
  conversations: JSONLConversation[];
  /** Đường dẫn đầu ra cho file JSONL */
  outputPath?: string;
}

/**
 * Interface cho kết quả chuyển đổi dữ liệu sang định dạng JSONL
 */
export interface ConvertToJSONLResult {
  /** Nội dung của file JSONL */
  content: string;
  /** Đường dẫn đến file JSONL đã tạo (nếu có) */
  filePath?: string;
  /** Số lượng cuộc hội thoại đã chuyển đổi */
  conversationCount: number;
  /** Số lượng tin nhắn đã chuyển đổi */
  messageCount: number;
}

/**
 * Interface cho tham số phân tích file JSONL
 */
export interface ParseJSONLParams {
  /** Nội dung của file JSONL */
  content: string;
  /** Giới hạn số lượng cuộc hội thoại cần phân tích */
  limit?: number;
}

/**
 * Interface cho kết quả phân tích file JSONL
 */
export interface ParseJSONLResult {
  /** Danh sách các cuộc hội thoại đã phân tích */
  conversations: JSONLConversation[];
  /** Số lượng cuộc hội thoại đã phân tích */
  conversationCount: number;
  /** Số lượng tin nhắn đã phân tích */
  messageCount: number;
}
