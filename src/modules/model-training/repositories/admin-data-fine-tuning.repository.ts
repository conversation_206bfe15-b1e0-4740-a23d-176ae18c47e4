import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminDataFineTuning } from '../entities/admin-data-fine-tuning.entity';
import { DataFineTuning } from '../entities/data-fine-tuning.entity';
import { AdminDataFineTuningQueryDto } from '../admin/dto/data-fine-tuning/admin-data-fine-tuning-query.dto';
import { PaginatedResult } from '@/common/response';
import { QueryDto } from '@/common/dto';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho bảng admin_data_fine_tuning
 * Cung cấp các phương thức truy vấn và thao tác với dữ liệu fine-tuning ở cấp độ quản trị
 */
@Injectable()
export class AdminDataFineTuningRepository extends Repository<AdminDataFineTuning> {
  private readonly logger = new Logger(AdminDataFineTuningRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminDataFineTuning, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho admin data fine tuning
   * @returns SelectQueryBuilder<AdminDataFineTuning> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<AdminDataFineTuning> {
    return this.createQueryBuilder('adminDataFineTuning')
      .leftJoinAndSelect('adminDataFineTuning.dataFineTuning', 'dataFineTuning')
      .leftJoinAndSelect('adminDataFineTuning.createdByEmployee', 'createdByEmployee')
      .leftJoinAndSelect('adminDataFineTuning.updatedByEmployee', 'updatedByEmployee')
      .leftJoinAndSelect('adminDataFineTuning.deletedByEmployee', 'deletedByEmployee');
  }

  /**
   * Tìm kiếm dữ liệu fine-tuning quản trị với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các bản ghi admin_data_fine_tuning
   */
  async findPaginated(query: QueryDto): Promise<PaginatedResult<AdminDataFineTuning>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const queryBuilder = this.createBaseQuery()
      .select([
        'adminDataFineTuning.id',
        'adminDataFineTuning.createdBy',
        'adminDataFineTuning.updatedBy',
        'adminDataFineTuning.modelId',
      ])
      .where('dataFineTuning.deletedAt IS NULL');

    // Tìm kiếm theo tên hoặc mô tả của dữ liệu fine-tuning
    if (query.search) {
      queryBuilder.andWhere(
        '(dataFineTuning.name ILIKE :search OR dataFineTuning.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Sắp xếp
    queryBuilder.orderBy('dataFineTuning.updatedAt', 'DESC');

    // Phân trang
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm dữ liệu fine-tuning quản trị theo ID
   * @param id UUID của bản ghi cần tìm
   * @returns Thông tin chi tiết của bản ghi hoặc null nếu không tìm thấy
   */
  async findById(id: string): Promise<AdminDataFineTuning | null> {
    try {
      return await this.createQueryBuilder('adminDataFineTuning')
        .select([
          'adminDataFineTuning.id',
          'adminDataFineTuning.createdBy',
          'adminDataFineTuning.updatedBy',
          'adminDataFineTuning.deletedBy',
        ])
        .where('adminDataFineTuning.id = :id', { id })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm dữ liệu fine-tuning quản trị với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm dữ liệu fine-tuning quản trị theo ID mô hình
   * @param modelId ID của mô hình
   * @returns Danh sách các bản ghi admin_data_fine_tuning
   */
  async findByModelId(modelId: string): Promise<AdminDataFineTuning[]> {
    try {
      return await this.createBaseQuery()
        .where('adminDataFineTuning.modelId = :modelId', { modelId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm dữ liệu fine-tuning quản trị với modelId ${modelId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Tìm dữ liệu fine-tuning quản trị theo người tạo
   * @param createdBy ID của nhân viên tạo
   * @param query Tham số truy vấn và phân trang
   * @returns Kết quả phân trang chứa các bản ghi admin_data_fine_tuning
   */
  async findByCreatedBy(createdBy: number, query: QueryDto): Promise<PaginatedResult<AdminDataFineTuning>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const queryBuilder = this.createBaseQuery()
      .select([
        'adminDataFineTuning.id',
        'adminDataFineTuning.createdBy',
        'adminDataFineTuning.updatedBy',
        'adminDataFineTuning.modelId',
      ])
      .where('adminDataFineTuning.createdBy = :createdBy', { createdBy })
      .andWhere('dataFineTuning.deletedAt IS NULL');

    // Tìm kiếm theo tên hoặc mô tả của dữ liệu fine-tuning
    if (query.search) {
      queryBuilder.andWhere(
        '(dataFineTuning.name ILIKE :search OR dataFineTuning.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Sắp xếp
    queryBuilder.orderBy('dataFineTuning.updatedAt', 'DESC');

    // Phân trang
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật thông tin người cập nhật
   * @param id UUID của bản ghi cần cập nhật
   * @param updatedBy ID của nhân viên cập nhật
   */
  @Transactional()
  async updateUpdatedBy(id: string, updatedBy: number): Promise<void> {
    await this.update(id, { updatedBy });
  }

  /**
   * Cập nhật thông tin người xóa
   * @param id UUID của bản ghi cần cập nhật
   * @param deletedBy ID của nhân viên xóa
   */
  @Transactional()
  async updateDeletedBy(id: string, deletedBy: number): Promise<void> {
    await this.update(id, { deletedBy });
  }

  /**
   * Tìm dữ liệu fine-tuning theo ID với thông tin nhân viên
   */
  async findByIdWithEmployeeInfo(id: string): Promise<AdminDataFineTuning | null> {
    try {
      // Chỉ lấy các trường cần thiết từ bảng admin_data_fine_tuning
      return await this.createQueryBuilder('adminDataFineTuning')
        .select([
          'adminDataFineTuning.id',
          'adminDataFineTuning.createdBy',
          'adminDataFineTuning.updatedBy',
          'adminDataFineTuning.deletedBy',
        ])
        .where('adminDataFineTuning.id = :id', { id })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm dữ liệu fine-tuning với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Lấy danh sách dữ liệu fine-tuning với thông tin nhân viên
   */
  async findAllWithEmployeeInfo(
    query: AdminDataFineTuningQueryDto,
  ): Promise<PaginatedResult<AdminDataFineTuning>> {
    try {
      const { page = 1, limit = 10, name, status, createdBy, updatedBy, deletedBy } = query;
      const skip = (page - 1) * limit;

      const qb = this.createBaseQuery()
        .where('dataFineTuning.deletedAt IS NULL');

      // Áp dụng các điều kiện tìm kiếm
      if (name) {
        qb.andWhere('dataFineTuning.name ILIKE :name', { name: `%${name}%` });
      }

      if (status) {
        qb.andWhere('dataFineTuning.status = :status', { status });
      }

      if (createdBy) {
        qb.andWhere('adminDataFineTuning.createdBy = :createdBy', { createdBy });
      }

      if (updatedBy) {
        qb.andWhere('adminDataFineTuning.updatedBy = :updatedBy', { updatedBy });
      }

      if (deletedBy) {
        qb.andWhere('adminDataFineTuning.deletedBy = :deletedBy', { deletedBy });
      }

      // Đếm tổng số bản ghi
      const totalItems = await qb.getCount();

      // Lấy dữ liệu phân trang
      const items = await qb
        .skip(skip)
        .take(limit)
        .getMany();

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách dữ liệu fine-tuning: ${error.message}`, error.stack);
      throw error;
    }
  }
}
