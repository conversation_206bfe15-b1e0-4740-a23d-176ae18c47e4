import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminProviderModel } from '../entities/admin-provider-model.entity';
import { Transactional } from 'typeorm-transactional';
import { PaginatedResult } from '@common/response';
import { QueryDto } from '@common/dto';
import { AppException } from '@common/exceptions';
import { MODEL_TRAINING_ERROR_CODES } from '../exceptions';
import { TypeProviderEnum } from '../constants/type-provider.enum';

/**
 * Repository cho AdminProviderModel
 */
@Injectable()
export class AdminProviderModelRepository extends Repository<AdminProviderModel> {
  constructor(private dataSource: DataSource) {
    super(AdminProviderModel, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản
   * @returns SelectQueryBuilder
   */
  private createBaseQuery(): SelectQueryBuilder<AdminProviderModel> {
    return this.createQueryBuilder('adminProviderModel');
  }

  /**
   * Kiểm tra tên provider đã tồn tại chưa
   * @param name Tên provider cần kiểm tra
   * @param excludeId ID provider cần loại trừ (dùng khi update) (UUID)
   * @returns boolean
   */
  async isNameExists(name: string, excludeId?: string): Promise<boolean> {
    const query = this.createBaseQuery()
      .where('adminProviderModel.name = :name', { name })
      .andWhere('adminProviderModel.deleted_at IS NULL');

    if (excludeId) {
      query.andWhere('adminProviderModel.id != :id', { id: excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Lấy danh sách provider có phân trang
   * @param queryDto Query parameters
   * @returns Danh sách provider có phân trang
   */
  async findPaginated(queryDto: QueryDto): Promise<PaginatedResult<AdminProviderModel>> {
    const { page, limit, search, sortBy = 'created_at', sortDirection } = queryDto;

    const query = this.createBaseQuery()
      .where('adminProviderModel.deleted_at IS NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      query.andWhere('(adminProviderModel.name ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Áp dụng phân trang và sắp xếp
    query
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy(`adminProviderModel.${sortBy}`, sortDirection);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      }
    };
  }

  /**
   * Lấy chi tiết provider theo ID
   * @param id ID của provider (UUID)
   * @returns Chi tiết provider
   */
  async findDetailById(id: string): Promise<AdminProviderModel | null> {
    return this.createBaseQuery()
      .where('adminProviderModel.id = :id', { id })
      .andWhere('adminProviderModel.deleted_at IS NULL')
      .getOne();
  }

  /**
   * Kiểm tra provider có tồn tại không
   * @param id ID của provider (UUID)
   * @returns boolean
   */
  async isExists(id: string): Promise<boolean> {
    const count = await this.createBaseQuery()
      .where('adminProviderModel.id = :id', { id })
      .andWhere('adminProviderModel.deleted_at IS NULL')
      .getCount();

    return count > 0;
  }

  /**
   * Cập nhật provider
   * @param id ID của provider (UUID)
   * @param updateData Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateProvider(id: string, updateData: Partial<AdminProviderModel>): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(AdminProviderModel)
      .set(updateData)
      .where('id = :id', { id })
      .andWhere('deleted_at IS NULL')
      .execute();

    return result.affected ? result.affected > 0 : false;
  }

  /**
   * Xóa mềm provider
   * @param id ID của provider (UUID)
   * @param employeeId ID của employee thực hiện xóa
   * @returns boolean
   */
  @Transactional()
  async softDeleteProvider(id: string, employeeId: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(AdminProviderModel)
      .set({
        deletedBy: employeeId,
        deletedAt: () => '(EXTRACT(EPOCH FROM now()) * 1000)::bigint',
      })
      .where('id = :id', { id })
      .andWhere('deleted_at IS NULL')
      .execute();

    return result.affected ? result.affected > 0 : false;
  }

  /**
   * Lấy type và API key theo provider ID
   * @param providerId ID của provider (UUID)
   * @returns Object chứa type và apiKey
   * @throws AppException nếu không tìm thấy provider hoặc provider đã bị xóa
   */
  async findTypeAndApiKeyById(providerId: string): Promise<{ type: string; apiKey: string }> {
    // Sử dụng QueryBuilder để tạo câu truy vấn
    const result = await this.createBaseQuery()
      .select(['adminProviderModel.type', 'adminProviderModel.apiKey'])
      .where('adminProviderModel.id = :id', { id: providerId })
      .andWhere('adminProviderModel.deleted_at IS NULL') // Đảm bảo provider chưa bị xóa
      .getOne();

    // Kiểm tra kết quả và ném exception nếu không tìm thấy
    if (!result) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        `Không tìm thấy nhà cung cấp với ID ${providerId} hoặc nhà cung cấp đã bị xóa`
      );
    }

    // Trả về object chứa type và apiKey
    return {
      type: result.type,
      apiKey: result.apiKey
    };
  }

  /**
   * Lấy type và API key theo provider ID với kiểm tra chi tiết
   * @param providerId ID của provider (UUID)
   * @returns Object chứa id, type, apiKey và thông tin khác
   * @throws AppException nếu không tìm thấy provider hoặc provider đã bị xóa
   */
  async getProviderDetailsById(providerId: string): Promise<{ id: string; type: string; apiKey: string; name: string; createdAt: number }> {
    // Sử dụng QueryBuilder để tạo câu truy vấn chi tiết hơn
    const result = await this.createBaseQuery()
      .select([
        'adminProviderModel.id',
        'adminProviderModel.type',
        'adminProviderModel.apiKey',
        'adminProviderModel.name',
        'adminProviderModel.createdAt'
      ])
      .where('adminProviderModel.id = :id', { id: providerId })
      .andWhere('adminProviderModel.deleted_at IS NULL') // Đảm bảo provider chưa bị xóa
      .getOne();

    // Kiểm tra kết quả và ném exception nếu không tìm thấy
    if (!result) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        `Không tìm thấy nhà cung cấp với ID ${providerId} hoặc nhà cung cấp đã bị xóa`
      );
    }

    // Trả về object chứa thông tin chi tiết
    return {
      id: result.id,
      type: result.type,
      apiKey: result.apiKey,
      name: result.name,
      createdAt: result.createdAt
    };
  }

  /**
   * Lấy type và API key theo tên provider
   * @param providerName Tên của provider
   * @returns Object chứa type và apiKey
   * @throws AppException nếu không tìm thấy provider
   */
  async findTypeAndApiKeyByName(providerName: string): Promise<{ type: string; apiKey: string }> {
    const result = await this.createBaseQuery()
      .select(['adminProviderModel.type', 'adminProviderModel.apiKey'])
      .where('adminProviderModel.name = :name', { name: providerName })
      .andWhere('adminProviderModel.deleted_at IS NULL')
      .getOne();

    if (!result) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        `Không tìm thấy nhà cung cấp với tên ${providerName}`
      );
    }

    return {
      type: result.type,
      apiKey: result.apiKey
    };
  }

  /**
   * Lấy type và API key theo loại provider
   * @param providerType Loại provider (OPENAI, ANTHROPIC, v.v.)
   * @returns Object chứa type và apiKey
   * @throws AppException nếu không tìm thấy provider
   */
  async findTypeAndApiKeyByType(providerType: TypeProviderEnum): Promise<{ type: string; apiKey: string }> {
    const result = await this.createBaseQuery()
      .select(['adminProviderModel.type', 'adminProviderModel.apiKey'])
      .where('adminProviderModel.type = :type', { type: providerType })
      .andWhere('adminProviderModel.deleted_at IS NULL')
      .getOne();

    if (!result) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        `Không tìm thấy nhà cung cấp với loại ${providerType}`
      );
    }

    return {
      type: result.type,
      apiKey: result.apiKey
    };
  }
}
