import { AiProviderHelper } from '@/shared/services/ai/helpers/ai-provider.helper';
import { Injectable, Logger } from '@nestjs/common';
import { TypeProviderEnum } from '../constants/type-provider.enum';

/**
 * Helper class để kiểm tra xem ID của fine-tuning model có tồn tại trong provider không
 */
@Injectable()
export class FineTuningModelIdValidatorHelper {
  private readonly logger = new Logger(FineTuningModelIdValidatorHelper.name);

  constructor(
    private readonly aiProviderHelper: AiProviderHelper,
  ) { }

  /**
   * Kiểm tra xem ID của fine-tuning model có tồn tại trong provider không
   * @param id ID của fine-tuning model
   * @param providerType Loại provider
   * @param apiKey API key của provider
   * @returns true nếu ID tồn tại, false nếu không tồn tại
   */
  async validateFineTuningModelId(
    id: string,
    providerType: TypeProviderEnum,
    apiKey: string,
  ): Promise<void> {
    try {
      // Trong môi trường thực tế, cần gọi API của provider để kiểm tra ID
        await this.aiProviderHelper.retrieveModel(id, providerType, apiKey);
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra ID fine-tuning model: ${error.message}`, error.stack);
      throw error;
    }
  }
}
