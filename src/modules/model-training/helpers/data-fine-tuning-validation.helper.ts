import { DataFineTunningStatusEnum } from '@/modules/model-training/constants/data-fine-tunning.enum';
import { AppException } from '@common/exceptions';
import { DataFineTuning } from '@modules/model-training/entities';
import { Injectable } from '@nestjs/common';
import { MODEL_TRAINING_ERROR_CODES } from '../exceptions';

@Injectable()
export class DataFineTuningValidationHelper {
  /**
   * Validates that a data fine-tuning record exists
   * @param record The record to validate
   * @throws AppException if the record does not exist
   */
  validateDataFineTuningExists(record: DataFineTuning | null): void {
    if (!record) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
        'DataFineTuning not found',
      );
    }
  }

  validateDataFineTuningPermission(
    record: DataFineTuning | null,
    userId: number,
  ): void {
    if (!record) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
        'DataFineTuning not found',
      );
    }
  }

  validateModerationStatus(
    record: DataFineTuning,
  ): void {
    if (record.status !== DataFineTunningStatusEnum.PENDING) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.BAD_REQUEST,
        'Invalid moderation status',
      );
    }
  }

  validateSaleStatus(
    status: DataFineTunningStatusEnum,
  ) {
    if (status !== DataFineTunningStatusEnum.APPROVED) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.BAD_REQUEST,
        'Invalid sale status',
      );
    }
  }

  validateUserPermission(
    userId: number,
    targetId: number,
  ) {
    if (userId !== targetId) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.FORBIDDEN,
      );
    }
  }

}