import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho danh sách mô hình fine-tuning của user với phân trang
 */
@Exclude()
export class UserFineTuningModelPaginatedRes {
  /**
   * ID định danh duy nhất cho mô hình fine-tuning
   */
  @Expose()
  @ApiProperty({
    description: 'ID định danh duy nhất cho mô hình fine-tuning',
    example: 'ft:gpt-3.5-turbo:openai:custom-model:7p89qrs',
  })
  id: string;

  /**
   * Tên hiển thị của mô hình fine-tuning
   */
  @Expose()
  @ApiProperty({
    description: 'Tên hiển thị của mô hình fine-tuning',
    example: 'Mô hình hỗ trợ khách hàng',
  })
  name: string;

  /**
   * <PERSON><PERSON> tả chi tiết về mô hình và mục đích sử dụng
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về mô hình và mục đích sử dụng',
    example: 'Mô hình được huấn luyện để trả lời các câu hỏi về sản phẩm và dịch vụ',
  })
  description: string;

  /**
   * Số lượng token đã sử dụng trong quá trình fine-tuning
   */
  @Expose()
  @ApiProperty({
    description: 'Số lượng token đã sử dụng trong quá trình fine-tuning',
    example: 10000,
  })
  token: number;

  /**
   * Trạng thái hiện tại của mô hình
   */
  @Expose()
  @ApiProperty({
    description: 'Trạng thái hiện tại của mô hình',
    example: 'completed',
  })
  status: string;

  /**
   * Thời điểm tạo mô hình (timestamp millis)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm tạo mô hình (timestamp millis)',
    example: 1625097600000,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật mô hình gần nhất (timestamp millis)
   */
  @Expose()
  @ApiProperty({
    description: 'Thời điểm cập nhật mô hình gần nhất (timestamp millis)',
    example: 1625097600000,
  })
  updatedAt: number;

  /**
   * Tên của mô hình gốc
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Tên của mô hình gốc',
    example: 'GPT-3.5 Turbo',
  })
  baseModelName?: string;

  /**
   * ID của mô hình gốc
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của mô hình gốc',
    example: 'gpt-3.5-turbo',
  })
  modelBaseId?: string;

  /**
   * ID của provider
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của provider',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  providerId?: string;

  /**
   * Tên của provider
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Tên của provider',
    example: 'OpenAI',
  })
  providerName?: string;

  /**
   * Loại của provider
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'Loại của provider',
    example: 'openai',
  })
  providerType?: string;

  /**
   * ID của model trong provider
   */
  @Expose()
  @ApiPropertyOptional({
    description: 'ID của model trong provider',
    example: 'gpt-3.5-turbo',
  })
  modelId?: string;
}

/**
 * DTO cho chi tiết mô hình fine-tuning của user
 */
@Exclude()
export class UserFineTuningModelDetailRes extends UserFineTuningModelPaginatedRes {
  /**
   * ID của người dùng
   */
  @Expose()
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  userId: number;
}
