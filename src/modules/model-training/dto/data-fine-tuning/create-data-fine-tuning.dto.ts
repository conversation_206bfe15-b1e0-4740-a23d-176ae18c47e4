import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsObject, IsOptional, IsString } from "class-validator";
import { DataFineTunningStatusEnum } from "../../constants/data-fine-tunning.enum";
import { TrainingDataset } from "../../interfaces";

export class CreateDataFineTuningReq {
    @ApiProperty({
        description: 'Name of the fine-tuning dataset',
        required: true,
        example: 'Custom Assistant v1',
    })
    @IsString()
    name: string;

    @ApiProperty({
        description: 'Detailed description of the model and its use case',
        required: false,
        example: 'A model fine-tuned for answering academic questions in Vietnamese.',
    })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({
        description: 'Training data (JSONB array)',
        required: true,
        type: 'array',
        example:
        {
            messages: [
                { role: 'system', content: 'Bạn là một AI hỗ trợ học tập.' },
                { role: 'user', content: 'Gi<PERSON><PERSON> tôi học tiếng <PERSON>.' },
                { role: 'assistant', content: '<PERSON><PERSON> nhiên! <PERSON>ạn muốn bắt đầu từ đâu?' }
            ]
        }

    })
    @IsObject()
    trainData: TrainingDataset;

    @ApiProperty({
        description: 'Validation data (JSONB array)',
        required: false,
        example:
        {
            messages: [
                { role: 'user', content: 'Bạn tên gì?' },
                { role: 'assistant', content: 'Tôi là một AI trợ lý.' }
            ]
        }

    })
    @IsOptional()
    @IsObject()
    validationData?: TrainingDataset;
}