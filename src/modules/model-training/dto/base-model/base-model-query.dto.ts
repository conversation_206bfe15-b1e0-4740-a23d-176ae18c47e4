import { QueryDto, SortDirection } from "@common/dto";
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { BaseModelSortByEnum } from '../../constants/base-model-sort-by.enum';

/**
 * Lớp mở rộng QueryDto cho việc truy vấn base model
 * Lưu ý: Đã loại bỏ trường status vì API chỉ trả về các model có trạng thái APPROVED
 */
export class BaseModelQueryDto extends QueryDto {
    /**
     * Trường cần sắp xếp
     */
    @ApiProperty({
        description: 'Trường cần sắp xếp',
        enum: BaseModelSortByEnum,
        example: BaseModelSortByEnum.CREATED_AT,
        default: BaseModelSortByEnum.CREATED_AT,
        required: false,
    })
    @IsOptional()
    @IsEnum(BaseModelSortByEnum)
    sortBy?: BaseModelSortByEnum = BaseModelSortByEnum.CREATED_AT;

    /**
     * Hướng sắp xếp
     */
    @ApiProperty({
        description: 'Hướng sắp xếp',
        enum: SortDirection,
        example: SortDirection.DESC,
        default: SortDirection.DESC,
        required: false,
    })
    @IsOptional()
    @IsEnum(SortDirection)
    sortDirection?: SortDirection = SortDirection.DESC;
}
