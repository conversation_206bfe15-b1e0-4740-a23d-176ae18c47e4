import { ApiProperty } from '@nestjs/swagger';
import { DataFineTuning } from '../entities/data-fine-tuning.entity';

export class DataFineTuningSchema {
  @ApiProperty({
    description: 'UUID định danh duy nhất cho bộ dữ liệu fine-tuning',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Dữ liệu huấn luyện',
    example: [
      {
        messages: [
          { role: "system", content: "Bạn là một trợ lý tài chính cá nhân." },
          { role: "user", content: "Tôi nên đầu tư vào đâu với 50 triệu đồng?" },
          { role: "assistant", content: "Với 50 triệu đồng, bạn có thể cân nhắc các phương án sau: 1. <PERSON><PERSON><PERSON> tiết kiệm ngân hàng với lãi suất 5-6%/năm. 2. <PERSON><PERSON><PERSON> tư vào quỹ mở với mức sinh lời kỳ vọng 8-12%/năm. 3. Mua trái phiếu doanh nghiệp với lãi suất 8-10%/năm. Tùy vào mức độ chấp nhận rủi ro và thời gian đầu tư của bạn, mỗi phương án sẽ có ưu và nhược điểm khác nhau." }
        ]
      }
    ],
    nullable: true,
  })
  trainData: any;

  @ApiProperty({
    description: 'Dữ liệu kiểm thử',
    example: [
      {
        messages: [
          { role: "system", content: "Bạn là một trợ lý tài chính cá nhân." },
          { role: "user", content: "Tôi nên tiết kiệm bao nhiêu mỗi tháng?" },
          { role: "assistant", content: "Theo nguyên tắc tài chính cá nhân, bạn nên tiết kiệm ít nhất 20% thu nhập hàng tháng. Ví dụ, nếu thu nhập của bạn là 15 triệu đồng/tháng, bạn nên tiết kiệm ít nhất 3 triệu đồng. Tuy nhiên, tỷ lệ này có thể điều chỉnh tùy theo mục tiêu tài chính và tình hình cá nhân của bạn." }
        ]
      }
    ],
    nullable: true,
  })
  validationData: any;

  @ApiProperty({
    description: 'Trạng thái kích hoạt của bộ dữ liệu',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Loại người sở hữu dữ liệu',
    example: 'user',
    enum: ['user', 'organization', 'system'],
    nullable: true,
  })
  ownerType: string;

  @ApiProperty({
    description: 'ID của người sở hữu dữ liệu',
    example: 1,
    nullable: true,
  })
  ownerBy: number;

  @ApiProperty({
    description: 'Thời điểm tạo bộ dữ liệu (timestamp millis)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật bộ dữ liệu gần nhất (timestamp millis)',
    example: 1625097600000,
  })
  updatedAt: number;

  constructor(partial: Partial<DataFineTuning>) {
    Object.assign(this, partial);
  }
}

export class DataFineTuningListResponseSchema {
  @ApiProperty({
    description: 'Danh sách bộ dữ liệu fine-tuning',
    type: [DataFineTuningSchema],
  })
  items: DataFineTuningSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số bộ dữ liệu',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số bộ dữ liệu trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số bộ dữ liệu trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
