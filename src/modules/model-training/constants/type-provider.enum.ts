import { AppException, ErrorCode } from "@/common";

/**
 * Enum đại diện cho các nhà cung cấp AI được hỗ trợ
 * Sử dụng trong bảng providers_model, cột type
 */
export enum TypeProviderEnum {
    OPENAI = 'OPENAI',
    ANTHROPIC = 'ANTHROPIC',
    GOOGLE = 'GOOGLE',
    META = 'META',
    DEEPSEEK = 'DEEPSEEK',
    XAI = 'XAI',
}


/**
 * Object tiện ích để làm việc với FileTypeEnum
 */
export const TypeProviderUtil = {
  /**
   * Lấy giá trị chuỗi của một loại file
   * @param type Loại file
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: TypeProviderEnum): string {
    return type;
  },

  /**
   * Lấy enum FileTypeEnum từ tên loại file hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type (ví dụ: 'application/pdf')
   * @returns Giá trị enum FileTypeEnum tương ứng
   * @throws AppException nếu loại file không tồn tại
   */
  getMimeType(type: string): TypeProviderEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'PDF')
    const typeFromKey = TypeProviderEnum[type as keyof typeof TypeProviderEnum];
    if (typeFromKey) {
      return typeFromKey;
    }

    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'application/pdf')
    const entries = Object.entries(TypeProviderEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return TypeProviderEnum[entry[0] as keyof typeof TypeProviderEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.NOT_FOUND,
      `Loại nhà cung cấp AI '${type}' không được hỗ trợ`
    );
  },
};