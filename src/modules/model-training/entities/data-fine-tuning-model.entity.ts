import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng data_fine_tuning_models trong cơ sở dữ liệu
 * Bảng lưu trữ chi tiết kỹ thuật của mô hình fine-tuning
 */
@Entity('data_fine_tuning_models')
export class DataFineTuningModel {
  /**
   * UUID định danh duy nhất cho dữ liệu chi tiết của mô hình fine-tuning
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Đường dẫn hoặc ID file dữ liệu huấn luyện
   */
  @Column({ name: 'train_file', length: 100, nullable: true })
  trainFile: string;

  /**
   * Đường dẫn hoặc ID file dữ liệu kiểm thử
   */
  @Column({ name: 'validation_file', length: 100, nullable: true })
  validationFile: string;

  /**
   * Phương pháp fine-tuning được sử dụng (dạng JSONB)
   */
  @Column({ name: 'method', type: 'jsonb', nullable: true })
  method: any;

  /**
   * Thông tin bổ sung về quá trình fine-tuning (dạng JSONB)
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: any;
}
