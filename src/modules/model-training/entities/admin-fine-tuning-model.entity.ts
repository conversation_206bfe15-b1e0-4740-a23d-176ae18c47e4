import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_fine_tuning_models trong cơ sở dữ liệu
 * Quản lý các mô hình fine-tune được tạo bởi admin, bao gồm thông tin người tạo, c<PERSON><PERSON> nh<PERSON>, x<PERSON><PERSON>, mô hình gốc và quyền riêng tư
 */
@Entity('admin_fine_tuning_models')
export class AdminFineTuningModel {
  /**
   * ID mô hình fine-tune, tham chiếu đến bảng fine_tuning_models
   */
  @PrimaryColumn({ name: 'id', length: 100 })
  id: string;

  /**
   * ID của nhân viên tạo mô hình (tham chiếu bảng employees)
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật mô hình (tham chiếu bảng employees)
   */
  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  /**
   * ID của nhân viên đã xóa mô hình (tham chiếu bảng employees)
   */
  @Column({ name: 'deleted_by', nullable: true })
  deletedBy: number;

  /**
   * ID mô hình gốc được sử dụng để fine-tune (tham chiếu bảng base_models)
   */
  @Column('uuid', { name: 'model_base_id' })
  modelBaseId: string;

  /**
   * Cờ đánh dấu mô hình có ở chế độ riêng tư hay không. Mặc định là true
   */
  @Column({ name: 'is_private', type: 'boolean', default: true })
  isPrivate: boolean;
}
