import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { DataFineTunningStatusEnum } from '../constants/data-fine-tunning.enum';
import { TrainingDataset } from '../interfaces';

/**
 * Entity đại diện cho bảng data_fine_tuning trong cơ sở dữ liệu
 * Bảng lưu trữ dữ liệu dùng cho quá trình fine-tuning các mô hình
 */
@Entity('data_fine_tuning')
export class DataFineTuning {
  /**
   * UUID định danh duy nhất cho bộ dữ liệu fine-tuning
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Dữ liệu huấn luyện dạng JSONB
   */
  @Column({ name: 'train_data', type: 'jsonb', default: '{}' })
  trainData: TrainingDataset;

  /**
   * Dữ liệu kiểm thử dạng JSONB
   */
  @Column({ name: 'validation_data', type: 'jsonb', nullable: true })
  validationData: TrainingDataset | null;

  /**
   * Thời điểm tạo bộ dữ liệu (timestamp millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật bộ dữ liệu gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Cờ đánh dấu bộ dữ liệu có được rao bán hay không
   */
  @Column({ name: 'is_for_sale', type: 'boolean', default: false })
  isForSale: boolean;

  /**
   * Tên của bộ dữ liệu fine-tuning
   */
  @Column({ name: 'name', type: 'varchar', length: 100, nullable: true })
  name: string | null;

  /**
   * Trạng thái của bộ dữ liệu fine-tuning
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: DataFineTunningStatusEnum,
    default: DataFineTunningStatusEnum.DRAFT,
  })
  status: DataFineTunningStatusEnum;

  /**
   * Mô tả chi tiết về bộ dữ liệu fine-tuning
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Thời điểm xóa bộ dữ liệu (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
