import { EmployeeModule } from '@modules/employee/employee.module';
import { BaseModelUserService } from "@modules/model-training/user/services";
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AdminDataFineTuning,
  AdminFineTuningModel,
  AdminProviderModel,
  BaseModel,
  DataFineTuning,
  DataFineTuningModel,
  FineTuningModel,
  UserDataFineTuning,
  UserFineTuningModel,
  UserProviderModel
} from '../entities';
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';
import { BaseModelValidationHelper } from '../helpers/base-model-validation.helper';
import { DataFineTuningValidationHelper } from '../helpers/data-fine-tuning-validation.helper';
import { FineTuningModelIdValidatorHelper } from '../helpers/fine-tuning-model-id-validator.helper';
import { FineTuningModelValidationHelper } from '../helpers/fine-tuning-model-validation.helper';
import {
  AdminDataFineTuningRepository,
  AdminFineTuningModelRepository,
  AdminProviderModelRepository,
  BaseModelRepository,
  DataFineTuningModelRepository,
  DataFineTuningRepository,
  FineTuningModelRepository,
  UserDataFineTuningRepository,
  UserFineTuningModelRepository,
  UserProviderModelRepository
} from '../repositories';
import {
  AdminDataFineTuningController,
  AdminProviderModelController,
  BaseModelAdminController,
  BaseModelTrashAdminController,
  FineTuningModelAdminController
} from './controllers';
import {
  AdminProviderModelService,
  BaseModelAdminService,
  FineTuningModelAdminService
} from './services';
import { AdminDataFineTuningService } from './services/admin-data-fine-tuning.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BaseModel,
      FineTuningModel,
      DataFineTuningModel,
      DataFineTuning,
      AdminProviderModel,
      UserProviderModel,
      AdminDataFineTuning,
      UserDataFineTuning,
      AdminFineTuningModel,
      UserFineTuningModel
    ]),
    EmployeeModule,
    ConfigModule
  ],
  controllers: [
    BaseModelTrashAdminController, // Đặt controller xử lý trash lên đầu để ưu tiên route cụ thể hơn
    BaseModelAdminController,
    FineTuningModelAdminController,
    AdminProviderModelController,
    AdminDataFineTuningController
  ],
  providers: [
    BaseModelAdminService,
    FineTuningModelAdminService,
    AdminProviderModelService,
    AdminDataFineTuningService,
    BaseModelRepository,
    FineTuningModelRepository,
    DataFineTuningModelRepository,
    DataFineTuningRepository,
    AdminProviderModelRepository,
    UserProviderModelRepository,
    AdminDataFineTuningRepository,
    UserDataFineTuningRepository,
    AdminFineTuningModelRepository,
    UserFineTuningModelRepository,
    BaseModelUserService,
    BaseModelValidationHelper,
    FineTuningModelValidationHelper,
    DataFineTuningValidationHelper,
    ApiKeyEncryptionHelper,
    FineTuningModelIdValidatorHelper,
  ],
  exports: [
    BaseModelAdminService,
    FineTuningModelAdminService,
    AdminProviderModelService,
    AdminDataFineTuningService
  ],
})
export class ModelTrainingAdminModule { }
