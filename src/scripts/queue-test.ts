/**
 * <PERSON>ript để test Redis Queue
 * Chạy script bằng lệnh: npx ts-node src/scripts/queue-test.ts
 */
import { NestFactory } from '@nestjs/core';
import { QueueName } from '../shared/queue/queue.constants';
import { QueueService } from '../shared/queue/queue.service';
import { ConfigModule } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';

// Tạo module tạm thời cho script test
@Module({
  imports: [
    ConfigModule.forRoot(),
    BullModule.forRoot({
      redis: process.env.REDIS_URL,
    }),
    BullModule.registerQueue(
      { name: QueueName.EMAIL },
      { name: QueueName.SMS },
      { name: QueueName.NOTIFICATION },
      { name: QueueName.DATA_PROCESS }
    ),
  ],
  providers: [QueueService],
})
class QueueTestModule {}

// Hàm thực thi chính
async function bootstrap() {
  try {
    // Khởi tạo ứng dụng Nest
    const app = await NestFactory.createApplicationContext(QueueTestModule);
    
    // Lấy Queue Service
    const queueService = app.get(QueueService);
    
    console.log('=== QUEUE TEST SCRIPT ===');
    console.log('Đã kết nối tới Redis Queue');
    
    // Tạo một job email đơn giản
    const emailData = {
      to: '<EMAIL>',
      subject: 'Test Email from Script',
      content: 'This is a test email sent via Redis Queue',
      timestamp: Date.now(),
    };
    
    console.log('Đang thêm job email vào queue...');
    const emailJobId = await queueService.addEmailJob(emailData);
    console.log(`Job email đã được thêm vào queue với ID: ${emailJobId}`);
    
    // Tạo một job email mẫu 
    const templateData = {
      to: '<EMAIL>',
      templateId: 'welcome-template',
      data: {
        name: 'New User',
        activationCode: '12345',
        activationLink: 'https://example.com/activate',
      },
      timestamp: Date.now(),
    };
    
    console.log('Đang thêm job email mẫu vào queue...');
    const templateJobId = await queueService.addTemplateEmailJob(templateData);
    console.log(`Job email mẫu đã được thêm vào queue với ID: ${templateJobId}`);
    
    // Kiểm tra trạng thái của job email
    console.log('Kiểm tra trạng thái job email...');
    const emailStatus = await queueService.getJobStatus(QueueName.EMAIL, emailJobId);
    console.log(`Trạng thái job email: ${emailStatus.state}`);
    
    // Kiểm tra trạng thái của job email mẫu
    console.log('Kiểm tra trạng thái job email mẫu...');
    const templateStatus = await queueService.getJobStatus(QueueName.EMAIL, templateJobId);
    console.log(`Trạng thái job email mẫu: ${templateStatus.state}`);
    
    console.log('Test hoàn tất!');
    
    // Đóng ứng dụng
    await app.close();
    process.exit(0);
  } catch (error) {
    console.error('Lỗi khi test queue:', error);
    process.exit(1);
  }
}

// Chạy script
bootstrap(); 